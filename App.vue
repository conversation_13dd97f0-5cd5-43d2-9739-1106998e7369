<script>
	import userService from './services/userService.js';

	export default {
		async onLaunch() {
			// 初始化用户服务
			try {
				await userService.init();
			} catch (error) {
				console.error('用户服务初始化失败:', error);
			}

			// 初始化用户信息（不检查登录状态）
			this.initUserInfo();

			// 同步会员状态
			this.syncMemberStatus();
		},
		onShow: function() {
			// 应用从后台切换到前台时，同步会员状态
			this.syncMemberStatus();
		},
		onHide: function() {
		},
		methods: {
			// 同步会员状态
			async syncMemberStatus() {
				try {
					// 静默同步，不显示loading
					await userService.syncMemberStatus();
				} catch (error) {
					console.error('会员状态同步失败:', error);
				}
			},

			initUserInfo() {
				// 检查是否有保存的用户信息
				let userInfo = uni.getStorageSync('userInfo');
				if (!userInfo) {
					// 设置默认用户信息
					const defaultUserInfo = {
						avatar: '/static/default-avatar.png',
						name: '请设置姓名',
						position: '请设置职位',
						company: '请设置公司',
						phone: '',
						email: '',
						wechat: '',
						address: '',
						description: ''
					};
					uni.setStorageSync('userInfo', defaultUserInfo);
					userInfo = defaultUserInfo;
				}

				// 确保用户信息完整性
				const requiredFields = ['avatar', 'name', 'position', 'company', 'phone', 'email', 'wechat', 'address', 'description'];
				let needUpdate = false;

				requiredFields.forEach(field => {
					if (userInfo[field] === undefined) {
						userInfo[field] = field === 'avatar' ? '/static/default-avatar.png' :
										 field === 'name' ? '请设置姓名' :
										 field === 'position' ? '请设置职位' :
										 field === 'company' ? '请设置公司' : '';
						needUpdate = true;
					}
				});

				if (needUpdate) {
					uni.setStorageSync('userInfo', userInfo);
				}
			}
		}
	}
</script>

<style>
	/* 全局样式 */
	page {
		background-color: #f5f5f5;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
	}

	/* 通用按钮样式 */
	.btn {
		display: flex;
		align-items: center;
		justify-content: center;
		border: none;
		border-radius: 8rpx;
		font-size: 28rpx;
		transition: all 0.3s ease;
	}

	.btn:active {
		transform: scale(0.98);
		opacity: 0.8;
	}

	/* 主色调按钮 */
	.btn-primary {
		background-color: #4A90E2;
		color: white;
	}

	/* 次要按钮 */
	.btn-secondary {
		background-color: #f8f9fa;
		color: #333;
		border: 1rpx solid #e0e0e0;
	}

	/* 卡片样式 */
	.card {
		background: white;
		border-radius: 15rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	/* 文本省略 */
	.text-ellipsis {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.text-ellipsis-2 {
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	/* 居中布局 */
	.flex-center {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.flex-between {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	/* 间距工具类 */
	.mt-10 { margin-top: 10rpx; }
	.mt-20 { margin-top: 20rpx; }
	.mt-30 { margin-top: 30rpx; }
	.mb-10 { margin-bottom: 10rpx; }
	.mb-20 { margin-bottom: 20rpx; }
	.mb-30 { margin-bottom: 30rpx; }
	.ml-10 { margin-left: 10rpx; }
	.ml-20 { margin-left: 20rpx; }
	.mr-10 { margin-right: 10rpx; }
	.mr-20 { margin-right: 20rpx; }

	.p-10 { padding: 10rpx; }
	.p-20 { padding: 20rpx; }
	.p-30 { padding: 30rpx; }

	/* 字体大小 */
	.text-xs { font-size: 20rpx; }
	.text-sm { font-size: 24rpx; }
	.text-base { font-size: 28rpx; }
	.text-lg { font-size: 32rpx; }
	.text-xl { font-size: 36rpx; }
	.text-2xl { font-size: 40rpx; }

	/* 字体颜色 */
	.text-primary { color: #4A90E2; }
	.text-secondary { color: #666; }
	.text-muted { color: #999; }
	.text-danger { color: #e74c3c; }
	.text-success { color: #27ae60; }

	/* 字体粗细 */
	.font-bold { font-weight: bold; }
	.font-normal { font-weight: normal; }
</style>
