<?php
declare(strict_types=1);

namespace app\api\controller;

use think\Request;
use app\common\model\BusinessUser;
use app\common\model\CardStats;
use app\common\service\WechatService;

/**
 * 用户API控制器
 */
class User
{
    /**
     * 读取用户详细信息（userinfo接口）
     */
    public function read(Request $request)
    {
        try {
            $user = $this->getCurrentUser($request);
            if (!$user) {
                return json(['code' => 401, 'message' => '请先登录']);
            }

            // 直接查询数据库获取完整数据（避免模型字段限制）
            try {
                $pdo = new \PDO("mysql:host=127.0.0.1;dbname=www_server_com;charset=utf8mb4", "www_server_com", "www_server_com");
                $sql = "SELECT * FROM bc_users WHERE id = ?";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([$user->id]);
                $userData = $stmt->fetch(\PDO::FETCH_ASSOC);

                if (!$userData) {
                    throw new \Exception('用户数据不存在');
                }
            } catch (\Exception $e) {
                // 如果直接查询失败，回退到模型方式
                $userData = $user->toArray();
            }

            // 处理头像URL
            if (!empty($userData['avatar']) && !str_starts_with($userData['avatar'], 'http')) {
                $userData['avatar'] = $request->domain() . $userData['avatar'];
            }

            // 解码JSON字段
            $jsonFields = ['tags', 'achievements', 'advantages'];
            foreach ($jsonFields as $field) {
                if (!empty($userData[$field])) {
                    $decoded = json_decode($userData[$field], true);
                    $userData[$field] = is_array($decoded) ? $decoded : [];
                } else {
                    $userData[$field] = [];
                }
            }

            // 构建个性定制数据
            $userData['customStyle'] = [
                'backgroundType' => $userData['background_type'] ?? 'color',
                'backgroundColor' => $userData['background_color'] ?? '#ffffff',
                'backgroundImage' => $userData['background_image'] ?? '',
                'gradientIndex' => (int)($userData['gradient_index'] ?? 0),
                'textColor' => $userData['text_color'] ?? '#333333',
                'borderRadiusIndex' => (int)($userData['border_radius_index'] ?? 1),
                'cardStyleIndex' => (int)($userData['card_style_index'] ?? 0)
            ];

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $userData
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 保存用户信息（userinfo接口）
     */
    public function save(Request $request)
    {
        try {
            $user = $this->getCurrentUser($request);
            if (!$user) {
                return json(['code' => 401, 'message' => '请先登录']);
            }

            $data = $request->post();



            // 准备更新数据
            $updateData = [];
            $allowedFields = [
                'name', 'phone', 'email', 'wechat', 'position', 'company', 'address', 'description', 'avatar',
                // 个人介绍相关字段
                'tags', 'achievements', 'education',
                'showProfileSection', 'showDescription', 'showTags', 'showAchievements', 'showEducation',
                // 企业介绍相关字段
                'companyName', 'companyDesc', 'companySlogan', 'advantages', 'companyAddress',
                'showCompanySection', 'showCompanyName', 'showCompanyDesc', 'showCompanySlogan',
                'showCompanyAdvantages', 'showCompanyAddress'
            ];
            $customStyleFields = ['background_type', 'background_color', 'background_image', 'gradient_index', 'text_color', 'border_radius_index', 'card_style_index'];

            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    // 对数组字段手动进行JSON编码（避免Array to string conversion错误）
                    if (in_array($field, ['tags', 'achievements', 'advantages']) && is_array($data[$field])) {
                        $updateData[$field] = json_encode($data[$field], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                    } else {
                        $updateData[$field] = $data[$field];
                    }
                }
            }

            // 处理个性定制数据
            if (isset($data['customStyle']) && is_array($data['customStyle'])) {
                $customStyle = $data['customStyle'];

                // 映射前端字段到数据库字段
                $fieldMapping = [
                    'backgroundType' => 'background_type',
                    'backgroundColor' => 'background_color',
                    'backgroundImage' => 'background_image',
                    'gradientIndex' => 'gradient_index',
                    'textColor' => 'text_color',
                    'borderRadiusIndex' => 'border_radius_index',
                    'cardStyleIndex' => 'card_style_index'
                ];

                foreach ($fieldMapping as $frontendField => $dbField) {
                    if (isset($customStyle[$frontendField])) {
                        $updateData[$dbField] = $customStyle[$frontendField];
                    }
                }
            }

            // 验证手机号格式
            if (!empty($updateData['phone']) && !preg_match('/^1[3-9]\d{9}$/', $updateData['phone'])) {
                return json(['code' => 400, 'message' => '手机号格式不正确']);
            }

            // 验证邮箱格式
            if (!empty($updateData['email']) && !filter_var($updateData['email'], FILTER_VALIDATE_EMAIL)) {
                return json(['code' => 400, 'message' => '邮箱格式不正确']);
            }

            // 更新用户信息
            if (!empty($updateData)) {
                $result = $user->save($updateData);
            }

            return json([
                'code' => 200,
                'message' => '保存成功',
                'data' => $user->toArray()
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 查询用户列表（用于验证注册是否成功）
     */
    public function list()
    {
        try {
            $users = BusinessUser::order('create_time', 'desc')->limit(10)->select();

            $userList = [];
            foreach ($users as $user) {
                $userList[] = [
                    'id' => $user->id,
                    'phone' => $user->phone,
                    'name' => $user->name,
                    'member_level' => $user->member_level,
                    'status' => $user->status,
                    'password_hash' => substr($user->password, 0, 20) . '...', // 只显示前20个字符
                    'create_time' => $user->create_time
                ];
            }

            return json([
                'code' => 200,
                'message' => '查询成功',
                'data' => [
                    'total' => count($userList),
                    'users' => $userList
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '查询失败：' . $e->getMessage()]);
        }
    }

    /**
     * 用户注册
     */
    public function register(Request $request)
    {
        try {
            // 简化版本：直接获取POST数据
            $data = $request->post();
            error_log('注册请求数据: ' . json_encode($data));

            // 验证必填字段
            if (empty($data['phone']) || empty($data['password']) || empty($data['name'])) {
                error_log('必填字段验证失败');
                return json(['code' => 400, 'message' => '手机号、密码和姓名不能为空']);
            }

            // 验证手机号格式
            if (!preg_match('/^1[3-9]\d{9}$/', $data['phone'])) {
                return json(['code' => 400, 'message' => '手机号格式不正确']);
            }

            // 检查手机号是否已存在
            $existingUser = BusinessUser::where('phone', $data['phone'])->find();
            if ($existingUser) {
                return json(['code' => 400, 'message' => '手机号已被注册']);
            }

            // 创建用户（密码会在模型的setPasswordAttr中自动加密）
            $userData = [
                'phone' => $data['phone'],
                'password' => $data['password'], // 不在这里加密，让模型自动处理
                'name' => $data['name'],
                'position' => $data['position'] ?? '',
                'company' => $data['company'] ?? '',
                'email' => $data['email'] ?? '',
                'description' => $data['description'] ?? '',
                'status' => 1,
                'member_level' => 0
            ];

            $user = BusinessUser::create($userData);

            if (!$user) {
                return json(['code' => 500, 'message' => '用户创建失败']);
            }

            // 创建统计记录
            CardStats::create([
                'user_id' => $user->id,
                'view_count' => 0,
                'share_count' => 0,
                'collect_count' => 0
            ]);

            // 生成 token
            $token = $this->generateToken($user, $request);

            // 返回成功结果
            return json([
                'code' => 200,
                'message' => '注册成功',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'phone' => $user->phone,
                        'name' => $user->name,
                        'position' => $user->position,
                        'company' => $user->company,
                        'email' => $user->email,
                        'avatar' => '/static/default-avatar.png',
                        'description' => $user->description,
                        'member_level' => $user->member_level,
                        'status' => $user->status
                    ],
                    'token' => $token
                ]
            ]);

        } catch (\Exception $e) {
            error_log('注册失败: ' . $e->getMessage() . ' 文件: ' . $e->getFile() . ' 行号: ' . $e->getLine());
            return json(['code' => 500, 'message' => '注册失败：' . $e->getMessage()]);
        }
    }



    /**
     * 用户登录
     */
    public function login(Request $request)
    {
        $data = $request->post();

        if (empty($data['phone']) || empty($data['password'])) {
            return json(['code' => 400, 'message' => '手机号和密码不能为空']);
        }

        // 查找用户
        $user = BusinessUser::where('phone', $data['phone'])->find();

        if (!$user) {
            return json(['code' => 400, 'message' => '用户不存在']);
        }

        if ($user->status != BusinessUser::STATUS_ENABLED) {
            return json(['code' => 400, 'message' => '账号已被禁用']);
        }

        // 验证密码
        if (!$user->checkPassword($data['password'])) {
            return json(['code' => 400, 'message' => '密码错误']);
        }

        // 生成JWT Token
        $token = $this->generateToken($user, $request);

        return json([
            'code' => 200,
            'message' => '登录成功',
            'data' => [
                'user' => $user->hidden(['password']),
                'token' => $token
            ]
        ]);
    }

    /**
     * 获取用户信息
     */
    public function info(Request $request)
    {
        $user = $this->getCurrentUser($request);
        if (!$user) {
            return json(['code' => 401, 'message' => '请先登录']);
        }

        // 获取统计数据
        $stats = CardStats::where('user_id', $user->id)->find();

        // 获取会员状态信息
        $isMember = $user->isMember();
        $memberInfo = [
            'is_member' => $isMember,
            'member_level' => $user->member_level,
            'member_expire_time' => $user->member_expire_time,
            'days_left' => $isMember ? max(0, ceil((strtotime($user->member_expire_time) - time()) / 86400)) : 0
        ];



        return json([
            'code' => 200,
            'message' => '获取成功',
            'data' => [
                'user' => $user->hidden(['password']),
                'member' => $memberInfo,
                'stats' => $stats ?: ['view_count' => 0, 'share_count' => 0, 'collect_count' => 0]
            ]
        ]);
    }

    /**
     * 更新用户信息
     */
    public function update(Request $request)
    {
        $user = $this->getCurrentUser($request);
        if (!$user) {
            return json(['code' => 401, 'message' => '请先登录']);
        }

        $data = $request->post();

        try {
            // 允许更新的字段
            $allowFields = ['name', 'position', 'company', 'email', 'wechat', 'address', 'description', 'avatar'];
            $updateData = [];

            foreach ($allowFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }

            // 验证手机号格式（如果提供）
            if (isset($data['phone']) && !empty($data['phone'])) {
                if (!preg_match('/^1[3-9]\d{9}$/', $data['phone'])) {
                    return json(['code' => 400, 'message' => '手机号格式不正确']);
                }

                // 检查手机号是否重复
                $exists = BusinessUser::where('phone', $data['phone'])
                    ->where('id', '<>', $user->id)
                    ->count();
                if ($exists > 0) {
                    return json(['code' => 400, 'message' => '手机号已被其他用户使用']);
                }

                $updateData['phone'] = $data['phone'];
            }

            // 验证邮箱格式（如果提供）
            if (isset($updateData['email']) && !empty($updateData['email'])) {
                if (!filter_var($updateData['email'], FILTER_VALIDATE_EMAIL)) {
                    return json(['code' => 400, 'message' => '邮箱格式不正确']);
                }
            }

            $user->save($updateData);

            return json([
                'code' => 200,
                'message' => '更新成功',
                'data' => $user->hidden(['password'])
            ]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '更新失败：' . $e->getMessage()]);
        }
    }



    /**
     * 修改密码
     */
    public function changePassword(Request $request)
    {
        $user = $this->getCurrentUser($request);
        if (!$user) {
            return json(['code' => 401, 'message' => '请先登录']);
        }
        
        $data = $request->post();
        
        if (empty($data['old_password']) || empty($data['new_password'])) {
            return json(['code' => 400, 'message' => '旧密码和新密码不能为空']);
        }
        
        // 验证旧密码
        if (!$user->checkPassword($data['old_password'])) {
            return json(['code' => 400, 'message' => '旧密码错误']);
        }
        
        // 验证新密码确认
        if ($data['new_password'] !== $data['confirm_password']) {
            return json(['code' => 400, 'message' => '两次新密码输入不一致']);
        }
        
        try {
            $user->password = $data['new_password'];
            $user->save();
            
            return json(['code' => 200, 'message' => '密码修改成功']);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '密码修改失败：' . $e->getMessage()]);
        }
    }

    /**
     * 微信一键登录
     */
    public function wechatLogin(Request $request)
    {
        try {
            $data = $request->post();

            if (empty($data['code'])) {
                return json(['code' => 400, 'message' => '授权码不能为空']);
            }

            // 通过code获取session_key和openid
            try {
                $wechatInfo = WechatService::code2Session($data['code']);
            } catch (\Exception $wxError) {
                // 处理微信API特定错误
                $errorMsg = $wxError->getMessage();
                $errorCode = 0;
                
                // 尝试提取错误码
                if (preg_match('/错误码:\s*(\d+)/', $errorMsg, $matches)) {
                    $errorCode = (int)$matches[1];
                }
                
                // 根据错误码提供更友好的提示
                switch ($errorCode) {
                    case 40029:
                        return json([
                            'code' => 400, 
                            'message' => '授权码无效或已过期，请重新授权',
                            'wx_error_code' => 40029
                        ]);
                    case 40226:
                        return json([
                            'code' => 400, 
                            'message' => '登录请求过于频繁，请稍后再试',
                            'wx_error_code' => 40226
                        ]);
                    case 45011:
                        return json([
                            'code' => 400, 
                            'message' => 'API调用频率超限，请稍后再试',
                            'wx_error_code' => 45011
                        ]);
                    default:
                        return json([
                            'code' => 400, 
                            'message' => '微信授权失败: ' . $errorMsg,
                            'wx_error_code' => $errorCode
                        ]);
                }
            }

            if (!isset($wechatInfo['openid'])) {
                return json(['code' => 400, 'message' => '获取微信信息失败']);
            }

            // 查找是否已有微信用户记录
            $user = BusinessUser::findByOpenid($wechatInfo['openid']);

            if ($user) {
                // 已有记录，直接登录
                if ($user->status != BusinessUser::STATUS_ENABLED) {
                    return json(['code' => 400, 'message' => '账号已被禁用']);
                }

                // 更新session_key
                $user->session_key = $wechatInfo['session_key'] ?? '';
                $user->save();

                // 生成token
                $token = $this->generateToken($user, $request);

                return json([
                    'code' => 200,
                    'message' => '登录成功',
                    'data' => [
                        'user' => $user->hidden(['password']),
                        'token' => $token,
                        'is_new_user' => false
                    ]
                ]);
            } else {
                // 新用户，需要创建账号
                $defaultName = '微信用户' . substr($wechatInfo['openid'], -6);

                // 创建用户
                $userData = [
                    'phone' => '', // 微信登录暂时不需要手机号
                    'password' => '', // 微信登录不需要密码
                    'openid' => $wechatInfo['openid'],
                    'unionid' => $wechatInfo['unionid'] ?? '',
                    'session_key' => $wechatInfo['session_key'] ?? '',
                    'name' => $defaultName,
                    'position' => '请设置职位',
                    'company' => '请设置公司',
                    'status' => BusinessUser::STATUS_ENABLED,
                    'member_level' => 0
                ];

                $user = BusinessUser::create($userData);

                if (!$user) {
                    return json(['code' => 500, 'message' => '用户创建失败']);
                }

                // 创建统计记录
                CardStats::create([
                    'user_id' => $user->id,
                    'view_count' => 0,
                    'share_count' => 0,
                    'collect_count' => 0
                ]);

                // 生成token
                $token = $this->generateToken($user, $request);

                return json([
                    'code' => 200,
                    'message' => '注册成功',
                    'data' => [
                        'user' => $user->hidden(['password']),
                        'token' => $token,
                        'is_new_user' => true
                    ]
                ]);
            }

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '微信登录失败：' . $e->getMessage()]);
        }
    }

    /**
     * 微信获取手机号
     */
    public function wechatGetPhone(Request $request)
    {
        try {
            $data = $request->post();

            if (empty($data['code'])) {
                return json(['code' => 400, 'message' => '授权码不能为空']);
            }

            // 获取当前用户
            $user = $this->getCurrentUser($request);
            if (!$user) {
                return json(['code' => 401, 'message' => '请先登录']);
            }

            // 检查是否为微信用户
            if (!$user->isWechatUser()) {
                return json(['code' => 400, 'message' => '当前用户不是微信用户']);
            }

            // 通过code获取手机号
            $phoneInfo = WechatService::getPhoneNumber($data['code']);

            if (!isset($phoneInfo['phone_info']['phoneNumber'])) {
                return json(['code' => 400, 'message' => '获取手机号失败']);
            }

            $phone = $phoneInfo['phone_info']['phoneNumber'];

            // 检查手机号是否已被其他用户使用
            $existingUser = BusinessUser::where('phone', $phone)
                                      ->where('id', '<>', $user->id)
                                      ->find();

            if ($existingUser) {
                return json(['code' => 400, 'message' => '该手机号已被其他用户使用']);
            }

            // 更新用户手机号
            $user->phone = $phone;
            $user->save();

            return json([
                'code' => 200,
                'message' => '手机号绑定成功',
                'data' => [
                    'phone' => $phone
                ]
            ]);

        } catch (\Exception $e) {
            error_log('获取微信手机号失败: ' . $e->getMessage());
            return json(['code' => 500, 'message' => '获取手机号失败：' . $e->getMessage()]);
        }
    }

    /**
     * 微信手机号一键登录
     */
    public function wechatPhoneLogin(Request $request)
    {
        try {
            $data = $request->post();

            if (empty($data['login_code'])) {
                return json(['code' => 400, 'message' => '登录授权码不能为空']);
            }

            if (empty($data['phone_code'])) {
                return json(['code' => 400, 'message' => '手机号授权码不能为空']);
            }

            // 通过code获取session_key和openid
            try {
                $wechatInfo = WechatService::code2Session($data['login_code']);
            } catch (\Exception $wxError) {
                // 处理微信API特定错误
                $errorMsg = $wxError->getMessage();
                $errorCode = 0;
                
                // 尝试提取错误码
                if (preg_match('/错误码:\s*(\d+)/', $errorMsg, $matches)) {
                    $errorCode = (int)$matches[1];
                }
                
                // 根据错误码提供更友好的提示
                switch ($errorCode) {
                    case 40029:
                        return json([
                            'code' => 400, 
                            'message' => '登录授权码无效或已过期，请重新授权',
                            'wx_error_code' => 40029
                        ]);
                    case 40226:
                        return json([
                            'code' => 400, 
                            'message' => '登录请求过于频繁，请稍后再试',
                            'wx_error_code' => 40226
                        ]);
                    default:
                        return json([
                            'code' => 400, 
                            'message' => '微信授权失败: ' . $errorMsg,
                            'wx_error_code' => $errorCode
                        ]);
                }
            }

            if (!isset($wechatInfo['openid'])) {
                return json(['code' => 400, 'message' => '获取微信信息失败']);
            }

            // 获取手机号
            try {
                $phoneInfo = WechatService::getPhoneNumber($data['phone_code']);
                $phone = $phoneInfo['phone_info']['phoneNumber'] ?? '';
            } catch (\Exception $wxError) {
                $errorMsg = $wxError->getMessage();
                $errorCode = 0;
                
                // 尝试提取错误码
                if (preg_match('/错误码:\s*(\d+)/', $errorMsg, $matches)) {
                    $errorCode = (int)$matches[1];
                }
                
                return json([
                    'code' => 400, 
                    'message' => '获取手机号失败: ' . $errorMsg,
                    'wx_error_code' => $errorCode
                ]);
            }

            if (empty($phone)) {
                return json(['code' => 400, 'message' => '获取手机号失败']);
            }

            // 查找是否已有微信用户记录
            $user = BusinessUser::findByOpenid($wechatInfo['openid']);

            if ($user) {
                // 已有记录，直接登录
                if ($user->status != BusinessUser::STATUS_ENABLED) {
                    return json(['code' => 400, 'message' => '账号已被禁用']);
                }

                // 更新session_key和手机号
                $user->session_key = $wechatInfo['session_key'] ?? '';
                if (empty($user->phone)) {
                    $user->phone = $phone;
                }
                $user->save();

                // 生成token
                $token = $this->generateToken($user, $request);

                return json([
                    'code' => 200,
                    'message' => '登录成功',
                    'data' => [
                        'user' => $user->hidden(['password']),
                        'token' => $token,
                        'is_new_user' => false
                    ]
                ]);
            } else {
                // 新用户，需要创建账号
                $defaultName = '微信用户' . substr($wechatInfo['openid'], -6);

                // 创建用户
                $userData = [
                    'phone' => $phone,
                    'password' => '', // 微信登录不需要密码
                    'openid' => $wechatInfo['openid'],
                    'unionid' => $wechatInfo['unionid'] ?? '',
                    'session_key' => $wechatInfo['session_key'] ?? '',
                    'name' => $defaultName,
                    'position' => '请设置职位',
                    'company' => '请设置公司',
                    'status' => BusinessUser::STATUS_ENABLED,
                    'member_level' => 0
                ];

                $user = BusinessUser::create($userData);

                if (!$user) {
                    return json(['code' => 500, 'message' => '用户创建失败']);
                }

                // 创建统计记录
                CardStats::create([
                    'user_id' => $user->id,
                    'view_count' => 0,
                    'share_count' => 0,
                    'collect_count' => 0
                ]);

                // 生成token
                $token = $this->generateToken($user, $request);

                return json([
                    'code' => 200,
                    'message' => '注册成功',
                    'data' => [
                        'user' => $user->hidden(['password']),
                        'token' => $token,
                        'is_new_user' => true
                    ]
                ]);
            }

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '微信登录失败：' . $e->getMessage()]);
        }
    }

    /**
     * 刷新Token
     */
    public function refreshToken(Request $request)
    {
        $user = $this->getCurrentUser($request);
        if (!$user) {
            return json(['code' => 401, 'message' => '请先登录']);
        }

        $token = $this->generateToken($user, $request);

        return json([
            'code' => 200,
            'message' => '刷新成功',
            'data' => ['token' => $token]
        ]);
    }



    /**
     * 生成访问令牌
     */
    private function generateToken($user, $request = null)
    {
        $ipAddress = $request ? $request->ip() : null;
        return $user->generateAccessToken($ipAddress);
    }

    /**
     * 获取当前用户
     */
    private function getCurrentUser($request)
    {
        $token = $request->header('Authorization');
        if (!$token) {
            return null;
        }

        // 移除 "Bearer " 前缀
        $token = str_replace('Bearer ', '', $token);

        try {
            return BusinessUser::validateAccessToken($token);
        } catch (\Exception $e) {
            return null;
        }
    }
}
