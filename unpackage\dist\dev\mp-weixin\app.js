"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const services_userService = require("./services/userService.js");
const services_appService = require("./services/appService.js");
const utils_config = require("./utils/config.js");
if (!Math) {
  "./pages/card/card.js";
  "./pages/auth/auth.js";
  "./pages/workspace/workspace.js";
  "./pages/share/share.js";
  "./pages/card/edit.js";
  "./pages/stats/stats.js";
  "./pages/stats/ai-analysis.js";
  "./pages/card/company.js";
  "./pages/card/profile.js";
  "./pages/company/premium.js";
  "./pages/test/custom-style-test.js";
}
const _sfc_main = {
  async onLaunch() {
    try {
      await services_userService.userService.init();
    } catch (error) {
      common_vendor.index.__f__("error", "at App.vue:10", "用户服务初始化失败:", error);
    }
    this.initUserInfo();
    this.syncMemberStatus();
  },
  onShow: function() {
    this.syncMemberStatus();
  },
  onHide: function() {
  },
  methods: {
    // 同步会员状态
    async syncMemberStatus() {
      try {
        await services_userService.userService.syncMemberStatus();
      } catch (error) {
        common_vendor.index.__f__("error", "at App.vue:32", "会员状态同步失败:", error);
      }
    },
    initUserInfo() {
      let userInfo = common_vendor.index.getStorageSync("userInfo");
      if (!userInfo) {
        const defaultUserInfo = {
          avatar: "/static/default-avatar.png",
          name: "请设置姓名",
          position: "请设置职位",
          company: "请设置公司",
          phone: "",
          email: "",
          wechat: "",
          address: "",
          description: ""
        };
        common_vendor.index.setStorageSync("userInfo", defaultUserInfo);
        userInfo = defaultUserInfo;
      }
      const requiredFields = ["avatar", "name", "position", "company", "phone", "email", "wechat", "address", "description"];
      let needUpdate = false;
      requiredFields.forEach((field) => {
        if (userInfo[field] === void 0) {
          userInfo[field] = field === "avatar" ? "/static/default-avatar.png" : field === "name" ? "请设置姓名" : field === "position" ? "请设置职位" : field === "company" ? "请设置公司" : "";
          needUpdate = true;
        }
      });
      if (needUpdate) {
        common_vendor.index.setStorageSync("userInfo", userInfo);
      }
    }
  }
};
const NavBar = () => "./components/NavBar.js";
const SwitchItem = () => "./components/SwitchItem.js";
function checkApiAvailability() {
  common_vendor.index.__f__("log", "at main.js:10", "正在检查API可用性...");
  common_vendor.index.__f__("log", "at main.js:11", "API基础URL:", utils_config.API_BASE_URL);
  common_vendor.index.request({
    url: utils_config.API_BASE_URL + "/test/index",
    method: "GET",
    timeout: 5e3,
    success: (res) => {
      if (res.statusCode === 200) {
        common_vendor.index.__f__("log", "at main.js:20", "API连接测试成功!");
      } else {
        common_vendor.index.__f__("warn", "at main.js:22", "API连接测试失败! 状态码:", res.statusCode);
      }
    },
    fail: (err) => {
      common_vendor.index.__f__("error", "at main.js:26", "API连接测试失败!", err);
    }
  });
}
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  app.component("NavBar", NavBar);
  app.component("SwitchItem", SwitchItem);
  app.config.globalProperties.$app = services_appService.appService;
  app.config.globalProperties.$user = services_appService.appService.getUserService();
  app.config.globalProperties.$company = services_appService.appService.getCompanyService();
  app.config.errorHandler = function(err, vm, info) {
    if (err && err.message && (err.message.includes("注册请求") || err.message.includes("系统发生错误") || err.message.includes("服务器内部错误"))) {
      common_vendor.index.__f__("log", "at main.js:126", "注册流程中的预期错误:", err.message);
    } else {
      common_vendor.index.__f__("error", "at main.js:129", "应用错误:", err);
      common_vendor.index.__f__("error", "at main.js:130", "错误信息:", info);
    }
  };
  checkApiAvailability();
  services_appService.appService.init().catch((error) => {
    common_vendor.index.__f__("error", "at main.js:139", "应用初始化失败:", error);
  });
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
