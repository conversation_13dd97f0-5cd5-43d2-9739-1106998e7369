/**
 * 用户服务
 * 处理用户登录、注册、信息管理等功能
 */
import { setData, getData, removeData } from '../utils/storage.js';
import http from '../utils/request.js';
import { API_ENDPOINTS, TOKEN_KEY, USER_INFO_KEY } from '../utils/config.js';

class UserService {
  constructor() {
    this.isInitialized = false;
    this.listeners = [];
    this.currentUser = null;
    this.token = null;
  }

  /**
   * 初始化用户服务
   * @returns {Promise<void>}
   */
  async init() {
    if (this.isInitialized) return;

    try {
      // 从存储中恢复用户状态
      this.token = getData(TOKEN_KEY);
      this.currentUser = getData(USER_INFO_KEY);
      this.isInitialized = true;

      // 如果有token但没有用户信息，尝试获取用户信息
      if (this.token && !this.currentUser) {
        await this.fetchUserInfo();
      }
    } catch (error) {
      this.logout();
    }
  }

  /**
   * 判断用户是否已登录
   * @returns {boolean} 是否已登录
   */
  isLoggedIn() {
    return !!(this.token && this.currentUser && (this.currentUser.phone || this.currentUser.username));
  }

  /**
   * 获取当前用户信息
   * @returns {Object|null} 用户信息
   */
  getUserInfo() {
    return this.currentUser;
  }

  /**
   * 获取用户Token
   * @returns {string|null} 用户Token
   */
  getToken() {
    return this.token;
  }

  /**
   * 用户登录
   * @param {Object} credentials 登录凭证
   * @param {string} credentials.username 用户名或手机号
   * @param {string} credentials.password 密码
   * @returns {Promise<Object>} 登录结果
   */
  async login(credentials) {
    try {
      // 调用后端登录接口
      const response = await http.post(API_ENDPOINTS.USER_LOGIN, {
        phone: credentials.username,
        password: credentials.password
      });

      // 保存token和用户信息
      // response 已经是 data.data，即 {user: {...}, token: "..."}
      this.token = response.token;
      this.currentUser = response.user;

      if (!this.token || !this.currentUser) {
        throw new Error('登录响应数据格式错误');
      }

      setData(TOKEN_KEY, this.token, 7 * 24 * 60 * 60); // 7天有效期
      setData(USER_INFO_KEY, this.currentUser, 30 * 24 * 60 * 60); // 30天有效期

      // 登录成功后立即获取完整用户信息（包括会员状态）
      try {
        await this.fetchUserInfo();
      } catch (fetchError) {
        console.warn('获取用户信息失败，但登录成功:', fetchError);
        // 不抛出错误，因为登录本身是成功的
      }

      // 通知监听器
      this.notifyListeners();

      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 用户注册
   * @param {Object} userData 用户数据
   * @returns {Promise<Object>} 注册结果
   */
  async register(userData) {
    try {

      const registerResponse = await http.post(API_ENDPOINTS.USER_REGISTER, {
        phone: userData.phone,
        password: userData.password,
        confirm_password: userData.confirmPassword,
        name: userData.name || '新用户',
        position: userData.position || '请设置职位',
        company: userData.company || '请设置公司'
      });



      // 保存token和用户信息
      this.token = registerResponse.token;
      this.currentUser = registerResponse.user;

      setData(TOKEN_KEY, this.token, 7 * 24 * 60 * 60); // 7天有效期
      setData(USER_INFO_KEY, this.currentUser, 30 * 24 * 60 * 60); // 30天有效期

      // 通知监听器
      this.notifyListeners();

      return registerResponse;

    } catch (error) {

      // 如果是404错误，表示API路径不存在，需要检查配置
      if (error.message.includes('不存在') || error.message.includes('404')) {
        throw new Error('注册接口不存在，请检查API配置');
      }

      // 直接抛出错误，不再尝试登录
      throw error;
    }
  }

  /**
   * 保存用户信息
   * @param {Object} userInfo 用户信息
   * @returns {Promise<void>}
   */
  async saveUserInfo(userInfo) {
    try {
      // 如果已登录，调用后端更新接口
      if (this.token) {
        const response = await http.post(API_ENDPOINTS.USER_UPDATE, userInfo);
        this.currentUser = { ...this.currentUser, ...response };
      } else {
        // 未登录时只更新本地信息
        this.currentUser = { ...this.currentUser, ...userInfo };
      }

      // 保存到存储
      setData(USER_INFO_KEY, this.currentUser, 30 * 24 * 60 * 60); // 30天有效期

      // 通知监听器
      this.notifyListeners();
    } catch (error) {
      throw error;
    }
  }

  /**
   * 获取最新的用户信息
   * @returns {Promise<Object>} 用户信息
   */
  async fetchUserInfo() {
    try {
      // 重新从存储中获取token，确保是最新的
      this.token = getData(TOKEN_KEY);

      if (!this.token) {
        throw new Error('未登录，无法获取用户信息');
      }

      // 调用后端获取用户信息接口
      const response = await http.get(API_ENDPOINTS.USER_INFO);

      console.log('fetchUserInfo 后端响应:', response);

      // 更新当前用户信息
      this.currentUser = response.user;
      setData(USER_INFO_KEY, response.user, 30 * 24 * 60 * 60); // 30天有效期

      // 同步更新本地会员状态存储
      if (response.member) {
        console.log('更新本地会员状态:', response.member);
        uni.setStorageSync('isMember', response.member.is_member);
        uni.setStorageSync('memberExpireDate', response.member.member_expire_time);
        uni.setStorageSync('memberLevel', response.member.member_level);

        // 添加会员状态到用户信息中
        this.currentUser.member_info = response.member;
      } else {
        console.log('后端未返回会员信息');
        uni.setStorageSync('isMember', false);
        uni.setStorageSync('memberExpireDate', null);
        uni.setStorageSync('memberLevel', 0);
      }

      // 通知监听器
      this.notifyListeners();

      return response;
    } catch (error) {
      console.error('fetchUserInfo 错误:', error);
      throw error;
    }
  }

  /**
   * 检查并同步会员状态
   * @returns {Promise<boolean>}
   */
  async syncMemberStatus() {
    if (!this.isLoggedIn()) {
      return false;
    }

    try {
      // 获取最新用户信息（包含会员状态）
      await this.fetchUserInfo();
      return true;
    } catch (error) {
      console.error('会员状态同步失败:', error);
      return false;
    }
  }

  /**
   * 获取本地会员状态
   * @returns {Object}
   */
  getMemberStatus() {
    return {
      isMember: uni.getStorageSync('isMember') || false,
      memberExpireDate: uni.getStorageSync('memberExpireDate') || null,
      memberLevel: uni.getStorageSync('memberLevel') || 0
    };
  }

  /**
   * 用户登出
   * @returns {Promise<void>}
   */
  async logout() {
    try {
      // 清除token和用户信息
      this.token = null;
      this.currentUser = null;

      // 从存储中移除
      removeData(TOKEN_KEY);
      removeData(USER_INFO_KEY);

      // 通知监听器
      this.notifyListeners();

      // 返回成功的 Promise
      return Promise.resolve();
    } catch (error) {
      throw error;
    }
  }

  /**
   * 添加状态变更监听器
   * @param {Function} listener 监听函数
   */
  addListener(listener) {
    if (typeof listener === 'function' && !this.listeners.includes(listener)) {
      this.listeners.push(listener);
    }
  }

  /**
   * 移除状态变更监听器
   * @param {Function} listener 监听函数
   */
  removeListener(listener) {
    const index = this.listeners.indexOf(listener);
    if (index !== -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 通知所有监听器状态变更
   */
  notifyListeners() {
    const data = {
      isLoggedIn: this.isLoggedIn(),
      userInfo: this.currentUser,
      token: this.token
    };
    
    this.listeners.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        // 忽略通知失败
      }
    });
  }

  // 工具方法

  /**
   * 检查Token是否有效
   * @returns {boolean} Token是否有效
   */
  isTokenValid() {
    if (!this.token) return false;

    try {
      // 简单的Token格式检查
      const parts = this.token.split('.');
      return parts.length === 3; // JWT格式检查
    } catch (error) {
      return false;
    }
  }

  /**
   * 微信登录
   * @param {string} code 微信授权码
   * @returns {Promise<Object>} 登录结果
   */
  async wechatLogin(code) {
    try {
      const requestData = { code: code };
      
      // 调用后端微信登录接口
      const response = await http.post(API_ENDPOINTS.USER_WECHAT_LOGIN, requestData, {
        // 增加超时时间，确保有足够时间处理
        timeout: 15000
      });


      
      // 保存token和用户信息
      this.token = response.token;
      this.currentUser = response.user;

      setData(TOKEN_KEY, this.token, 7 * 24 * 60 * 60); // 7天有效期，与后端保持一致
      setData(USER_INFO_KEY, this.currentUser, 30 * 24 * 60 * 60); // 30天有效期

      // 通知监听器
      this.notifyListeners();

      return response;
    } catch (error) {
      // 提取更详细的错误信息
      let errorMessage = '微信登录失败，请重试';
      let wxErrorCode = 0;

      if (error.data) {
        // 获取后端返回的具体错误信息
        errorMessage = error.data.message || errorMessage;
        wxErrorCode = error.data.wx_error_code || 0;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      // 创建增强的错误对象
      const enhancedError = new Error(errorMessage);
      enhancedError.originalError = error;
      enhancedError.wx_error_code = wxErrorCode;
      
      throw enhancedError;
    }
  }

  /**
   * 微信获取手机号
   * @param {string} code 手机号授权码
   * @returns {Promise<Object>} 获取结果
   */
  async wechatGetPhone(code) {
    try {
      const response = await http.post(API_ENDPOINTS.USER_WECHAT_GET_PHONE, {
        code: code
      });

      // 更新用户信息中的手机号
      if (this.currentUser && response.phone) {
        this.currentUser.phone = response.phone;
        setData(USER_INFO_KEY, this.currentUser, 30 * 24 * 60 * 60);
        this.notifyListeners();
      }

      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 微信手机号授权登录
   * @param {string} loginCode 微信登录授权码
   * @param {string} phoneCode 微信手机号授权码
   * @returns {Promise<Object>} 登录结果
   */
  async wechatPhoneLogin(loginCode, phoneCode) {
    try {
      // 添加请求前的验证
      if (!loginCode) {
        throw new Error('登录授权码无效');
      }

      if (!phoneCode) {
        throw new Error('手机号授权码无效');
      }

      // 简化请求参数，确保授权码正确传递
      const requestData = {
        login_code: loginCode,
        phone_code: phoneCode
      };
      
      // 调用后端微信手机号登录接口
      const response = await http.post(API_ENDPOINTS.USER_WECHAT_PHONE_LOGIN, requestData, {
        timeout: 15000
      });


      
      // 保存token和用户信息
      this.token = response.token;
      this.currentUser = response.user;

      setData(TOKEN_KEY, this.token, 7 * 24 * 60 * 60); // 7天有效期，与后端保持一致
      setData(USER_INFO_KEY, this.currentUser, 30 * 24 * 60 * 60); // 30天有效期

      // 通知监听器
      this.notifyListeners();

      return response;
    } catch (error) {
      // 提取更详细的错误信息
      let errorMessage = '微信登录失败，请重试';
      let wxErrorCode = 0;

      if (error.data) {
        // 获取后端返回的具体错误信息
        errorMessage = error.data.message || errorMessage;
        wxErrorCode = error.data.wx_error_code || 0;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      // 创建增强的错误对象
      const enhancedError = new Error(errorMessage);
      enhancedError.originalError = error;
      enhancedError.wx_error_code = wxErrorCode;
      
      throw enhancedError;
    }
  }

  /**
   * 修改密码
   * @param {Object} passwordData 密码数据
   * @returns {Promise<Object>} 修改结果
   */
  async changePassword(passwordData) {
    try {
      if (!this.token) {
        throw new Error('请先登录');
      }

      const response = await http.post(API_ENDPOINTS.USER_CHANGE_PASSWORD, {
        old_password: passwordData.oldPassword,
        new_password: passwordData.newPassword,
        confirm_password: passwordData.confirmPassword
      });

      return response;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 刷新Token
   * @returns {Promise<Object>} 刷新结果
   */
  async refreshToken() {
    try {
      if (!this.token) {
        throw new Error('请先登录');
      }

      const response = await http.post(API_ENDPOINTS.USER_REFRESH_TOKEN);

      // 更新token
      this.token = response.token;
      setData(TOKEN_KEY, this.token, 7 * 24 * 60 * 60); // 7天有效期

      return response;
    } catch (error) {
      throw error;
    }
  }


}

// 导出单例
export default new UserService(); 