{"version": 3, "file": "userService.js", "sources": ["services/userService.js"], "sourcesContent": ["/**\n * 用户服务\n * 处理用户登录、注册、信息管理等功能\n */\nimport { setData, getData, removeData } from '../utils/storage.js';\nimport http from '../utils/request.js';\nimport { API_ENDPOINTS, TOKEN_KEY, USER_INFO_KEY } from '../utils/config.js';\n\nclass UserService {\n  constructor() {\n    this.isInitialized = false;\n    this.listeners = [];\n    this.currentUser = null;\n    this.token = null;\n  }\n\n  /**\n   * 初始化用户服务\n   * @returns {Promise<void>}\n   */\n  async init() {\n    if (this.isInitialized) return;\n\n    try {\n      // 从存储中恢复用户状态\n      this.token = getData(TOKEN_KEY);\n      this.currentUser = getData(USER_INFO_KEY);\n      this.isInitialized = true;\n\n      // 如果有token但没有用户信息，尝试获取用户信息\n      if (this.token && !this.currentUser) {\n        await this.fetchUserInfo();\n      }\n    } catch (error) {\n      this.logout();\n    }\n  }\n\n  /**\n   * 判断用户是否已登录\n   * @returns {boolean} 是否已登录\n   */\n  isLoggedIn() {\n    return !!(this.token && this.currentUser && (this.currentUser.phone || this.currentUser.username));\n  }\n\n  /**\n   * 获取当前用户信息\n   * @returns {Object|null} 用户信息\n   */\n  getUserInfo() {\n    return this.currentUser;\n  }\n\n  /**\n   * 获取用户Token\n   * @returns {string|null} 用户Token\n   */\n  getToken() {\n    return this.token;\n  }\n\n  /**\n   * 用户登录\n   * @param {Object} credentials 登录凭证\n   * @param {string} credentials.username 用户名或手机号\n   * @param {string} credentials.password 密码\n   * @returns {Promise<Object>} 登录结果\n   */\n  async login(credentials) {\n    try {\n      // 调用后端登录接口\n      const response = await http.post(API_ENDPOINTS.USER_LOGIN, {\n        phone: credentials.username,\n        password: credentials.password\n      });\n\n      // 保存token和用户信息\n      // response 已经是 data.data，即 {user: {...}, token: \"...\"}\n      this.token = response.token;\n      this.currentUser = response.user;\n\n      if (!this.token || !this.currentUser) {\n        throw new Error('登录响应数据格式错误');\n      }\n\n      setData(TOKEN_KEY, this.token, 7 * 24 * 60 * 60); // 7天有效期\n      setData(USER_INFO_KEY, this.currentUser, 30 * 24 * 60 * 60); // 30天有效期\n\n      // 登录成功后立即获取完整用户信息（包括会员状态）\n      try {\n        await this.fetchUserInfo();\n      } catch (fetchError) {\n        console.warn('获取用户信息失败，但登录成功:', fetchError);\n        // 不抛出错误，因为登录本身是成功的\n      }\n\n      // 通知监听器\n      this.notifyListeners();\n\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  /**\n   * 用户注册\n   * @param {Object} userData 用户数据\n   * @returns {Promise<Object>} 注册结果\n   */\n  async register(userData) {\n    try {\n\n      const registerResponse = await http.post(API_ENDPOINTS.USER_REGISTER, {\n        phone: userData.phone,\n        password: userData.password,\n        confirm_password: userData.confirmPassword,\n        name: userData.name || '新用户',\n        position: userData.position || '请设置职位',\n        company: userData.company || '请设置公司'\n      });\n\n\n\n      // 保存token和用户信息\n      this.token = registerResponse.token;\n      this.currentUser = registerResponse.user;\n\n      setData(TOKEN_KEY, this.token, 7 * 24 * 60 * 60); // 7天有效期\n      setData(USER_INFO_KEY, this.currentUser, 30 * 24 * 60 * 60); // 30天有效期\n\n      // 通知监听器\n      this.notifyListeners();\n\n      return registerResponse;\n\n    } catch (error) {\n\n      // 如果是404错误，表示API路径不存在，需要检查配置\n      if (error.message.includes('不存在') || error.message.includes('404')) {\n        throw new Error('注册接口不存在，请检查API配置');\n      }\n\n      // 直接抛出错误，不再尝试登录\n      throw error;\n    }\n  }\n\n  /**\n   * 保存用户信息\n   * @param {Object} userInfo 用户信息\n   * @returns {Promise<void>}\n   */\n  async saveUserInfo(userInfo) {\n    try {\n      // 如果已登录，调用后端更新接口\n      if (this.token) {\n        const response = await http.post(API_ENDPOINTS.USER_UPDATE, userInfo);\n        this.currentUser = { ...this.currentUser, ...response };\n      } else {\n        // 未登录时只更新本地信息\n        this.currentUser = { ...this.currentUser, ...userInfo };\n      }\n\n      // 保存到存储\n      setData(USER_INFO_KEY, this.currentUser, 30 * 24 * 60 * 60); // 30天有效期\n\n      // 通知监听器\n      this.notifyListeners();\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  /**\n   * 获取最新的用户信息\n   * @returns {Promise<Object>} 用户信息\n   */\n  async fetchUserInfo() {\n    try {\n      // 重新从存储中获取token，确保是最新的\n      this.token = getData(TOKEN_KEY);\n\n      if (!this.token) {\n        throw new Error('未登录，无法获取用户信息');\n      }\n\n      // 调用后端获取用户信息接口\n      const response = await http.get(API_ENDPOINTS.USER_INFO);\n\n      console.log('fetchUserInfo 后端响应:', response);\n\n      // 更新当前用户信息\n      this.currentUser = response.user;\n      setData(USER_INFO_KEY, response.user, 30 * 24 * 60 * 60); // 30天有效期\n\n      // 同步更新本地会员状态存储\n      if (response.member) {\n        console.log('更新本地会员状态:', response.member);\n        uni.setStorageSync('isMember', response.member.is_member);\n        uni.setStorageSync('memberExpireDate', response.member.member_expire_time);\n        uni.setStorageSync('memberLevel', response.member.member_level);\n\n        // 添加会员状态到用户信息中\n        this.currentUser.member_info = response.member;\n      } else {\n        console.log('后端未返回会员信息');\n        uni.setStorageSync('isMember', false);\n        uni.setStorageSync('memberExpireDate', null);\n        uni.setStorageSync('memberLevel', 0);\n      }\n\n      // 通知监听器\n      this.notifyListeners();\n\n      return response;\n    } catch (error) {\n      console.error('fetchUserInfo 错误:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 检查并同步会员状态\n   * @returns {Promise<boolean>}\n   */\n  async syncMemberStatus() {\n    if (!this.isLoggedIn()) {\n      return false;\n    }\n\n    try {\n      // 获取最新用户信息（包含会员状态）\n      await this.fetchUserInfo();\n      return true;\n    } catch (error) {\n      console.error('会员状态同步失败:', error);\n      return false;\n    }\n  }\n\n  /**\n   * 获取本地会员状态\n   * @returns {Object}\n   */\n  getMemberStatus() {\n    return {\n      isMember: uni.getStorageSync('isMember') || false,\n      memberExpireDate: uni.getStorageSync('memberExpireDate') || null,\n      memberLevel: uni.getStorageSync('memberLevel') || 0\n    };\n  }\n\n  /**\n   * 用户登出\n   * @returns {Promise<void>}\n   */\n  async logout() {\n    try {\n      // 清除token和用户信息\n      this.token = null;\n      this.currentUser = null;\n\n      // 从存储中移除\n      removeData(TOKEN_KEY);\n      removeData(USER_INFO_KEY);\n\n      // 通知监听器\n      this.notifyListeners();\n\n      // 返回成功的 Promise\n      return Promise.resolve();\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  /**\n   * 添加状态变更监听器\n   * @param {Function} listener 监听函数\n   */\n  addListener(listener) {\n    if (typeof listener === 'function' && !this.listeners.includes(listener)) {\n      this.listeners.push(listener);\n    }\n  }\n\n  /**\n   * 移除状态变更监听器\n   * @param {Function} listener 监听函数\n   */\n  removeListener(listener) {\n    const index = this.listeners.indexOf(listener);\n    if (index !== -1) {\n      this.listeners.splice(index, 1);\n    }\n  }\n\n  /**\n   * 通知所有监听器状态变更\n   */\n  notifyListeners() {\n    const data = {\n      isLoggedIn: this.isLoggedIn(),\n      userInfo: this.currentUser,\n      token: this.token\n    };\n    \n    this.listeners.forEach(listener => {\n      try {\n        listener(data);\n      } catch (error) {\n        // 忽略通知失败\n      }\n    });\n  }\n\n  // 工具方法\n\n  /**\n   * 检查Token是否有效\n   * @returns {boolean} Token是否有效\n   */\n  isTokenValid() {\n    if (!this.token) return false;\n\n    try {\n      // 简单的Token格式检查\n      const parts = this.token.split('.');\n      return parts.length === 3; // JWT格式检查\n    } catch (error) {\n      return false;\n    }\n  }\n\n  /**\n   * 微信登录\n   * @param {string} code 微信授权码\n   * @returns {Promise<Object>} 登录结果\n   */\n  async wechatLogin(code) {\n    try {\n      const requestData = { code: code };\n      \n      // 调用后端微信登录接口\n      const response = await http.post(API_ENDPOINTS.USER_WECHAT_LOGIN, requestData, {\n        // 增加超时时间，确保有足够时间处理\n        timeout: 15000\n      });\n\n\n      \n      // 保存token和用户信息\n      this.token = response.token;\n      this.currentUser = response.user;\n\n      setData(TOKEN_KEY, this.token, 7 * 24 * 60 * 60); // 7天有效期，与后端保持一致\n      setData(USER_INFO_KEY, this.currentUser, 30 * 24 * 60 * 60); // 30天有效期\n\n      // 通知监听器\n      this.notifyListeners();\n\n      return response;\n    } catch (error) {\n      // 提取更详细的错误信息\n      let errorMessage = '微信登录失败，请重试';\n      let wxErrorCode = 0;\n\n      if (error.data) {\n        // 获取后端返回的具体错误信息\n        errorMessage = error.data.message || errorMessage;\n        wxErrorCode = error.data.wx_error_code || 0;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      \n      // 创建增强的错误对象\n      const enhancedError = new Error(errorMessage);\n      enhancedError.originalError = error;\n      enhancedError.wx_error_code = wxErrorCode;\n      \n      throw enhancedError;\n    }\n  }\n\n  /**\n   * 微信获取手机号\n   * @param {string} code 手机号授权码\n   * @returns {Promise<Object>} 获取结果\n   */\n  async wechatGetPhone(code) {\n    try {\n      const response = await http.post(API_ENDPOINTS.USER_WECHAT_GET_PHONE, {\n        code: code\n      });\n\n      // 更新用户信息中的手机号\n      if (this.currentUser && response.phone) {\n        this.currentUser.phone = response.phone;\n        setData(USER_INFO_KEY, this.currentUser, 30 * 24 * 60 * 60);\n        this.notifyListeners();\n      }\n\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  /**\n   * 微信手机号授权登录\n   * @param {string} loginCode 微信登录授权码\n   * @param {string} phoneCode 微信手机号授权码\n   * @returns {Promise<Object>} 登录结果\n   */\n  async wechatPhoneLogin(loginCode, phoneCode) {\n    try {\n      // 添加请求前的验证\n      if (!loginCode) {\n        throw new Error('登录授权码无效');\n      }\n\n      if (!phoneCode) {\n        throw new Error('手机号授权码无效');\n      }\n\n      // 简化请求参数，确保授权码正确传递\n      const requestData = {\n        login_code: loginCode,\n        phone_code: phoneCode\n      };\n      \n      // 调用后端微信手机号登录接口\n      const response = await http.post(API_ENDPOINTS.USER_WECHAT_PHONE_LOGIN, requestData, {\n        timeout: 15000\n      });\n\n\n      \n      // 保存token和用户信息\n      this.token = response.token;\n      this.currentUser = response.user;\n\n      setData(TOKEN_KEY, this.token, 7 * 24 * 60 * 60); // 7天有效期，与后端保持一致\n      setData(USER_INFO_KEY, this.currentUser, 30 * 24 * 60 * 60); // 30天有效期\n\n      // 通知监听器\n      this.notifyListeners();\n\n      return response;\n    } catch (error) {\n      // 提取更详细的错误信息\n      let errorMessage = '微信登录失败，请重试';\n      let wxErrorCode = 0;\n\n      if (error.data) {\n        // 获取后端返回的具体错误信息\n        errorMessage = error.data.message || errorMessage;\n        wxErrorCode = error.data.wx_error_code || 0;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      \n      // 创建增强的错误对象\n      const enhancedError = new Error(errorMessage);\n      enhancedError.originalError = error;\n      enhancedError.wx_error_code = wxErrorCode;\n      \n      throw enhancedError;\n    }\n  }\n\n  /**\n   * 修改密码\n   * @param {Object} passwordData 密码数据\n   * @returns {Promise<Object>} 修改结果\n   */\n  async changePassword(passwordData) {\n    try {\n      if (!this.token) {\n        throw new Error('请先登录');\n      }\n\n      const response = await http.post(API_ENDPOINTS.USER_CHANGE_PASSWORD, {\n        old_password: passwordData.oldPassword,\n        new_password: passwordData.newPassword,\n        confirm_password: passwordData.confirmPassword\n      });\n\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  /**\n   * 刷新Token\n   * @returns {Promise<Object>} 刷新结果\n   */\n  async refreshToken() {\n    try {\n      if (!this.token) {\n        throw new Error('请先登录');\n      }\n\n      const response = await http.post(API_ENDPOINTS.USER_REFRESH_TOKEN);\n\n      // 更新token\n      this.token = response.token;\n      setData(TOKEN_KEY, this.token, 7 * 24 * 60 * 60); // 7天有效期\n\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  }\n\n\n}\n\n// 导出单例\nexport default new UserService(); "], "names": ["getData", "TOKEN_KEY", "USER_INFO_KEY", "http", "API_ENDPOINTS", "setData", "uni", "removeData"], "mappings": ";;;;;AAQA,MAAM,YAAY;AAAA,EAChB,cAAc;AACZ,SAAK,gBAAgB;AACrB,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,QAAQ;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,OAAO;AACX,QAAI,KAAK;AAAe;AAExB,QAAI;AAEF,WAAK,QAAQA,sBAAQC,aAAAA,SAAS;AAC9B,WAAK,cAAcD,sBAAQE,aAAAA,aAAa;AACxC,WAAK,gBAAgB;AAGrB,UAAI,KAAK,SAAS,CAAC,KAAK,aAAa;AACnC,cAAM,KAAK;MACZ;AAAA,IACF,SAAQ,OAAO;AACd,WAAK,OAAM;AAAA,IACZ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,aAAa;AACX,WAAO,CAAC,EAAE,KAAK,SAAS,KAAK,gBAAgB,KAAK,YAAY,SAAS,KAAK,YAAY;AAAA,EACzF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,cAAc;AACZ,WAAO,KAAK;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,WAAW;AACT,WAAO,KAAK;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,MAAM,MAAM,aAAa;AACvB,QAAI;AAEF,YAAM,WAAW,MAAMC,cAAAA,KAAK,KAAKC,aAAAA,cAAc,YAAY;AAAA,QACzD,OAAO,YAAY;AAAA,QACnB,UAAU,YAAY;AAAA,MAC9B,CAAO;AAID,WAAK,QAAQ,SAAS;AACtB,WAAK,cAAc,SAAS;AAE5B,UAAI,CAAC,KAAK,SAAS,CAAC,KAAK,aAAa;AACpC,cAAM,IAAI,MAAM,YAAY;AAAA,MAC7B;AAEDC,4BAAQJ,aAAAA,WAAW,KAAK,OAAO,IAAI,KAAK,KAAK,EAAE;AAC/CI,4BAAQH,aAAAA,eAAe,KAAK,aAAa,KAAK,KAAK,KAAK,EAAE;AAG1D,UAAI;AACF,cAAM,KAAK;MACZ,SAAQ,YAAY;AACnBI,sBAAA,MAAA,MAAA,QAAA,iCAAa,mBAAmB,UAAU;AAAA,MAE3C;AAGD,WAAK,gBAAe;AAEpB,aAAO;AAAA,IACR,SAAQ,OAAO;AACd,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,SAAS,UAAU;AACvB,QAAI;AAEF,YAAM,mBAAmB,MAAMH,cAAAA,KAAK,KAAKC,aAAAA,cAAc,eAAe;AAAA,QACpE,OAAO,SAAS;AAAA,QAChB,UAAU,SAAS;AAAA,QACnB,kBAAkB,SAAS;AAAA,QAC3B,MAAM,SAAS,QAAQ;AAAA,QACvB,UAAU,SAAS,YAAY;AAAA,QAC/B,SAAS,SAAS,WAAW;AAAA,MACrC,CAAO;AAKD,WAAK,QAAQ,iBAAiB;AAC9B,WAAK,cAAc,iBAAiB;AAEpCC,4BAAQJ,aAAAA,WAAW,KAAK,OAAO,IAAI,KAAK,KAAK,EAAE;AAC/CI,4BAAQH,aAAAA,eAAe,KAAK,aAAa,KAAK,KAAK,KAAK,EAAE;AAG1D,WAAK,gBAAe;AAEpB,aAAO;AAAA,IAER,SAAQ,OAAO;AAGd,UAAI,MAAM,QAAQ,SAAS,KAAK,KAAK,MAAM,QAAQ,SAAS,KAAK,GAAG;AAClE,cAAM,IAAI,MAAM,kBAAkB;AAAA,MACnC;AAGD,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,aAAa,UAAU;AAC3B,QAAI;AAEF,UAAI,KAAK,OAAO;AACd,cAAM,WAAW,MAAMC,mBAAK,KAAKC,aAAAA,cAAc,aAAa,QAAQ;AACpE,aAAK,cAAc,EAAE,GAAG,KAAK,aAAa,GAAG;MACrD,OAAa;AAEL,aAAK,cAAc,EAAE,GAAG,KAAK,aAAa,GAAG;MAC9C;AAGDC,4BAAQH,aAAAA,eAAe,KAAK,aAAa,KAAK,KAAK,KAAK,EAAE;AAG1D,WAAK,gBAAe;AAAA,IACrB,SAAQ,OAAO;AACd,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,gBAAgB;AACpB,QAAI;AAEF,WAAK,QAAQF,sBAAQC,aAAAA,SAAS;AAE9B,UAAI,CAAC,KAAK,OAAO;AACf,cAAM,IAAI,MAAM,cAAc;AAAA,MAC/B;AAGD,YAAM,WAAW,MAAME,cAAI,KAAC,IAAIC,aAAa,cAAC,SAAS;AAEvDE,yEAAY,uBAAuB,QAAQ;AAG3C,WAAK,cAAc,SAAS;AAC5BD,4BAAQH,aAAAA,eAAe,SAAS,MAAM,KAAK,KAAK,KAAK,EAAE;AAGvD,UAAI,SAAS,QAAQ;AACnBI,sBAAA,MAAA,MAAA,OAAA,kCAAY,aAAa,SAAS,MAAM;AACxCA,sBAAG,MAAC,eAAe,YAAY,SAAS,OAAO,SAAS;AACxDA,sBAAG,MAAC,eAAe,oBAAoB,SAAS,OAAO,kBAAkB;AACzEA,sBAAG,MAAC,eAAe,eAAe,SAAS,OAAO,YAAY;AAG9D,aAAK,YAAY,cAAc,SAAS;AAAA,MAChD,OAAa;AACLA,sBAAAA,qDAAY,WAAW;AACvBA,sBAAAA,MAAI,eAAe,YAAY,KAAK;AACpCA,sBAAAA,MAAI,eAAe,oBAAoB,IAAI;AAC3CA,sBAAAA,MAAI,eAAe,eAAe,CAAC;AAAA,MACpC;AAGD,WAAK,gBAAe;AAEpB,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,kCAAA,qBAAqB,KAAK;AACxC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,mBAAmB;AACvB,QAAI,CAAC,KAAK,cAAc;AACtB,aAAO;AAAA,IACR;AAED,QAAI;AAEF,YAAM,KAAK;AACX,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,2EAAc,aAAa,KAAK;AAChC,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,kBAAkB;AAChB,WAAO;AAAA,MACL,UAAUA,cAAG,MAAC,eAAe,UAAU,KAAK;AAAA,MAC5C,kBAAkBA,cAAG,MAAC,eAAe,kBAAkB,KAAK;AAAA,MAC5D,aAAaA,cAAG,MAAC,eAAe,aAAa,KAAK;AAAA,IACxD;AAAA,EACG;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,SAAS;AACb,QAAI;AAEF,WAAK,QAAQ;AACb,WAAK,cAAc;AAGnBC,oBAAU,WAACN,aAAS,SAAA;AACpBM,oBAAU,WAACL,aAAa,aAAA;AAGxB,WAAK,gBAAe;AAGpB,aAAO,QAAQ;IAChB,SAAQ,OAAO;AACd,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,YAAY,UAAU;AACpB,QAAI,OAAO,aAAa,cAAc,CAAC,KAAK,UAAU,SAAS,QAAQ,GAAG;AACxE,WAAK,UAAU,KAAK,QAAQ;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,eAAe,UAAU;AACvB,UAAM,QAAQ,KAAK,UAAU,QAAQ,QAAQ;AAC7C,QAAI,UAAU,IAAI;AAChB,WAAK,UAAU,OAAO,OAAO,CAAC;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,kBAAkB;AAChB,UAAM,OAAO;AAAA,MACX,YAAY,KAAK,WAAY;AAAA,MAC7B,UAAU,KAAK;AAAA,MACf,OAAO,KAAK;AAAA,IAClB;AAEI,SAAK,UAAU,QAAQ,cAAY;AACjC,UAAI;AACF,iBAAS,IAAI;AAAA,MACd,SAAQ,OAAO;AAAA,MAEf;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,eAAe;AACb,QAAI,CAAC,KAAK;AAAO,aAAO;AAExB,QAAI;AAEF,YAAM,QAAQ,KAAK,MAAM,MAAM,GAAG;AAClC,aAAO,MAAM,WAAW;AAAA,IACzB,SAAQ,OAAO;AACd,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,YAAY,MAAM;AACtB,QAAI;AACF,YAAM,cAAc,EAAE;AAGtB,YAAM,WAAW,MAAMC,cAAI,KAAC,KAAKC,aAAa,cAAC,mBAAmB,aAAa;AAAA;AAAA,QAE7E,SAAS;AAAA,MACjB,CAAO;AAKD,WAAK,QAAQ,SAAS;AACtB,WAAK,cAAc,SAAS;AAE5BC,4BAAQJ,aAAAA,WAAW,KAAK,OAAO,IAAI,KAAK,KAAK,EAAE;AAC/CI,4BAAQH,aAAAA,eAAe,KAAK,aAAa,KAAK,KAAK,KAAK,EAAE;AAG1D,WAAK,gBAAe;AAEpB,aAAO;AAAA,IACR,SAAQ,OAAO;AAEd,UAAI,eAAe;AACnB,UAAI,cAAc;AAElB,UAAI,MAAM,MAAM;AAEd,uBAAe,MAAM,KAAK,WAAW;AACrC,sBAAc,MAAM,KAAK,iBAAiB;AAAA,MAClD,WAAiB,MAAM,SAAS;AACxB,uBAAe,MAAM;AAAA,MACtB;AAGD,YAAM,gBAAgB,IAAI,MAAM,YAAY;AAC5C,oBAAc,gBAAgB;AAC9B,oBAAc,gBAAgB;AAE9B,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,eAAe,MAAM;AACzB,QAAI;AACF,YAAM,WAAW,MAAMC,cAAAA,KAAK,KAAKC,aAAAA,cAAc,uBAAuB;AAAA,QACpE;AAAA,MACR,CAAO;AAGD,UAAI,KAAK,eAAe,SAAS,OAAO;AACtC,aAAK,YAAY,QAAQ,SAAS;AAClCC,8BAAQH,aAAAA,eAAe,KAAK,aAAa,KAAK,KAAK,KAAK,EAAE;AAC1D,aAAK,gBAAe;AAAA,MACrB;AAED,aAAO;AAAA,IACR,SAAQ,OAAO;AACd,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,MAAM,iBAAiB,WAAW,WAAW;AAC3C,QAAI;AAEF,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,SAAS;AAAA,MAC1B;AAED,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,UAAU;AAAA,MAC3B;AAGD,YAAM,cAAc;AAAA,QAClB,YAAY;AAAA,QACZ,YAAY;AAAA,MACpB;AAGM,YAAM,WAAW,MAAMC,cAAI,KAAC,KAAKC,aAAa,cAAC,yBAAyB,aAAa;AAAA,QACnF,SAAS;AAAA,MACjB,CAAO;AAKD,WAAK,QAAQ,SAAS;AACtB,WAAK,cAAc,SAAS;AAE5BC,4BAAQJ,aAAAA,WAAW,KAAK,OAAO,IAAI,KAAK,KAAK,EAAE;AAC/CI,4BAAQH,aAAAA,eAAe,KAAK,aAAa,KAAK,KAAK,KAAK,EAAE;AAG1D,WAAK,gBAAe;AAEpB,aAAO;AAAA,IACR,SAAQ,OAAO;AAEd,UAAI,eAAe;AACnB,UAAI,cAAc;AAElB,UAAI,MAAM,MAAM;AAEd,uBAAe,MAAM,KAAK,WAAW;AACrC,sBAAc,MAAM,KAAK,iBAAiB;AAAA,MAClD,WAAiB,MAAM,SAAS;AACxB,uBAAe,MAAM;AAAA,MACtB;AAGD,YAAM,gBAAgB,IAAI,MAAM,YAAY;AAC5C,oBAAc,gBAAgB;AAC9B,oBAAc,gBAAgB;AAE9B,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,eAAe,cAAc;AACjC,QAAI;AACF,UAAI,CAAC,KAAK,OAAO;AACf,cAAM,IAAI,MAAM,MAAM;AAAA,MACvB;AAED,YAAM,WAAW,MAAMC,cAAAA,KAAK,KAAKC,aAAAA,cAAc,sBAAsB;AAAA,QACnE,cAAc,aAAa;AAAA,QAC3B,cAAc,aAAa;AAAA,QAC3B,kBAAkB,aAAa;AAAA,MACvC,CAAO;AAED,aAAO;AAAA,IACR,SAAQ,OAAO;AACd,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,eAAe;AACnB,QAAI;AACF,UAAI,CAAC,KAAK,OAAO;AACf,cAAM,IAAI,MAAM,MAAM;AAAA,MACvB;AAED,YAAM,WAAW,MAAMD,cAAI,KAAC,KAAKC,aAAa,cAAC,kBAAkB;AAGjE,WAAK,QAAQ,SAAS;AACtBC,4BAAQJ,aAAAA,WAAW,KAAK,OAAO,IAAI,KAAK,KAAK,EAAE;AAE/C,aAAO;AAAA,IACR,SAAQ,OAAO;AACd,YAAM;AAAA,IACP;AAAA,EACF;AAGH;AAGA,MAAe,cAAA,IAAI,YAAa;;"}