<?php
// +----------------------------------------------------------------------
// | 全局路由配置
// +----------------------------------------------------------------------

use think\facade\Route;

// API 路由组
Route::group('api', function () {
    
    // 用户相关接口
    Route::group('user', function () {
        Route::get('list', 'api/User/list');                   // 用户列表（验证用）
        Route::post('register', 'api/User/register');           // 用户注册
        Route::post('login', 'api/User/login');                 // 用户登录
        Route::post('wechat-login', 'api/User/wechatLogin');     // 微信登录
        Route::post('wechat-phone-login', 'api/User/wechatPhoneLogin'); // 微信手机号登录
        Route::get('info', 'api/User/info');                    // 获取用户信息
        Route::post('update', 'api/User/update');               // 更新用户信息

        Route::post('change-password', 'api/User/changePassword'); // 修改密码
        Route::post('refresh-token', 'api/User/refreshToken');  // 刷新Token
        // userinfo 接口
        Route::get('read', 'api/User/read');                    // 读取用户详细信息
        Route::post('save', 'api/User/save');                   // 保存用户信息
    });

    // 名片相关接口
    Route::group('card', function () {
        Route::get('info/:user_id', 'api/Card/info');           // 获取名片信息
        Route::post('share/:user_id', 'api/Card/share');        // 分享名片
        Route::post('collect/:user_id', 'api/Card/collect');    // 收藏名片
        Route::get('stats', 'api/Card/stats');                  // 获取统计数据
        Route::get('visitors', 'api/Card/visitors');            // 获取访客记录
        Route::get('ai-analysis', 'api/Card/aiAnalysis');       // AI数据分析
    });

    // 会员相关接口
    Route::group('premium', function () {
        Route::get('plans', 'api/Premium/plans');               // 获取会员套餐
        Route::post('purchase', 'api/Premium/purchase');        // 购买会员
        Route::get('status', 'api/Premium/status');             // 获取会员状态
        Route::post('activate', 'api/Premium/activate');        // 激活会员
    });

    // 支付相关接口
    Route::group('payment', function () {
        Route::post('create-order', 'api/Payment/createOrder'); // 创建支付订单
        Route::post('verify-code', 'api/Payment/verifyCode');   // 验证激活码
        Route::get('order-status', 'api/Payment/orderStatus');  // 查询订单状态
        Route::post('wechat/notify', 'api/Payment/wechatNotify'); // 微信支付回调
    });
    
    // 文件上传接口
    Route::group('upload', function () {
        Route::post('avatar', 'api/Upload/avatar');             // 上传头像
        Route::post('image', 'api/Upload/image');               // 上传图片
        Route::post('batch-images', 'api/Upload/batchImages'); // 批量上传图片

        // 测试接口
        Route::post('test', 'api/TestUpload/test');             // 测试上传
        Route::get('env', 'api/TestUpload/env');                // 环境检查
    });
    
});
