{"version": 3, "file": "userInfoService.js", "sources": ["services/userInfoService.js"], "sourcesContent": ["/**\n * 用户信息管理服务\n */\nimport { TOKEN_KEY, API_BASE_URL } from '../utils/config.js';\nimport { getData } from '../utils/storage.js';\nimport userService from './userService.js';\n\nclass UserInfoService {\n  /**\n   * 获取用户详细信息\n   * @returns {Promise} 用户信息\n   */\n  async getUserInfo() {\n    try {\n      // 使用统一的登录状态检查\n      if (!userService.isLoggedIn()) {\n        throw new Error('请先登录');\n      }\n\n      const token = userService.getToken();\n\n      const response = await uni.request({\n        url: `${API_BASE_URL}/user/read`,\n        method: 'GET',\n        header: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.statusCode === 200 && response.data.code === 200) {\n        return {\n          success: true,\n          data: response.data.data\n        };\n      } else {\n        console.error('获取用户信息失败 - 服务器响应:', response.data);\n        throw new Error(response.data.message || '获取用户信息失败');\n      }\n    } catch (error) {\n      console.error('获取用户信息失败:', error);\n      return {\n        success: false,\n        message: error.message || '网络请求失败'\n      };\n    }\n  }\n\n  /**\n   * 更新用户信息\n   * @param {object} userInfo 用户信息\n   * @returns {Promise} 更新结果\n   */\n  async updateUserInfo(userInfo) {\n    try {\n      // 使用统一的登录状态检查\n      if (!userService.isLoggedIn()) {\n        throw new Error('请先登录');\n      }\n\n      const token = userService.getToken();\n\n      // 构建更新数据 - 包含所有支持的字段\n      const updateData = {\n        id: userInfo.id,\n        name: userInfo.name,\n        phone: userInfo.phone,\n        email: userInfo.email,\n        wechat: userInfo.wechat,\n        position: userInfo.position,\n        company: userInfo.company,\n        address: userInfo.address,\n        description: userInfo.description,\n        avatar: userInfo.avatar,\n\n        // 个人介绍相关字段 - 确保数组字段转换为普通数组\n        tags: Array.isArray(userInfo.tags) ? [...userInfo.tags] : (userInfo.tags || []),\n        achievements: Array.isArray(userInfo.achievements) ? [...userInfo.achievements] : (userInfo.achievements || []),\n        education: userInfo.education || '',\n        showProfileSection: userInfo.showProfileSection,\n        showDescription: userInfo.showDescription,\n        showTags: userInfo.showTags,\n        showAchievements: userInfo.showAchievements,\n        showEducation: userInfo.showEducation,\n\n        // 企业介绍相关字段\n        companyName: userInfo.companyName,\n        companyDesc: userInfo.companyDesc,\n        companySlogan: userInfo.companySlogan,\n        advantages: userInfo.advantages,\n        companyAddress: userInfo.companyAddress,\n        showCompanySection: userInfo.showCompanySection,\n        showCompanyName: userInfo.showCompanyName,\n        showCompanyDesc: userInfo.showCompanyDesc,\n        showCompanySlogan: userInfo.showCompanySlogan,\n        showCompanyAdvantages: userInfo.showCompanyAdvantages,\n        showCompanyAddress: userInfo.showCompanyAddress\n      };\n\n      // 如果包含个性定制数据，也一并发送\n      if (userInfo.customStyle) {\n        updateData.customStyle = userInfo.customStyle;\n      }\n\n\n\n      // 过滤掉undefined和null的字段，但保留空数组和空字符串\n      const filteredUpdateData = {};\n      const protectedFields = ['tags', 'achievements', 'education'];\n\n      for (const [key, value] of Object.entries(updateData)) {\n        // 对于受保护的字段，即使是空数组也要保留\n        if (protectedFields.includes(key)) {\n          filteredUpdateData[key] = value;\n        } else if (value !== undefined && value !== null) {\n          filteredUpdateData[key] = value;\n        }\n      }\n\n      const response = await uni.request({\n        url: `${API_BASE_URL}/user/save`,\n        method: 'POST',\n        header: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        data: filteredUpdateData\n      });\n\n      if (response.statusCode === 200 && response.data.code === 200) {\n        return {\n          success: true,\n          message: '更新成功'\n        };\n      } else {\n        console.error('更新用户信息失败 - 服务器响应:', response.data);\n        throw new Error(response.data.message || '更新失败');\n      }\n    } catch (error) {\n      console.error('更新用户信息失败:', error);\n      return {\n        success: false,\n        message: error.message || '网络请求失败'\n      };\n    }\n  }\n\n  /**\n   * 上传头像\n   * @param {string} filePath 文件路径\n   * @returns {Promise} 上传结果\n   */\n  async uploadAvatar(filePath) {\n    try {\n      // 使用统一的登录状态检查\n      if (!userService.isLoggedIn()) {\n        throw new Error('请先登录');\n      }\n\n      const token = userService.getToken();\n\n      return new Promise((resolve, reject) => {\n        // 使用 ThinkAdmin 上传接口\n        const uploadUrl = `${API_BASE_URL}/upload/avatar`;\n\n        uni.uploadFile({\n          url: uploadUrl,\n          filePath: filePath,\n          name: 'avatar',\n          header: {\n            'Authorization': `Bearer ${token}`\n          },\n          success: (res) => {\n            try {\n              console.log('上传响应原始数据:', res);\n\n              // 检查响应状态码\n              if (res.statusCode !== 200) {\n                reject(new Error(`服务器响应错误: ${res.statusCode}`));\n                return;\n              }\n\n              // 检查响应数据是否存在\n              if (!res.data) {\n                reject(new Error('服务器返回空数据'));\n                return;\n              }\n\n              const data = JSON.parse(res.data);\n              console.log('解析后的数据:', data);\n\n              if (data.code === 200) {\n                resolve({\n                  success: true,\n                  data: data.data,\n                  url: data.data.url\n                });\n              } else {\n                reject(new Error(data.message || '上传失败'));\n              }\n            } catch (error) {\n              console.error('解析响应数据失败:', error);\n              console.error('原始响应数据:', res.data);\n              reject(new Error(`响应数据解析失败: ${error.message}`));\n            }\n          },\n          fail: () => {\n            reject(new Error('上传请求失败'));\n          }\n        });\n      });\n    } catch (error) {\n      console.error('上传头像失败:', error);\n      return {\n        success: false,\n        message: error.message || '上传失败'\n      };\n    }\n  }\n\n  /**\n   * 获取当前登录用户信息\n   * @returns {Promise} 当前用户信息\n   */\n  async getCurrentUserInfo() {\n    try {\n      return await this.getUserInfo();\n    } catch (error) {\n      console.error('获取当前用户信息失败:', error);\n      return {\n        success: false,\n        message: error.message || '获取用户信息失败'\n      };\n    }\n  }\n\n  /**\n   * 验证手机号格式\n   * @param {string} phone 手机号\n   * @returns {boolean} 是否有效\n   */\n  validatePhone(phone) {\n    return /^1[3-9]\\d{9}$/.test(phone);\n  }\n\n  /**\n   * 验证邮箱格式\n   * @param {string} email 邮箱\n   * @returns {boolean} 是否有效\n   */\n  validateEmail(email) {\n    return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email);\n  }\n}\n\nexport default new UserInfoService();\n"], "names": ["userService", "uni", "API_BASE_URL"], "mappings": ";;;;AAOA,MAAM,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,MAAM,cAAc;AAClB,QAAI;AAEF,UAAI,CAACA,qBAAAA,YAAY,cAAc;AAC7B,cAAM,IAAI,MAAM,MAAM;AAAA,MACvB;AAED,YAAM,QAAQA,iCAAY;AAE1B,YAAM,WAAW,MAAMC,cAAG,MAAC,QAAQ;AAAA,QACjC,KAAK,GAAGC,aAAY,YAAA;AAAA,QACpB,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,gBAAgB;AAAA,UAChB,iBAAiB,UAAU,KAAK;AAAA,QACjC;AAAA,MACT,CAAO;AAED,UAAI,SAAS,eAAe,OAAO,SAAS,KAAK,SAAS,KAAK;AAC7D,eAAO;AAAA,UACL,SAAS;AAAA,UACT,MAAM,SAAS,KAAK;AAAA,QAC9B;AAAA,MACA,OAAa;AACLD,sBAAc,MAAA,MAAA,SAAA,qCAAA,qBAAqB,SAAS,IAAI;AAChD,cAAM,IAAI,MAAM,SAAS,KAAK,WAAW,UAAU;AAAA,MACpD;AAAA,IACF,SAAQ,OAAO;AACdA,8EAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,eAAe,UAAU;AAC7B,QAAI;AAEF,UAAI,CAACD,qBAAAA,YAAY,cAAc;AAC7B,cAAM,IAAI,MAAM,MAAM;AAAA,MACvB;AAED,YAAM,QAAQA,iCAAY;AAG1B,YAAM,aAAa;AAAA,QACjB,IAAI,SAAS;AAAA,QACb,MAAM,SAAS;AAAA,QACf,OAAO,SAAS;AAAA,QAChB,OAAO,SAAS;AAAA,QAChB,QAAQ,SAAS;AAAA,QACjB,UAAU,SAAS;AAAA,QACnB,SAAS,SAAS;AAAA,QAClB,SAAS,SAAS;AAAA,QAClB,aAAa,SAAS;AAAA,QACtB,QAAQ,SAAS;AAAA;AAAA,QAGjB,MAAM,MAAM,QAAQ,SAAS,IAAI,IAAI,CAAC,GAAG,SAAS,IAAI,IAAK,SAAS,QAAQ,CAAA;AAAA,QAC5E,cAAc,MAAM,QAAQ,SAAS,YAAY,IAAI,CAAC,GAAG,SAAS,YAAY,IAAK,SAAS,gBAAgB,CAAA;AAAA,QAC5G,WAAW,SAAS,aAAa;AAAA,QACjC,oBAAoB,SAAS;AAAA,QAC7B,iBAAiB,SAAS;AAAA,QAC1B,UAAU,SAAS;AAAA,QACnB,kBAAkB,SAAS;AAAA,QAC3B,eAAe,SAAS;AAAA;AAAA,QAGxB,aAAa,SAAS;AAAA,QACtB,aAAa,SAAS;AAAA,QACtB,eAAe,SAAS;AAAA,QACxB,YAAY,SAAS;AAAA,QACrB,gBAAgB,SAAS;AAAA,QACzB,oBAAoB,SAAS;AAAA,QAC7B,iBAAiB,SAAS;AAAA,QAC1B,iBAAiB,SAAS;AAAA,QAC1B,mBAAmB,SAAS;AAAA,QAC5B,uBAAuB,SAAS;AAAA,QAChC,oBAAoB,SAAS;AAAA,MACrC;AAGM,UAAI,SAAS,aAAa;AACxB,mBAAW,cAAc,SAAS;AAAA,MACnC;AAKD,YAAM,qBAAqB,CAAA;AAC3B,YAAM,kBAAkB,CAAC,QAAQ,gBAAgB,WAAW;AAE5D,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,UAAU,GAAG;AAErD,YAAI,gBAAgB,SAAS,GAAG,GAAG;AACjC,6BAAmB,GAAG,IAAI;AAAA,QAC3B,WAAU,UAAU,UAAa,UAAU,MAAM;AAChD,6BAAmB,GAAG,IAAI;AAAA,QAC3B;AAAA,MACF;AAED,YAAM,WAAW,MAAMC,cAAG,MAAC,QAAQ;AAAA,QACjC,KAAK,GAAGC,aAAY,YAAA;AAAA,QACpB,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,gBAAgB;AAAA,UAChB,iBAAiB,UAAU,KAAK;AAAA,QACjC;AAAA,QACD,MAAM;AAAA,MACd,CAAO;AAED,UAAI,SAAS,eAAe,OAAO,SAAS,KAAK,SAAS,KAAK;AAC7D,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS;AAAA,QACnB;AAAA,MACA,OAAa;AACLD,sBAAc,MAAA,MAAA,SAAA,sCAAA,qBAAqB,SAAS,IAAI;AAChD,cAAM,IAAI,MAAM,SAAS,KAAK,WAAW,MAAM;AAAA,MAChD;AAAA,IACF,SAAQ,OAAO;AACdA,+EAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,aAAa,UAAU;AAC3B,QAAI;AAEF,UAAI,CAACD,qBAAAA,YAAY,cAAc;AAC7B,cAAM,IAAI,MAAM,MAAM;AAAA,MACvB;AAED,YAAM,QAAQA,iCAAY;AAE1B,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEtC,cAAM,YAAY,GAAGE,aAAY,YAAA;AAEjCD,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,UACL;AAAA,UACA,MAAM;AAAA,UACN,QAAQ;AAAA,YACN,iBAAiB,UAAU,KAAK;AAAA,UACjC;AAAA,UACD,SAAS,CAAC,QAAQ;AAChB,gBAAI;AACFA,4BAAY,MAAA,MAAA,OAAA,sCAAA,aAAa,GAAG;AAG5B,kBAAI,IAAI,eAAe,KAAK;AAC1B,uBAAO,IAAI,MAAM,YAAY,IAAI,UAAU,EAAE,CAAC;AAC9C;AAAA,cACD;AAGD,kBAAI,CAAC,IAAI,MAAM;AACb,uBAAO,IAAI,MAAM,UAAU,CAAC;AAC5B;AAAA,cACD;AAED,oBAAM,OAAO,KAAK,MAAM,IAAI,IAAI;AAChCA,4BAAY,MAAA,MAAA,OAAA,sCAAA,WAAW,IAAI;AAE3B,kBAAI,KAAK,SAAS,KAAK;AACrB,wBAAQ;AAAA,kBACN,SAAS;AAAA,kBACT,MAAM,KAAK;AAAA,kBACX,KAAK,KAAK,KAAK;AAAA,gBACjC,CAAiB;AAAA,cACjB,OAAqB;AACL,uBAAO,IAAI,MAAM,KAAK,WAAW,MAAM,CAAC;AAAA,cACzC;AAAA,YACF,SAAQ,OAAO;AACdA,uFAAc,aAAa,KAAK;AAChCA,4BAAA,MAAA,MAAA,SAAA,sCAAc,WAAW,IAAI,IAAI;AACjC,qBAAO,IAAI,MAAM,aAAa,MAAM,OAAO,EAAE,CAAC;AAAA,YAC/C;AAAA,UACF;AAAA,UACD,MAAM,MAAM;AACV,mBAAO,IAAI,MAAM,QAAQ,CAAC;AAAA,UAC3B;AAAA,QACX,CAAS;AAAA,MACT,CAAO;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,sCAAA,WAAW,KAAK;AAC9B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,qBAAqB;AACzB,QAAI;AACF,aAAO,MAAM,KAAK;IACnB,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,sCAAA,eAAe,KAAK;AAClC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,cAAc,OAAO;AACnB,WAAO,gBAAgB,KAAK,KAAK;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,cAAc,OAAO;AACnB,WAAO,6BAA6B,KAAK,KAAK;AAAA,EAC/C;AACH;AAEA,MAAe,kBAAA,IAAI,gBAAiB;;"}