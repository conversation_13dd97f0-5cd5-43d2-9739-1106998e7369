{"version": 3, "file": "company.js", "sources": ["pages/card/company.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.45.2025010502 (1)/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY2FyZC9jb21wYW55LnZ1ZQ"], "sourcesContent": ["\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\t// 状态栏高度\n\t\t\tstatusBarHeight: 0,\n\n\t\t\t// 企业版提示\n\t\t\tshowPremiumNotice: false,\n\n\t\t\t// 展开状态\n\t\t\texpandStatus: {\n\t\t\t\tinfo: true,\n\t\t\t\timages: false,\n\t\t\t\thonors: false\n\t\t\t},\n\n\t\t\t// 企业数据\n\t\t\tcompanyData: {\n\t\t\t\tshowSection: true,\n\t\t\t\t// 基本信息\n\t\t\t\tname: '',\n\t\t\t\tshowName: true,\n\t\t\t\tdesc: '',\n\t\t\t\tshowDesc: true,\n\t\t\t\tslogan: '',\n\t\t\t\tshowSlogan: true,\n\t\t\t\taddress: '',\n\t\t\t\tshowAddress: true,\n\t\t\t\t// 图片\n\t\t\t\timages: [],\n\t\t\t\t// 荣誉\n\t\t\t\thonors: []\n\t\t\t},\n\n\t\t\t// 荣誉弹窗\n\t\t\tshowHonorModal: false,\n\t\t\tcurrentHonor: {\n\t\t\t\ttitle: '',\n\t\t\t\timage: ''\n\t\t\t}\n\t\t}\n\t},\n\n\tcomputed: {\n\t\tnavbarStyle() {\n\t\t\treturn {\n\t\t\t\theight: `${this.statusBarHeight + 45}px`\n\t\t\t}\n\t\t},\n\t\tmainStyle() {\n\t\t\treturn {\n\t\t\t\tmarginTop: `${this.statusBarHeight + 55}px`\n\t\t\t}\n\t\t}\n\t},\n\n\tonLoad() {\n\t\tthis.getSystemInfo();\n\t\tthis.loadData();\n\t},\n\n\tmethods: {\n\t\t// 获取系统信息\n\t\tgetSystemInfo() {\n\t\t\tconst systemInfo = uni.getSystemInfoSync();\n\t\t\tthis.statusBarHeight = systemInfo.statusBarHeight || 0;\n\t\t},\n\n\t\t// 返回\n\t\tgoBack() {\n\t\t\tuni.navigateBack();\n\t\t},\n\n\t\t// 加载数据\n\t\tloadData() {\n\t\t\t// 这里可以从API加载数据\n\t\t\tconsole.log('加载企业数据');\n\t\t},\n\n\t\t// 保存数据\n\t\tsaveData() {\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '保存中...'\n\t\t\t});\n\n\t\t\t// 这里调用API保存数据\n\t\t\tsetTimeout(() => {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '保存成功',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t}, 1000);\n\t\t},\n\n\t\t// 主开关变化\n\t\tonSwitchChange(e) {\n\t\t\tthis.companyData.showSection = e.detail.value;\n\t\t},\n\n\t\t// 切换展开状态\n\t\ttoggleExpand(section) {\n\t\t\tthis.expandStatus[section] = !this.expandStatus[section];\n\t\t},\n\n\t\t// 上传图片\n\t\tuploadImage() {\n\t\t\tuni.chooseImage({\n\t\t\t\tcount: 1,\n\t\t\t\tsizeType: ['compressed'],\n\t\t\t\tsourceType: ['album', 'camera'],\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconst tempFilePath = res.tempFilePaths[0];\n\t\t\t\t\t// 这里可以上传到服务器\n\t\t\t\t\tthis.companyData.images.push(tempFilePath);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 删除图片\n\t\tdeleteImage(index) {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '确认删除',\n\t\t\t\tcontent: '确定要删除这张图片吗？',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tthis.companyData.images.splice(index, 1);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 添加荣誉\n\t\taddHonor() {\n\t\t\tthis.currentHonor = {\n\t\t\t\ttitle: '',\n\t\t\t\timage: ''\n\t\t\t};\n\t\t\tthis.showHonorModal = true;\n\t\t},\n\n\t\t// 选择荣誉图片\n\t\tselectHonorImage() {\n\t\t\tuni.chooseImage({\n\t\t\t\tcount: 1,\n\t\t\t\tsizeType: ['compressed'],\n\t\t\t\tsourceType: ['album', 'camera'],\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tthis.currentHonor.image = res.tempFilePaths[0];\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 确认添加荣誉\n\t\tconfirmHonor() {\n\t\t\tif (!this.currentHonor.title.trim()) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请输入荣誉标题',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (!this.currentHonor.image) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请选择荣誉图片',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.companyData.honors.push({\n\t\t\t\ttitle: this.currentHonor.title,\n\t\t\t\timage: this.currentHonor.image\n\t\t\t});\n\n\t\t\tthis.closeHonorModal();\n\t\t},\n\n\t\t// 关闭荣誉弹窗\n\t\tcloseHonorModal() {\n\t\t\tthis.showHonorModal = false;\n\t\t},\n\n\t\t// 删除荣誉\n\t\tdeleteHonor(index) {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '确认删除',\n\t\t\t\tcontent: '确定要删除这个荣誉吗？',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tthis.companyData.honors.splice(index, 1);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n/* 页面容器 */\n.company-page {\n\tmin-height: 100vh;\n\tbackground: #fafbfc;\n\tposition: relative;\n}\n\n/* 自定义导航栏 */\n.custom-navbar {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbackground: rgba(255, 255, 255, 0.98);\n\tbackdrop-filter: blur(20rpx);\n\tz-index: 1000;\n\tdisplay: flex;\n\talign-items: flex-end;\n\tpadding: 20rpx 40rpx 24rpx 40rpx;\n\tbox-sizing: border-box;\n\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.04);\n}\n\n.navbar-left {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.back-btn {\n\tpadding: 12rpx 20rpx;\n\tbackground: transparent;\n\tborder-radius: 12rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n\ttransition: all 0.2s ease;\n}\n\n.back-btn:active {\n\tbackground: rgba(0, 0, 0, 0.04);\n}\n\n.back-icon {\n\tfont-size: 32rpx;\n\tcolor: #1f2937;\n\tfont-weight: 500;\n}\n\n.back-text {\n\tfont-size: 28rpx;\n\tcolor: #1f2937;\n\tfont-weight: 500;\n}\n\n.navbar-title {\n\tcolor: #111827;\n\tfont-size: 34rpx;\n\tfont-weight: 600;\n\tposition: absolute;\n\tleft: 50%;\n\ttransform: translateX(-50%);\n\tletter-spacing: -0.5rpx;\n}\n\n/* 主要内容 */\n.main-content {\n\tpadding: 40rpx 32rpx;\n\tpadding-bottom: calc(160rpx + env(safe-area-inset-bottom));\n\tposition: relative;\n\tz-index: 1;\n}\n\n/* 企业版提示卡片 */\n.premium-card {\n\tbackground: linear-gradient(135deg, #1f2937 0%, #374151 100%);\n\tborder-radius: 20rpx;\n\tpadding: 40rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 32rpx;\n\tmargin-bottom: 40rpx;\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n\tborder: 1rpx solid rgba(255, 255, 255, 0.1);\n}\n\n.premium-icon {\n\twidth: 88rpx;\n\theight: 88rpx;\n\tbackground: rgba(255, 255, 255, 0.15);\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 44rpx;\n}\n\n.premium-info {\n\tflex: 1;\n}\n\n.premium-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #ffffff;\n\tmargin-bottom: 8rpx;\n}\n\n.premium-desc {\n\tfont-size: 26rpx;\n\tcolor: rgba(255, 255, 255, 0.7);\n\tline-height: 1.4;\n}\n\n.upgrade-btn {\n\tpadding: 20rpx 32rpx;\n\tbackground: rgba(255, 255, 255, 0.15);\n\tborder-radius: 12rpx;\n\tborder: 1rpx solid rgba(255, 255, 255, 0.2);\n\ttransition: all 0.2s ease;\n}\n\n.upgrade-btn:active {\n\tbackground: rgba(255, 255, 255, 0.25);\n}\n\n.upgrade-text {\n\tcolor: #ffffff;\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n}\n\n/* 卡片样式 */\n.switch-card, .info-card, .media-card, .honor-card {\n\tbackground: #ffffff;\n\tborder-radius: 16rpx;\n\tmargin-bottom: 24rpx;\n\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);\n\tborder: 1rpx solid rgba(0, 0, 0, 0.06);\n\toverflow: hidden;\n\ttransition: all 0.2s ease;\n}\n\n.switch-card:active, .info-card:active, .media-card:active, .honor-card:active {\n\ttransform: translateY(-1rpx);\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\n}\n\n.disabled {\n\topacity: 0.4;\n\tpointer-events: none;\n}\n\n.card-header {\n\tpadding: 40rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.04);\n}\n\n.header-info {\n\tflex: 1;\n}\n\n.card-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #111827;\n\tmargin-bottom: 8rpx;\n\tletter-spacing: -0.3rpx;\n}\n\n.card-desc {\n\tfont-size: 26rpx;\n\tcolor: #6b7280;\n\tline-height: 1.4;\n\tfont-weight: 400;\n}\n\n.expand-btn {\n\twidth: 56rpx;\n\theight: 56rpx;\n\tbackground: rgba(0, 0, 0, 0.04);\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\ttransition: all 0.2s ease;\n}\n\n.expand-btn.expanded {\n\ttransform: rotate(90deg);\n\tbackground: rgba(0, 0, 0, 0.08);\n}\n\n.expand-icon {\n\tfont-size: 24rpx;\n\tcolor: #6b7280;\n\tfont-weight: 600;\n}\n\n.card-content {\n\tpadding: 40rpx;\n\tpadding-top: 0;\n}\n\n/* 表单样式 */\n.input-group {\n\tmargin-bottom: 40rpx;\n}\n\n.input-header {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tmargin-bottom: 20rpx;\n}\n\n.input-label {\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\tcolor: #111827;\n\tletter-spacing: -0.3rpx;\n}\n\n.modern-input, .modern-textarea {\n\twidth: 100%;\n\theight: 96rpx;\n\tpadding: 0 28rpx;\n\tbackground: #fafbfc;\n\tborder: 1rpx solid #e5e7eb;\n\tborder-radius: 12rpx;\n\tfont-size: 28rpx;\n\tline-height: 96rpx;\n\tcolor: #111827;\n\ttransition: all 0.2s ease;\n\tbox-sizing: border-box;\n}\n\n.modern-input:focus, .modern-textarea:focus {\n\toutline: none;\n\tborder-color: #111827;\n\tbackground: #ffffff;\n\tbox-shadow: 0 0 0 4rpx rgba(17, 24, 39, 0.04);\n}\n\n.modern-input::placeholder, .modern-textarea::placeholder {\n\tcolor: #9ca3af;\n\tfont-size: 28rpx;\n}\n\n.modern-textarea {\n\theight: auto;\n\tmin-height: 160rpx;\n\tline-height: 1.6;\n\tpadding: 28rpx;\n\tresize: none;\n}\n\n/* 媒体网格 */\n.media-grid, .honor-grid {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tgap: 20rpx;\n}\n\n.media-item, .upload-item {\n\twidth: calc((100% - 40rpx) / 3);\n\taspect-ratio: 1;\n\tborder-radius: 16rpx;\n\tposition: relative;\n\toverflow: hidden;\n}\n\n.media-preview {\n\twidth: 100%;\n\theight: 100%;\n\tobject-fit: cover;\n}\n\n.upload-item {\n\tbackground: rgba(249, 250, 251, 0.8);\n\tborder: 3rpx dashed rgba(156, 163, 175, 0.6);\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tgap: 12rpx;\n\ttransition: all 0.3s ease;\n}\n\n.upload-item:active {\n\tborder-color: #6366f1;\n\tbackground: rgba(99, 102, 241, 0.05);\n\ttransform: scale(0.98);\n}\n\n.upload-icon {\n\tfont-size: 48rpx;\n\tcolor: #9ca3af;\n\tfont-weight: 300;\n}\n\n.upload-text {\n\tfont-size: 24rpx;\n\tcolor: #6b7280;\n\tfont-weight: 500;\n}\n\n.delete-btn {\n\tposition: absolute;\n\ttop: 8rpx;\n\tright: 8rpx;\n\twidth: 48rpx;\n\theight: 48rpx;\n\tbackground: rgba(239, 68, 68, 0.9);\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\ttransition: all 0.3s ease;\n}\n\n.delete-btn:active {\n\ttransform: scale(0.9);\n}\n\n.delete-icon {\n\tcolor: #ffffff;\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n}\n\n/* 荣誉样式 */\n.honor-item, .add-honor-item {\n\twidth: calc((100% - 20rpx) / 2);\n\taspect-ratio: 1;\n\tborder-radius: 16rpx;\n\tposition: relative;\n\toverflow: hidden;\n}\n\n.honor-image {\n\twidth: 100%;\n\theight: 70%;\n\tobject-fit: cover;\n}\n\n.honor-info {\n\tposition: absolute;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tbackground: linear-gradient(to top, rgba(0,0,0,0.8), transparent);\n\tpadding: 16rpx;\n\tpadding-top: 32rpx;\n}\n\n.honor-title {\n\tcolor: #ffffff;\n\tfont-size: 24rpx;\n\tfont-weight: 600;\n\tline-height: 1.3;\n}\n\n.add-honor-item {\n\tbackground: rgba(249, 250, 251, 0.8);\n\tborder: 3rpx dashed rgba(156, 163, 175, 0.6);\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tgap: 12rpx;\n\ttransition: all 0.3s ease;\n}\n\n.add-honor-item:active {\n\tborder-color: #6366f1;\n\tbackground: rgba(99, 102, 241, 0.05);\n\ttransform: scale(0.98);\n}\n\n.add-icon {\n\tfont-size: 60rpx;\n\tcolor: #9ca3af;\n\tfont-weight: 300;\n}\n\n.add-text {\n\tfont-size: 26rpx;\n\tcolor: #6b7280;\n\tfont-weight: 500;\n}\n\n/* 弹窗样式 */\n.honor-modal {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: rgba(0, 0, 0, 0.5);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tz-index: 2000;\n\tpadding: 40rpx;\n}\n\n.modal-content {\n\tbackground: #ffffff;\n\tborder-radius: 24rpx;\n\twidth: 100%;\n\tmax-width: 600rpx;\n\tmax-height: 80vh;\n\toverflow: hidden;\n\tbox-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);\n}\n\n.modal-header {\n\tpadding: 32rpx;\n\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.05);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n}\n\n.modal-title {\n\tfont-size: 32rpx;\n\tfont-weight: 700;\n\tcolor: #1f2937;\n}\n\n.close-btn {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tbackground: rgba(239, 68, 68, 0.1);\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.close-icon {\n\tcolor: #ef4444;\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n}\n\n.modal-body {\n\tpadding: 32rpx;\n}\n\n.image-upload {\n\twidth: 100%;\n\theight: 200rpx;\n\tborder-radius: 16rpx;\n\toverflow: hidden;\n\tposition: relative;\n}\n\n.preview-image {\n\twidth: 100%;\n\theight: 100%;\n\tobject-fit: cover;\n}\n\n.upload-placeholder {\n\twidth: 100%;\n\theight: 100%;\n\tbackground: rgba(249, 250, 251, 0.8);\n\tborder: 3rpx dashed rgba(156, 163, 175, 0.6);\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tgap: 12rpx;\n}\n\n.modal-footer {\n\tpadding: 32rpx;\n\tborder-top: 1rpx solid rgba(0, 0, 0, 0.05);\n\tdisplay: flex;\n\tgap: 20rpx;\n}\n\n.cancel-btn, .confirm-btn {\n\tflex: 1;\n\tpadding: 24rpx;\n\tborder-radius: 16rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\ttransition: all 0.3s ease;\n}\n\n.cancel-btn {\n\tbackground: rgba(107, 114, 128, 0.1);\n}\n\n.cancel-btn:active {\n\tbackground: rgba(107, 114, 128, 0.2);\n}\n\n.cancel-text {\n\tcolor: #6b7280;\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n}\n\n.confirm-btn {\n\tbackground: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);\n}\n\n.confirm-btn:active {\n\ttransform: translateY(2rpx);\n}\n\n.confirm-text {\n\tcolor: #ffffff;\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n}\n\n/* 底部操作栏 */\n.bottom-actions {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tbackground: rgba(255, 255, 255, 0.95);\n\tbackdrop-filter: blur(20rpx);\n\tpadding: 32rpx;\n\tpadding-bottom: calc(32rpx + env(safe-area-inset-bottom));\n\tborder-top: 1rpx solid rgba(0, 0, 0, 0.06);\n\tz-index: 1000;\n}\n\n.save-btn {\n\twidth: 100%;\n\tpadding: 36rpx;\n\tbackground: #111827;\n\tborder-radius: 16rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n\ttransition: all 0.2s ease;\n}\n\n.save-btn:active {\n\ttransform: translateY(1rpx);\n\tbox-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.15);\n\tbackground: #000000;\n}\n\n.save-text {\n\tcolor: #ffffff;\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tletter-spacing: -0.5rpx;\n}\n</style>", "import MiniProgramPage from 'D:/NewDemo/unimp/pages/card/company.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAGA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA;AAAA,MAEN,iBAAiB;AAAA;AAAA,MAGjB,mBAAmB;AAAA;AAAA,MAGnB,cAAc;AAAA,QACb,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,MACR;AAAA;AAAA,MAGD,aAAa;AAAA,QACZ,aAAa;AAAA;AAAA,QAEb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,aAAa;AAAA;AAAA,QAEb,QAAQ,CAAE;AAAA;AAAA,QAEV,QAAQ,CAAC;AAAA,MACT;AAAA;AAAA,MAGD,gBAAgB;AAAA,MAChB,cAAc;AAAA,QACb,OAAO;AAAA,QACP,OAAO;AAAA,MACR;AAAA,IACD;AAAA,EACA;AAAA,EAED,UAAU;AAAA,IACT,cAAc;AACb,aAAO;AAAA,QACN,QAAQ,GAAG,KAAK,kBAAkB,EAAE;AAAA,MACrC;AAAA,IACA;AAAA,IACD,YAAY;AACX,aAAO;AAAA,QACN,WAAW,GAAG,KAAK,kBAAkB,EAAE;AAAA,MACxC;AAAA,IACD;AAAA,EACA;AAAA,EAED,SAAS;AACR,SAAK,cAAa;AAClB,SAAK,SAAQ;AAAA,EACb;AAAA,EAED,SAAS;AAAA;AAAA,IAER,gBAAgB;AACf,YAAM,aAAaA,oBAAI;AACvB,WAAK,kBAAkB,WAAW,mBAAmB;AAAA,IACrD;AAAA;AAAA,IAGD,SAAS;AACRA,oBAAG,MAAC,aAAY;AAAA,IAChB;AAAA;AAAA,IAGD,WAAW;AAEVA,oBAAAA,MAAY,MAAA,OAAA,gCAAA,QAAQ;AAAA,IACpB;AAAA;AAAA,IAGD,WAAW;AACVA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAGD,iBAAW,MAAM;AAChBA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACD,GAAE,GAAI;AAAA,IACP;AAAA;AAAA,IAGD,eAAe,GAAG;AACjB,WAAK,YAAY,cAAc,EAAE,OAAO;AAAA,IACxC;AAAA;AAAA,IAGD,aAAa,SAAS;AACrB,WAAK,aAAa,OAAO,IAAI,CAAC,KAAK,aAAa,OAAO;AAAA,IACvD;AAAA;AAAA,IAGD,cAAc;AACbA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AACjB,gBAAM,eAAe,IAAI,cAAc,CAAC;AAExC,eAAK,YAAY,OAAO,KAAK,YAAY;AAAA,QAC1C;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,OAAO;AAClBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChB,iBAAK,YAAY,OAAO,OAAO,OAAO,CAAC;AAAA,UACxC;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,WAAW;AACV,WAAK,eAAe;AAAA,QACnB,OAAO;AAAA,QACP,OAAO;AAAA;AAER,WAAK,iBAAiB;AAAA,IACtB;AAAA;AAAA,IAGD,mBAAmB;AAClBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AACjB,eAAK,aAAa,QAAQ,IAAI,cAAc,CAAC;AAAA,QAC9C;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACd,UAAI,CAAC,KAAK,aAAa,MAAM,KAAI,GAAI;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,UAAI,CAAC,KAAK,aAAa,OAAO;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,WAAK,YAAY,OAAO,KAAK;AAAA,QAC5B,OAAO,KAAK,aAAa;AAAA,QACzB,OAAO,KAAK,aAAa;AAAA,MAC1B,CAAC;AAED,WAAK,gBAAe;AAAA,IACpB;AAAA;AAAA,IAGD,kBAAkB;AACjB,WAAK,iBAAiB;AAAA,IACtB;AAAA;AAAA,IAGD,YAAY,OAAO;AAClBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChB,iBAAK,YAAY,OAAO,OAAO,OAAO,CAAC;AAAA,UACxC;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD;AACD;;ACvMA,GAAG,WAAW,eAAe;"}