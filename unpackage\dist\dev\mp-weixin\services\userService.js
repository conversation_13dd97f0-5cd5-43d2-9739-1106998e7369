"use strict";
const common_vendor = require("../common/vendor.js");
const utils_storage = require("../utils/storage.js");
const utils_request = require("../utils/request.js");
const utils_config = require("../utils/config.js");
class UserService {
  constructor() {
    this.isInitialized = false;
    this.listeners = [];
    this.currentUser = null;
    this.token = null;
  }
  /**
   * 初始化用户服务
   * @returns {Promise<void>}
   */
  async init() {
    if (this.isInitialized)
      return;
    try {
      this.token = utils_storage.getData(utils_config.TOKEN_KEY);
      this.currentUser = utils_storage.getData(utils_config.USER_INFO_KEY);
      this.isInitialized = true;
      if (this.token && !this.currentUser) {
        await this.fetchUserInfo();
      }
    } catch (error) {
      this.logout();
    }
  }
  /**
   * 判断用户是否已登录
   * @returns {boolean} 是否已登录
   */
  isLoggedIn() {
    return !!(this.token && this.currentUser && (this.currentUser.phone || this.currentUser.username));
  }
  /**
   * 获取当前用户信息
   * @returns {Object|null} 用户信息
   */
  getUserInfo() {
    return this.currentUser;
  }
  /**
   * 获取用户Token
   * @returns {string|null} 用户Token
   */
  getToken() {
    return this.token;
  }
  /**
   * 用户登录
   * @param {Object} credentials 登录凭证
   * @param {string} credentials.username 用户名或手机号
   * @param {string} credentials.password 密码
   * @returns {Promise<Object>} 登录结果
   */
  async login(credentials) {
    try {
      const response = await utils_request.http.post(utils_config.API_ENDPOINTS.USER_LOGIN, {
        phone: credentials.username,
        password: credentials.password
      });
      this.token = response.token;
      this.currentUser = response.user;
      if (!this.token || !this.currentUser) {
        throw new Error("登录响应数据格式错误");
      }
      utils_storage.setData(utils_config.TOKEN_KEY, this.token, 7 * 24 * 60 * 60);
      utils_storage.setData(utils_config.USER_INFO_KEY, this.currentUser, 30 * 24 * 60 * 60);
      try {
        await this.fetchUserInfo();
      } catch (fetchError) {
        common_vendor.index.__f__("warn", "at services/userService.js:94", "获取用户信息失败，但登录成功:", fetchError);
      }
      this.notifyListeners();
      return response;
    } catch (error) {
      throw error;
    }
  }
  /**
   * 用户注册
   * @param {Object} userData 用户数据
   * @returns {Promise<Object>} 注册结果
   */
  async register(userData) {
    try {
      const registerResponse = await utils_request.http.post(utils_config.API_ENDPOINTS.USER_REGISTER, {
        phone: userData.phone,
        password: userData.password,
        confirm_password: userData.confirmPassword,
        name: userData.name || "新用户",
        position: userData.position || "请设置职位",
        company: userData.company || "请设置公司"
      });
      this.token = registerResponse.token;
      this.currentUser = registerResponse.user;
      utils_storage.setData(utils_config.TOKEN_KEY, this.token, 7 * 24 * 60 * 60);
      utils_storage.setData(utils_config.USER_INFO_KEY, this.currentUser, 30 * 24 * 60 * 60);
      this.notifyListeners();
      return registerResponse;
    } catch (error) {
      if (error.message.includes("不存在") || error.message.includes("404")) {
        throw new Error("注册接口不存在，请检查API配置");
      }
      throw error;
    }
  }
  /**
   * 保存用户信息
   * @param {Object} userInfo 用户信息
   * @returns {Promise<void>}
   */
  async saveUserInfo(userInfo) {
    try {
      if (this.token) {
        const response = await utils_request.http.post(utils_config.API_ENDPOINTS.USER_UPDATE, userInfo);
        this.currentUser = { ...this.currentUser, ...response };
      } else {
        this.currentUser = { ...this.currentUser, ...userInfo };
      }
      utils_storage.setData(utils_config.USER_INFO_KEY, this.currentUser, 30 * 24 * 60 * 60);
      this.notifyListeners();
    } catch (error) {
      throw error;
    }
  }
  /**
   * 获取最新的用户信息
   * @returns {Promise<Object>} 用户信息
   */
  async fetchUserInfo() {
    try {
      this.token = utils_storage.getData(utils_config.TOKEN_KEY);
      if (!this.token) {
        throw new Error("未登录，无法获取用户信息");
      }
      const response = await utils_request.http.get(utils_config.API_ENDPOINTS.USER_INFO);
      common_vendor.index.__f__("log", "at services/userService.js:192", "fetchUserInfo 后端响应:", response);
      this.currentUser = response.user;
      utils_storage.setData(utils_config.USER_INFO_KEY, response.user, 30 * 24 * 60 * 60);
      if (response.member) {
        common_vendor.index.__f__("log", "at services/userService.js:200", "更新本地会员状态:", response.member);
        common_vendor.index.setStorageSync("isMember", response.member.is_member);
        common_vendor.index.setStorageSync("memberExpireDate", response.member.member_expire_time);
        common_vendor.index.setStorageSync("memberLevel", response.member.member_level);
        this.currentUser.member_info = response.member;
      } else {
        common_vendor.index.__f__("log", "at services/userService.js:208", "后端未返回会员信息");
        common_vendor.index.setStorageSync("isMember", false);
        common_vendor.index.setStorageSync("memberExpireDate", null);
        common_vendor.index.setStorageSync("memberLevel", 0);
      }
      this.notifyListeners();
      return response;
    } catch (error) {
      common_vendor.index.__f__("error", "at services/userService.js:219", "fetchUserInfo 错误:", error);
      throw error;
    }
  }
  /**
   * 检查并同步会员状态
   * @returns {Promise<boolean>}
   */
  async syncMemberStatus() {
    if (!this.isLoggedIn()) {
      return false;
    }
    try {
      await this.fetchUserInfo();
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at services/userService.js:238", "会员状态同步失败:", error);
      return false;
    }
  }
  /**
   * 获取本地会员状态
   * @returns {Object}
   */
  getMemberStatus() {
    return {
      isMember: common_vendor.index.getStorageSync("isMember") || false,
      memberExpireDate: common_vendor.index.getStorageSync("memberExpireDate") || null,
      memberLevel: common_vendor.index.getStorageSync("memberLevel") || 0
    };
  }
  /**
   * 用户登出
   * @returns {Promise<void>}
   */
  async logout() {
    try {
      this.token = null;
      this.currentUser = null;
      utils_storage.removeData(utils_config.TOKEN_KEY);
      utils_storage.removeData(utils_config.USER_INFO_KEY);
      this.notifyListeners();
      return Promise.resolve();
    } catch (error) {
      throw error;
    }
  }
  /**
   * 添加状态变更监听器
   * @param {Function} listener 监听函数
   */
  addListener(listener) {
    if (typeof listener === "function" && !this.listeners.includes(listener)) {
      this.listeners.push(listener);
    }
  }
  /**
   * 移除状态变更监听器
   * @param {Function} listener 监听函数
   */
  removeListener(listener) {
    const index = this.listeners.indexOf(listener);
    if (index !== -1) {
      this.listeners.splice(index, 1);
    }
  }
  /**
   * 通知所有监听器状态变更
   */
  notifyListeners() {
    const data = {
      isLoggedIn: this.isLoggedIn(),
      userInfo: this.currentUser,
      token: this.token
    };
    this.listeners.forEach((listener) => {
      try {
        listener(data);
      } catch (error) {
      }
    });
  }
  // 工具方法
  /**
   * 检查Token是否有效
   * @returns {boolean} Token是否有效
   */
  isTokenValid() {
    if (!this.token)
      return false;
    try {
      const parts = this.token.split(".");
      return parts.length === 3;
    } catch (error) {
      return false;
    }
  }
  /**
   * 微信登录
   * @param {string} code 微信授权码
   * @returns {Promise<Object>} 登录结果
   */
  async wechatLogin(code) {
    try {
      const requestData = { code };
      const response = await utils_request.http.post(utils_config.API_ENDPOINTS.USER_WECHAT_LOGIN, requestData, {
        // 增加超时时间，确保有足够时间处理
        timeout: 15e3
      });
      this.token = response.token;
      this.currentUser = response.user;
      utils_storage.setData(utils_config.TOKEN_KEY, this.token, 7 * 24 * 60 * 60);
      utils_storage.setData(utils_config.USER_INFO_KEY, this.currentUser, 30 * 24 * 60 * 60);
      this.notifyListeners();
      return response;
    } catch (error) {
      let errorMessage = "微信登录失败，请重试";
      let wxErrorCode = 0;
      if (error.data) {
        errorMessage = error.data.message || errorMessage;
        wxErrorCode = error.data.wx_error_code || 0;
      } else if (error.message) {
        errorMessage = error.message;
      }
      const enhancedError = new Error(errorMessage);
      enhancedError.originalError = error;
      enhancedError.wx_error_code = wxErrorCode;
      throw enhancedError;
    }
  }
  /**
   * 微信获取手机号
   * @param {string} code 手机号授权码
   * @returns {Promise<Object>} 获取结果
   */
  async wechatGetPhone(code) {
    try {
      const response = await utils_request.http.post(utils_config.API_ENDPOINTS.USER_WECHAT_GET_PHONE, {
        code
      });
      if (this.currentUser && response.phone) {
        this.currentUser.phone = response.phone;
        utils_storage.setData(utils_config.USER_INFO_KEY, this.currentUser, 30 * 24 * 60 * 60);
        this.notifyListeners();
      }
      return response;
    } catch (error) {
      throw error;
    }
  }
  /**
   * 微信手机号授权登录
   * @param {string} loginCode 微信登录授权码
   * @param {string} phoneCode 微信手机号授权码
   * @returns {Promise<Object>} 登录结果
   */
  async wechatPhoneLogin(loginCode, phoneCode) {
    try {
      if (!loginCode) {
        throw new Error("登录授权码无效");
      }
      if (!phoneCode) {
        throw new Error("手机号授权码无效");
      }
      const requestData = {
        login_code: loginCode,
        phone_code: phoneCode
      };
      const response = await utils_request.http.post(utils_config.API_ENDPOINTS.USER_WECHAT_PHONE_LOGIN, requestData, {
        timeout: 15e3
      });
      this.token = response.token;
      this.currentUser = response.user;
      utils_storage.setData(utils_config.TOKEN_KEY, this.token, 7 * 24 * 60 * 60);
      utils_storage.setData(utils_config.USER_INFO_KEY, this.currentUser, 30 * 24 * 60 * 60);
      this.notifyListeners();
      return response;
    } catch (error) {
      let errorMessage = "微信登录失败，请重试";
      let wxErrorCode = 0;
      if (error.data) {
        errorMessage = error.data.message || errorMessage;
        wxErrorCode = error.data.wx_error_code || 0;
      } else if (error.message) {
        errorMessage = error.message;
      }
      const enhancedError = new Error(errorMessage);
      enhancedError.originalError = error;
      enhancedError.wx_error_code = wxErrorCode;
      throw enhancedError;
    }
  }
  /**
   * 修改密码
   * @param {Object} passwordData 密码数据
   * @returns {Promise<Object>} 修改结果
   */
  async changePassword(passwordData) {
    try {
      if (!this.token) {
        throw new Error("请先登录");
      }
      const response = await utils_request.http.post(utils_config.API_ENDPOINTS.USER_CHANGE_PASSWORD, {
        old_password: passwordData.oldPassword,
        new_password: passwordData.newPassword,
        confirm_password: passwordData.confirmPassword
      });
      return response;
    } catch (error) {
      throw error;
    }
  }
  /**
   * 刷新Token
   * @returns {Promise<Object>} 刷新结果
   */
  async refreshToken() {
    try {
      if (!this.token) {
        throw new Error("请先登录");
      }
      const response = await utils_request.http.post(utils_config.API_ENDPOINTS.USER_REFRESH_TOKEN);
      this.token = response.token;
      utils_storage.setData(utils_config.TOKEN_KEY, this.token, 7 * 24 * 60 * 60);
      return response;
    } catch (error) {
      throw error;
    }
  }
}
const userService = new UserService();
exports.userService = userService;
//# sourceMappingURL=../../.sourcemap/mp-weixin/services/userService.js.map
