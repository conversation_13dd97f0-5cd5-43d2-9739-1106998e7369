# 前后端API对接指南

## 概述

本文档详细说明了电子名片小程序前后端API的对接规范、接口定义、数据格式和错误处理机制。

## 1. API基础规范

### 1.1 基础URL
```
开发环境: https://mp.hwkj01.xin/api/
生产环境: https://mp.hwkj01.xin/api/
```

### 1.2 请求格式
- **Content-Type**: `application/json` 或 `multipart/form-data`（文件上传）
- **认证方式**: Bearer Token
- **请求头**:
  ```
  Authorization: Bearer {access_token}
  Content-Type: application/json
  ```

### 1.3 响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据
  }
}
```

**状态码说明**：
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权（需要登录）
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 2. 用户认证API

### 2.1 微信登录
**接口**: `POST /auth/wechat-login`

**请求参数**:
```json
{
  "code": "微信授权码",
  "userInfo": {
    "nickName": "用户昵称",
    "avatarUrl": "头像URL"
  }
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "访问令牌",
    "expires_in": 604800,
    "user": {
      "id": 1,
      "phone": "手机号",
      "name": "姓名",
      "avatar": "头像URL",
      "custom_style": {
        "backgroundType": "color",
        "backgroundColor": "#ffffff"
      }
    }
  }
}
```

### 2.2 手机号绑定
**接口**: `POST /auth/bind-phone`

**请求参数**:
```json
{
  "phone": "17681106680",
  "code": "验证码"
}
```

## 3. 用户信息API

### 3.1 获取用户信息
**接口**: `GET /user/read`

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "id": 1,
    "name": "张三",
    "phone": "17681106680",
    "email": "<EMAIL>",
    "company": "某某公司",
    "position": "产品经理",
    "avatar": "https://example.com/avatar.jpg",
    "description": "资深产品经理，专注用户体验设计",
    "tags": ["产品经理", "技术专家", "UI设计师"],
    "achievements": ["年度最佳员工", "项目优秀奖", "创新奖"],
    "education": "清华大学计算机系本科",
    "showProfileSection": 1,
    "showDescription": 1,
    "showTags": 1,
    "showAchievements": 1,
    "showEducation": 1,
    "custom_style": {
      "backgroundType": "image",
      "backgroundImage": "https://example.com/bg.jpg",
      "textColor": "#333333",
      "borderRadiusIndex": 1,
      "cardStyleIndex": 0
    }
  }
}
```

### 3.2 更新用户信息
**接口**: `POST /user/save`

**请求参数**:
```json
{
  "name": "张三",
  "phone": "17681106680",
  "email": "<EMAIL>",
  "company": "某某公司",
  "position": "产品经理",
  "address": "北京市朝阳区",
  "description": "个人简介",
  "custom_style": {
    "backgroundType": "gradient",
    "gradientIndex": 2,
    "textColor": "#ffffff"
  }
}
```

### 3.3 更新个人介绍信息
**接口**: `POST /user/save`

**请求参数**:
```json
{
  "tags": ["产品经理", "技术专家", "UI设计师"],
  "achievements": ["年度最佳员工", "项目优秀奖", "创新奖"],
  "education": "清华大学计算机系本科",
  "showProfileSection": 1,
  "showDescription": 1,
  "showTags": 1,
  "showAchievements": 1,
  "showEducation": 1
}
```

**字段说明**:
- `tags`: 个人标签数组，每个标签不超过8个字符
- `achievements`: 个人成就数组，展示个人荣誉和成就
- `education`: 教育背景，文本格式
- `showProfileSection`: 是否显示整个个人介绍板块 (1显示/0隐藏)
- `showDescription`: 是否显示个人描述 (1显示/0隐藏)
- `showTags`: 是否显示个人标签 (1显示/0隐藏)
- `showAchievements`: 是否显示个人成就 (1显示/0隐藏)
- `showEducation`: 是否显示教育背景 (1显示/0隐藏)

**响应数据**:
```json
{
  "code": 200,
  "message": "保存成功"
}
```

**注意事项**:
1. 数组字段（tags, achievements）在后端会自动转换为JSON格式存储
2. 显示控制字段为整型，1表示显示，0表示隐藏
3. 所有字段都是可选的，只传递需要更新的字段即可

## 4. 文件上传API

### 4.1 头像上传
**接口**: `POST /upload/avatar`

**请求格式**: `multipart/form-data`

**请求参数**:
```
avatar: File (图片文件)
```

**响应数据**:
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "url": "https://cdn.example.com/avatars/2025/01/27/user_1_1706345678.jpg"
  }
}
```

**文件命名规则**:
```
avatars/{年}/{月}/{日}/user_{用户ID}_{时间戳}.{扩展名}
```

### 4.2 背景图片上传
**接口**: `POST /upload/background`

**请求格式**: `multipart/form-data`

**请求参数**:
```
background: File (图片文件)
```

**响应数据**:
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "url": "https://cdn.example.com/backgrounds/2025/01/27/user_1_1706345678.jpg"
  }
}
```

**文件命名规则**:
```
backgrounds/{年}/{月}/{日}/user_{用户ID}_{时间戳}.{扩展名}
```

## 5. 会员相关API

### 5.1 获取会员状态
**接口**: `GET /member/status`

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "is_member": true,
    "member_level": 2,
    "member_expire_time": "2030-07-27 17:57:15",
    "days_left": 1827
  }
}
```

### 5.2 会员权限检查
**接口**: `POST /member/check-permission`

**请求参数**:
```json
{
  "feature": "custom_style",
  "level": "enterprise"
}
```

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "has_permission": true,
    "message": "拥有权限"
  }
}
```

## 6. 前端服务层实现

### 6.1 用户服务 (userService.js)

**主要方法**:
```javascript
class UserService {
  // 登录相关
  async login(code, userInfo)
  async bindPhone(phone, code)
  async logout()

  // 状态管理
  isLoggedIn()
  getToken()
  getUserInfo()

  // 数据同步
  async fetchUserInfo()
  async syncMemberStatus()
}
```

### 6.2 用户信息服务 (userInfoService.js)

**主要方法**:
```javascript
class UserInfoService {
  // 数据操作
  async getCurrentUserInfo()
  async saveUserInfo(data)

  // 文件上传
  async uploadAvatar(filePath)
  async uploadBackground(filePath)

  // 验证方法
  validatePhone(phone)
  validateEmail(email)
}
```

### 6.3 HTTP请求封装 (httpService.js)

**请求拦截器**:
```javascript
// 自动添加认证头
request.interceptors.request.use(config => {
  const token = userService.getToken();
  if (token) {
    config.header.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

**响应拦截器**:
```javascript
// 统一错误处理
request.interceptors.response.use(response => {
  if (response.data.code === 401) {
    // Token过期，跳转登录
    userService.logout();
    uni.navigateTo({ url: '/pages/auth/auth' });
  }
  return response;
});
```

## 7. 错误处理规范

### 7.1 前端错误处理

**网络错误**:
```javascript
try {
  const result = await api.call();
} catch (error) {
  if (error.code === 'NETWORK_ERROR') {
    uni.showToast({ title: '网络连接失败，请检查网络', icon: 'none' });
  }
}
```

**业务错误**:
```javascript
if (!result.success) {
  uni.showToast({ title: result.message, icon: 'none' });
  return;
}
```

### 7.2 后端错误响应

**参数验证错误**:
```json
{
  "code": 400,
  "message": "手机号格式不正确",
  "data": {
    "field": "phone",
    "value": "invalid_phone"
  }
}
```

**权限错误**:
```json
{
  "code": 403,
  "message": "该功能需要企业版会员",
  "data": {
    "required_level": "enterprise",
    "current_level": "basic"
  }
}
```

## 8. 数据同步策略

### 8.1 实时同步

**触发时机**:
- 用户信息修改后
- 文件上传成功后
- 会员状态变更后

**实现方式**:
```javascript
// 乐观更新 + 服务器同步
async updateUserInfo(data) {
  // 1. 立即更新本地状态
  this.updateLocalState(data);

  // 2. 同步到服务器
  try {
    await api.saveUserInfo(data);
  } catch (error) {
    // 3. 失败时回滚本地状态
    this.rollbackLocalState();
    throw error;
  }
}
```

### 8.2 定期同步

**同步时机**:
- 应用启动时
- 从后台切换到前台时
- 页面显示时

**实现方式**:
```javascript
// 静默同步，不影响用户体验
async silentSync() {
  try {
    const serverData = await api.getUserInfo();
    this.mergeServerData(serverData);
  } catch (error) {
    // 静默失败，不提示用户
    console.error('同步失败:', error);
  }
}
```

## 9. 性能优化

### 9.1 请求优化

**请求合并**:
```javascript
// 避免重复请求
const pendingRequests = new Map();

async function request(url, options) {
  const key = `${url}_${JSON.stringify(options)}`;

  if (pendingRequests.has(key)) {
    return pendingRequests.get(key);
  }

  const promise = fetch(url, options);
  pendingRequests.set(key, promise);

  try {
    return await promise;
  } finally {
    pendingRequests.delete(key);
  }
}
```

**缓存策略**:
```javascript
// 短期缓存非实时数据
const cache = new Map();

async function getCachedData(key, fetcher, ttl = 5000) {
  const cached = cache.get(key);

  if (cached && Date.now() - cached.timestamp < ttl) {
    return cached.data;
  }

  const data = await fetcher();
  cache.set(key, { data, timestamp: Date.now() });

  return data;
}
```

### 9.2 文件上传优化

**压缩上传**:
```javascript
// 图片压缩后上传
async function uploadImage(filePath) {
  const compressedPath = await compressImage(filePath, {
    quality: 0.8,
    maxWidth: 1200
  });

  return await uploadFile(compressedPath);
}
```

**进度显示**:
```javascript
// 上传进度反馈
uni.uploadFile({
  url: uploadUrl,
  filePath: filePath,
  name: 'file',
  success: (res) => {
    // 处理成功
  },
  fail: (err) => {
    // 处理失败
  }
});
```

## 10. 安全考虑

### 10.1 Token安全

- **存储安全**: 使用加密存储敏感信息
- **传输安全**: HTTPS协议传输
- **过期处理**: 自动刷新或重新登录

### 10.2 数据验证

- **前端验证**: 用户体验优化
- **后端验证**: 安全保障
- **双重验证**: 关键操作二次确认

### 10.3 文件上传安全

- **类型限制**: 只允许图片格式
- **大小限制**: 限制文件大小
- **内容检查**: 服务器端文件内容验证

## 11. ThinkAdmin框架特殊说明

### 11.1 模型字段配置

**重要**: ThinkAdmin框架使用严格的字段白名单机制，所有需要保存的字段必须在模型的 `$schema` 中定义。

**BusinessUser模型配置**:
```php
// server/app/common/model/BusinessUser.php
protected $schema = [
    'id'          => 'int',
    'name'        => 'string',
    'phone'       => 'string',
    'email'       => 'string',
    'company'     => 'string',
    'position'    => 'string',
    'address'     => 'string',
    'description' => 'text',
    'avatar'      => 'string',
    // 个人介绍字段
    'tags'        => 'text',
    'achievements'=> 'text',
    'education'   => 'string',
    // 显示控制开关
    'showProfileSection' => 'int',
    'showDescription' => 'int',
    'showTags'    => 'int',
    'showAchievements' => 'int',
    'showEducation' => 'int',
    // 其他字段...
];
```

### 11.2 JSON字段处理

**数组字段的特殊处理**:
```php
// 在控制器中手动处理JSON编码
if (in_array($field, ['tags', 'achievements', 'advantages']) && is_array($data[$field])) {
    $updateData[$field] = json_encode($data[$field], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}
```

**注意事项**:
1. 不要在模型中使用 `protected $json = []` 配置，会与手动编码冲突
2. 使用 `JSON_UNESCAPED_UNICODE` 确保中文正确显示
3. 数组字段在数据库中以JSON字符串格式存储

### 11.3 字段类型转换

```php
protected $type = [
    'gender' => 'integer',
    'member_level' => 'integer',
    'status' => 'integer',
    'showProfileSection' => 'integer',
    'showDescription' => 'integer',
    'showTags' => 'integer',
    'showAchievements' => 'integer',
    'showEducation' => 'integer',
];
```

### 11.4 新增字段的标准流程

1. **添加数据库字段**:
   ```sql
   ALTER TABLE `bc_users`
   ADD COLUMN `new_field` varchar(255) DEFAULT NULL COMMENT '新字段';
   ```

2. **更新模型定义**:
   ```php
   protected $schema = [
       // 现有字段...
       'new_field' => 'string',
   ];
   ```

3. **配置字段类型**（如需要）:
   ```php
   protected $type = [
       // 现有配置...
       'new_field' => 'string',
   ];
   ```

4. **测试保存功能**:
   确保新字段能正确保存和读取

## 12. 总结

本文档涵盖了电子名片小程序前后端API对接的所有关键点，包括认证、用户管理、文件上传、会员系统、个人介绍功能、错误处理、数据同步和ThinkAdmin框架特殊配置等方面。

**关键要点**:
1. 统一的API响应格式
2. 完善的错误处理机制
3. 安全的认证体系
4. 高效的数据同步策略
5. ThinkAdmin框架的字段白名单机制
6. JSON字段的正确处理方式
7. 个人介绍功能的完整实现
8. 良好的用户体验设计

**个人介绍功能特色**:
- 支持个人标签、成就、教育背景的管理
- 灵活的显示控制开关
- 中文JSON数据的正确存储和显示
- 与ThinkAdmin框架的完美集成

遵循本指南可以确保前后端对接的稳定性、安全性和可维护性。