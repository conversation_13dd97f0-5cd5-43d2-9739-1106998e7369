
/* 页面容器 */
.company-page.data-v-becff3b5 {
	min-height: 100vh;
	background: #fafbfc;
	position: relative;
}

/* 自定义导航栏 */
.custom-navbar.data-v-becff3b5 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	background: rgba(255, 255, 255, 0.98);
	-webkit-backdrop-filter: blur(20rpx);
	        backdrop-filter: blur(20rpx);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
	padding: 20rpx 40rpx 24rpx 40rpx;
	box-sizing: border-box;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);
}
.navbar-left.data-v-becff3b5 {
	display: flex;
	align-items: center;
}
.back-btn.data-v-becff3b5 {
	padding: 12rpx 20rpx;
	background: transparent;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
	transition: all 0.2s ease;
}
.back-btn.data-v-becff3b5:active {
	background: rgba(0, 0, 0, 0.04);
}
.back-icon.data-v-becff3b5 {
	font-size: 32rpx;
	color: #1f2937;
	font-weight: 500;
}
.back-text.data-v-becff3b5 {
	font-size: 28rpx;
	color: #1f2937;
	font-weight: 500;
}
.navbar-title.data-v-becff3b5 {
	color: #111827;
	font-size: 34rpx;
	font-weight: 600;
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	letter-spacing: -0.5rpx;
}

/* 主要内容 */
.main-content.data-v-becff3b5 {
	padding: 40rpx 32rpx;
	padding-bottom: calc(160rpx + env(safe-area-inset-bottom));
	position: relative;
	z-index: 1;
}

/* 企业版提示卡片 */
.premium-card.data-v-becff3b5 {
	background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
	border-radius: 20rpx;
	padding: 40rpx;
	display: flex;
	align-items: center;
	gap: 32rpx;
	margin-bottom: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.1);
}
.premium-icon.data-v-becff3b5 {
	width: 88rpx;
	height: 88rpx;
	background: rgba(255, 255, 255, 0.15);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 44rpx;
}
.premium-info.data-v-becff3b5 {
	flex: 1;
}
.premium-title.data-v-becff3b5 {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
	margin-bottom: 8rpx;
}
.premium-desc.data-v-becff3b5 {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.7);
	line-height: 1.4;
}
.upgrade-btn.data-v-becff3b5 {
	padding: 20rpx 32rpx;
	background: rgba(255, 255, 255, 0.15);
	border-radius: 12rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	transition: all 0.2s ease;
}
.upgrade-btn.data-v-becff3b5:active {
	background: rgba(255, 255, 255, 0.25);
}
.upgrade-text.data-v-becff3b5 {
	color: #ffffff;
	font-size: 28rpx;
	font-weight: 500;
}

/* 卡片样式 */
.switch-card.data-v-becff3b5, .info-card.data-v-becff3b5, .media-card.data-v-becff3b5, .honor-card.data-v-becff3b5 {
	background: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	border: 1rpx solid rgba(0, 0, 0, 0.06);
	overflow: hidden;
	transition: all 0.2s ease;
}
.switch-card.data-v-becff3b5:active, .info-card.data-v-becff3b5:active, .media-card.data-v-becff3b5:active, .honor-card.data-v-becff3b5:active {
	transform: translateY(-1rpx);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
.disabled.data-v-becff3b5 {
	opacity: 0.4;
	pointer-events: none;
}
.card-header.data-v-becff3b5 {
	padding: 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);
}
.header-info.data-v-becff3b5 {
	flex: 1;
}
.card-title.data-v-becff3b5 {
	font-size: 32rpx;
	font-weight: 600;
	color: #111827;
	margin-bottom: 8rpx;
	letter-spacing: -0.3rpx;
}
.card-desc.data-v-becff3b5 {
	font-size: 26rpx;
	color: #6b7280;
	line-height: 1.4;
	font-weight: 400;
}
.expand-btn.data-v-becff3b5 {
	width: 56rpx;
	height: 56rpx;
	background: rgba(0, 0, 0, 0.04);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s ease;
}
.expand-btn.expanded.data-v-becff3b5 {
	transform: rotate(90deg);
	background: rgba(0, 0, 0, 0.08);
}
.expand-icon.data-v-becff3b5 {
	font-size: 24rpx;
	color: #6b7280;
	font-weight: 600;
}
.card-content.data-v-becff3b5 {
	padding: 40rpx;
	padding-top: 0;
}

/* 表单样式 */
.input-group.data-v-becff3b5 {
	margin-bottom: 40rpx;
}
.input-header.data-v-becff3b5 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20rpx;
}
.input-label.data-v-becff3b5 {
	font-size: 28rpx;
	font-weight: 500;
	color: #111827;
	letter-spacing: -0.3rpx;
}
.modern-input.data-v-becff3b5, .modern-textarea.data-v-becff3b5 {
	width: 100%;
	height: 96rpx;
	padding: 0 28rpx;
	background: #fafbfc;
	border: 1rpx solid #e5e7eb;
	border-radius: 12rpx;
	font-size: 28rpx;
	line-height: 96rpx;
	color: #111827;
	transition: all 0.2s ease;
	box-sizing: border-box;
}
.modern-input.data-v-becff3b5:focus, .modern-textarea.data-v-becff3b5:focus {
	outline: none;
	border-color: #111827;
	background: #ffffff;
	box-shadow: 0 0 0 4rpx rgba(17, 24, 39, 0.04);
}
.modern-input.data-v-becff3b5::-webkit-input-placeholder, .modern-textarea.data-v-becff3b5::-webkit-input-placeholder {
	color: #9ca3af;
	font-size: 28rpx;
}
.modern-input.data-v-becff3b5::placeholder, .modern-textarea.data-v-becff3b5::placeholder {
	color: #9ca3af;
	font-size: 28rpx;
}
.modern-textarea.data-v-becff3b5 {
	height: auto;
	min-height: 160rpx;
	line-height: 1.6;
	padding: 28rpx;
	resize: none;
}

/* 媒体网格 */
.media-grid.data-v-becff3b5, .honor-grid.data-v-becff3b5 {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}
.media-item.data-v-becff3b5, .upload-item.data-v-becff3b5 {
	width: calc((100% - 40rpx) / 3);
	aspect-ratio: 1;
	border-radius: 16rpx;
	position: relative;
	overflow: hidden;
}
.media-preview.data-v-becff3b5 {
	width: 100%;
	height: 100%;
	object-fit: cover;
}
.upload-item.data-v-becff3b5 {
	background: rgba(249, 250, 251, 0.8);
	border: 3rpx dashed rgba(156, 163, 175, 0.6);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	transition: all 0.3s ease;
}
.upload-item.data-v-becff3b5:active {
	border-color: #6366f1;
	background: rgba(99, 102, 241, 0.05);
	transform: scale(0.98);
}
.upload-icon.data-v-becff3b5 {
	font-size: 48rpx;
	color: #9ca3af;
	font-weight: 300;
}
.upload-text.data-v-becff3b5 {
	font-size: 24rpx;
	color: #6b7280;
	font-weight: 500;
}
.delete-btn.data-v-becff3b5 {
	position: absolute;
	top: 8rpx;
	right: 8rpx;
	width: 48rpx;
	height: 48rpx;
	background: rgba(239, 68, 68, 0.9);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}
.delete-btn.data-v-becff3b5:active {
	transform: scale(0.9);
}
.delete-icon.data-v-becff3b5 {
	color: #ffffff;
	font-size: 28rpx;
	font-weight: bold;
}

/* 荣誉样式 */
.honor-item.data-v-becff3b5, .add-honor-item.data-v-becff3b5 {
	width: calc((100% - 20rpx) / 2);
	aspect-ratio: 1;
	border-radius: 16rpx;
	position: relative;
	overflow: hidden;
}
.honor-image.data-v-becff3b5 {
	width: 100%;
	height: 70%;
	object-fit: cover;
}
.honor-info.data-v-becff3b5 {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
	padding: 16rpx;
	padding-top: 32rpx;
}
.honor-title.data-v-becff3b5 {
	color: #ffffff;
	font-size: 24rpx;
	font-weight: 600;
	line-height: 1.3;
}
.add-honor-item.data-v-becff3b5 {
	background: rgba(249, 250, 251, 0.8);
	border: 3rpx dashed rgba(156, 163, 175, 0.6);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	transition: all 0.3s ease;
}
.add-honor-item.data-v-becff3b5:active {
	border-color: #6366f1;
	background: rgba(99, 102, 241, 0.05);
	transform: scale(0.98);
}
.add-icon.data-v-becff3b5 {
	font-size: 60rpx;
	color: #9ca3af;
	font-weight: 300;
}
.add-text.data-v-becff3b5 {
	font-size: 26rpx;
	color: #6b7280;
	font-weight: 500;
}

/* 弹窗样式 */
.honor-modal.data-v-becff3b5 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 2000;
	padding: 40rpx;
}
.modal-content.data-v-becff3b5 {
	background: #ffffff;
	border-radius: 24rpx;
	width: 100%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}
.modal-header.data-v-becff3b5 {
	padding: 32rpx;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.modal-title.data-v-becff3b5 {
	font-size: 32rpx;
	font-weight: 700;
	color: #1f2937;
}
.close-btn.data-v-becff3b5 {
	width: 60rpx;
	height: 60rpx;
	background: rgba(239, 68, 68, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}
.close-icon.data-v-becff3b5 {
	color: #ef4444;
	font-size: 32rpx;
	font-weight: bold;
}
.modal-body.data-v-becff3b5 {
	padding: 32rpx;
}
.image-upload.data-v-becff3b5 {
	width: 100%;
	height: 200rpx;
	border-radius: 16rpx;
	overflow: hidden;
	position: relative;
}
.preview-image.data-v-becff3b5 {
	width: 100%;
	height: 100%;
	object-fit: cover;
}
.upload-placeholder.data-v-becff3b5 {
	width: 100%;
	height: 100%;
	background: rgba(249, 250, 251, 0.8);
	border: 3rpx dashed rgba(156, 163, 175, 0.6);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
}
.modal-footer.data-v-becff3b5 {
	padding: 32rpx;
	border-top: 1rpx solid rgba(0, 0, 0, 0.05);
	display: flex;
	gap: 20rpx;
}
.cancel-btn.data-v-becff3b5, .confirm-btn.data-v-becff3b5 {
	flex: 1;
	padding: 24rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}
.cancel-btn.data-v-becff3b5 {
	background: rgba(107, 114, 128, 0.1);
}
.cancel-btn.data-v-becff3b5:active {
	background: rgba(107, 114, 128, 0.2);
}
.cancel-text.data-v-becff3b5 {
	color: #6b7280;
	font-size: 28rpx;
	font-weight: 600;
}
.confirm-btn.data-v-becff3b5 {
	background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
}
.confirm-btn.data-v-becff3b5:active {
	transform: translateY(2rpx);
}
.confirm-text.data-v-becff3b5 {
	color: #ffffff;
	font-size: 28rpx;
	font-weight: 600;
}

/* 底部操作栏 */
.bottom-actions.data-v-becff3b5 {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: rgba(255, 255, 255, 0.95);
	-webkit-backdrop-filter: blur(20rpx);
	        backdrop-filter: blur(20rpx);
	padding: 32rpx;
	padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
	border-top: 1rpx solid rgba(0, 0, 0, 0.06);
	z-index: 1000;
}
.save-btn.data-v-becff3b5 {
	width: 100%;
	padding: 36rpx;
	background: #111827;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	transition: all 0.2s ease;
}
.save-btn.data-v-becff3b5:active {
	transform: translateY(1rpx);
	box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.15);
	background: #000000;
}
.save-text.data-v-becff3b5 {
	color: #ffffff;
	font-size: 32rpx;
	font-weight: 600;
	letter-spacing: -0.5rpx;
}
