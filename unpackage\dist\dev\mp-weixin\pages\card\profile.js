"use strict";
const common_vendor = require("../../common/vendor.js");
const services_userService = require("../../services/userService.js");
const services_userInfoService = require("../../services/userInfoService.js");
const _sfc_main = {
  data() {
    return {
      statusBarHeight: 44,
      // 状态栏高度
      pageType: "personal",
      // 页面类型：personal 或 company
      userInfo: {
        description: "",
        tags: [],
        achievements: [],
        education: "",
        // 显示控制开关
        showProfileSection: true,
        // 总开关
        showDescription: true,
        showTags: true,
        showAchievements: true,
        showEducation: true
      },
      companyInfo: {
        companyName: "",
        companyDesc: "",
        companySlogan: "",
        advantages: [],
        companyAddress: "",
        // 显示控制开关
        showCompanySection: true,
        // 总开关
        showCompanyName: true,
        showCompanyDesc: true,
        showCompanySlogan: true,
        showCompanyAdvantages: true,
        showCompanyAddress: true
      },
      tagInput: "",
      achievementInput: "",
      // 弹窗控制
      showTagPopup: false,
      showAchievementPopup: false,
      // 企业优势弹窗控制
      showAdvantagePopup: false,
      advantageInput: ""
    };
  },
  computed: {
    navbarStyle() {
      return {
        height: `${this.statusBarHeight + 45}px`
      };
    },
    mainStyle() {
      return {
        marginTop: `${this.statusBarHeight + 55}px`
      };
    }
  },
  async onLoad(option) {
    this.setStatusBarHeight();
    if (option && option.type === "company") {
      this.pageType = "company";
      await this.loadCompanyInfo();
    } else {
      this.pageType = "personal";
      await this.loadUserInfo();
    }
  },
  onShow() {
  },
  methods: {
    async loadUserInfo() {
      if (services_userService.userService.isLoggedIn()) {
        try {
          const result = await services_userInfoService.userInfoService.getCurrentUserInfo();
          if (result.success && result.data) {
            const serverData = result.data;
            this.userInfo = {
              description: serverData.description || this.userInfo.description || "",
              tags: serverData.tags || this.userInfo.tags || [],
              achievements: serverData.achievements || this.userInfo.achievements || [],
              education: serverData.education || this.userInfo.education || "",
              // 显示控制开关，优先使用服务器数据
              showProfileSection: serverData.showProfileSection !== void 0 ? serverData.showProfileSection : this.userInfo.showProfileSection !== void 0 ? this.userInfo.showProfileSection : true,
              showDescription: serverData.showDescription !== void 0 ? serverData.showDescription : this.userInfo.showDescription !== void 0 ? this.userInfo.showDescription : true,
              showTags: serverData.showTags !== void 0 ? serverData.showTags : this.userInfo.showTags !== void 0 ? this.userInfo.showTags : true,
              showAchievements: serverData.showAchievements !== void 0 ? serverData.showAchievements : this.userInfo.showAchievements !== void 0 ? this.userInfo.showAchievements : true,
              showEducation: serverData.showEducation !== void 0 ? serverData.showEducation : this.userInfo.showEducation !== void 0 ? this.userInfo.showEducation : true
            };
            const currentUserInfo = common_vendor.index.getStorageSync("userInfo") || {};
            const updatedUserInfo = {
              ...currentUserInfo,
              ...this.userInfo
            };
            common_vendor.index.setStorageSync("userInfo", updatedUserInfo);
            return;
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/card/profile.vue:565", "从服务器加载个人介绍数据失败:", error);
        }
      }
      this.loadUserInfoFromLocal();
    },
    loadUserInfoFromLocal() {
      const savedInfo = common_vendor.index.getStorageSync("userInfo") || {};
      this.userInfo = {
        description: savedInfo.description || this.userInfo.description || "",
        tags: savedInfo.tags || this.userInfo.tags || [],
        achievements: savedInfo.achievements || this.userInfo.achievements || [],
        education: savedInfo.education || this.userInfo.education || "",
        // 显示控制开关，默认为true，如果已保存则使用保存的值
        showProfileSection: savedInfo.showProfileSection !== void 0 ? savedInfo.showProfileSection : this.userInfo.showProfileSection !== void 0 ? this.userInfo.showProfileSection : true,
        showDescription: savedInfo.showDescription !== void 0 ? savedInfo.showDescription : this.userInfo.showDescription !== void 0 ? this.userInfo.showDescription : true,
        showTags: savedInfo.showTags !== void 0 ? savedInfo.showTags : this.userInfo.showTags !== void 0 ? this.userInfo.showTags : true,
        showAchievements: savedInfo.showAchievements !== void 0 ? savedInfo.showAchievements : this.userInfo.showAchievements !== void 0 ? this.userInfo.showAchievements : true,
        showEducation: savedInfo.showEducation !== void 0 ? savedInfo.showEducation : this.userInfo.showEducation !== void 0 ? this.userInfo.showEducation : true
      };
    },
    async loadCompanyInfo() {
      if (services_userService.userService.isLoggedIn()) {
        try {
          const result = await services_userInfoService.userInfoService.getCurrentUserInfo();
          if (result.success && result.data) {
            const serverData = result.data;
            this.companyInfo = {
              companyName: serverData.companyName || "",
              companyDesc: serverData.companyDesc || "",
              companySlogan: serverData.companySlogan || "",
              advantages: serverData.advantages || [],
              companyAddress: serverData.companyAddress || "",
              // 显示控制开关，默认为true，如果已保存则使用保存的值
              showCompanySection: serverData.showCompanySection !== void 0 ? serverData.showCompanySection : true,
              showCompanyName: serverData.showCompanyName !== void 0 ? serverData.showCompanyName : true,
              showCompanyDesc: serverData.showCompanyDesc !== void 0 ? serverData.showCompanyDesc : true,
              showCompanySlogan: serverData.showCompanySlogan !== void 0 ? serverData.showCompanySlogan : true,
              showCompanyAdvantages: serverData.showCompanyAdvantages !== void 0 ? serverData.showCompanyAdvantages : true,
              showCompanyAddress: serverData.showCompanyAddress !== void 0 ? serverData.showCompanyAddress : true
            };
            const currentUserInfo = common_vendor.index.getStorageSync("userInfo") || {};
            const updatedUserInfo = {
              ...currentUserInfo,
              ...this.companyInfo
            };
            common_vendor.index.setStorageSync("userInfo", updatedUserInfo);
            return;
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/card/profile.vue:627", "从服务器加载企业介绍数据失败:", error);
        }
      }
      this.loadCompanyInfoFromLocal();
    },
    loadCompanyInfoFromLocal() {
      const savedInfo = common_vendor.index.getStorageSync("userInfo") || {};
      this.companyInfo = {
        companyName: savedInfo.companyName || "",
        companyDesc: savedInfo.companyDesc || "",
        companySlogan: savedInfo.companySlogan || "",
        advantages: savedInfo.advantages || [],
        companyAddress: savedInfo.companyAddress || "",
        // 显示控制开关，默认为true，如果已保存则使用保存的值
        showCompanySection: savedInfo.showCompanySection !== void 0 ? savedInfo.showCompanySection : true,
        showCompanyName: savedInfo.showCompanyName !== void 0 ? savedInfo.showCompanyName : true,
        showCompanyDesc: savedInfo.showCompanyDesc !== void 0 ? savedInfo.showCompanyDesc : true,
        showCompanySlogan: savedInfo.showCompanySlogan !== void 0 ? savedInfo.showCompanySlogan : true,
        showCompanyAdvantages: savedInfo.showCompanyAdvantages !== void 0 ? savedInfo.showCompanyAdvantages : true,
        showCompanyAddress: savedInfo.showCompanyAddress !== void 0 ? savedInfo.showCompanyAddress : true
      };
    },
    setStatusBarHeight() {
      try {
        const windowInfo = common_vendor.index.getWindowInfo();
        const statusBarHeight = windowInfo.statusBarHeight || 0;
        this.statusBarHeight = statusBarHeight;
      } catch (error) {
        common_vendor.index.__f__("log", "at pages/card/profile.vue:662", "获取状态栏高度失败:", error);
        this.statusBarHeight = 44;
      }
    },
    goBack() {
      common_vendor.index.navigateBack();
    },
    showAddTagModal() {
      this.tagInput = "";
      this.showTagPopup = true;
      if (!this.userInfo.tags) {
        this.userInfo.tags = [];
      }
    },
    closeTagPopup() {
      this.showTagPopup = false;
    },
    onTagInputChange(e) {
      this.tagInput = e.detail.value;
    },
    confirmAddTag() {
      const value = this.tagInput;
      if (value && value.trim() && value.trim().length > 0 && value.trim().length <= 8) {
        if (!this.userInfo.tags.includes(value.trim())) {
          this.userInfo.tags.push(value.trim());
          this.closeTagPopup();
          common_vendor.index.showToast({
            title: "标签添加成功",
            icon: "success"
          });
        } else {
          common_vendor.index.showToast({
            title: "标签已存在",
            icon: "none"
          });
        }
      } else if (value && value.trim().length > 8) {
        common_vendor.index.showToast({
          title: "标签不能超过8个字",
          icon: "none"
        });
      } else {
        common_vendor.index.showToast({
          title: "请输入标签内容",
          icon: "none"
        });
      }
    },
    deleteTag(index) {
      this.userInfo.tags.splice(index, 1);
    },
    showAddAchievementModal() {
      this.achievementInput = "";
      this.showAchievementPopup = true;
    },
    closeAchievementPopup() {
      this.showAchievementPopup = false;
    },
    confirmAddAchievement() {
      const value = this.achievementInput;
      if (value && value.trim() && value.length <= 20) {
        this.userInfo.achievements.push(value.trim());
        this.closeAchievementPopup();
      } else if (value.length > 20) {
        common_vendor.index.showToast({
          title: "成就内容不能超过20个字",
          icon: "none"
        });
      } else {
        common_vendor.index.showToast({
          title: "请输入成就内容",
          icon: "none"
        });
      }
    },
    deleteAchievement(index) {
      this.userInfo.achievements.splice(index, 1);
    },
    showAddAdvantageModal() {
      this.advantageInput = "";
      this.showAdvantagePopup = true;
    },
    closeAdvantagePopup() {
      this.showAdvantagePopup = false;
    },
    confirmAddAdvantage() {
      const value = this.advantageInput;
      if (value && value.trim() && value.length <= 10) {
        if (!this.companyInfo.advantages.includes(value.trim())) {
          this.companyInfo.advantages.push(value.trim());
          this.closeAdvantagePopup();
        } else {
          common_vendor.index.showToast({
            title: "标签已存在",
            icon: "none"
          });
        }
      } else if (value.length > 10) {
        common_vendor.index.showToast({
          title: "标签不能超过10个字",
          icon: "none"
        });
      } else {
        common_vendor.index.showToast({
          title: "请输入标签内容",
          icon: "none"
        });
      }
    },
    deleteAdvantage(index) {
      this.companyInfo.advantages.splice(index, 1);
    },
    async saveProfile() {
      if (!services_userService.userService.isLoggedIn()) {
        common_vendor.index.showModal({
          title: "需要登录",
          content: "保存功能需要登录后才能使用，是否前往登录？",
          confirmText: "去登录",
          cancelText: "取消",
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.navigateTo({
                url: "/pages/auth/auth"
              });
            }
          }
        });
        return;
      }
      const isMember = common_vendor.index.getStorageSync("isMember");
      const memberLevel = common_vendor.index.getStorageSync("memberLevel") || 0;
      const memberExpireDate = common_vendor.index.getStorageSync("memberExpireDate");
      let isExpired = false;
      if (memberExpireDate) {
        const now = /* @__PURE__ */ new Date();
        const formattedDate = memberExpireDate.replace(/\s/g, "T");
        const expireDate = new Date(formattedDate);
        isExpired = now > expireDate;
      }
      const hasPermission = isMember && memberLevel >= 1 && !isExpired;
      if (!hasPermission) {
        common_vendor.index.showModal({
          title: "专业版功能",
          content: this.pageType === "company" ? "企业介绍保存功能需要专业版或企业版权限" : "个人介绍保存功能需要专业版或企业版权限",
          confirmText: "立即升级",
          cancelText: "取消",
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.navigateTo({
                url: "/pages/company/premium"
              });
            }
          }
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "保存中...",
        mask: true
      });
      try {
        const currentUserInfo = common_vendor.index.getStorageSync("userInfo") || {};
        let updatedUserInfo;
        if (this.pageType === "company") {
          updatedUserInfo = {
            ...currentUserInfo,
            companyName: this.companyInfo.companyName,
            companyDesc: this.companyInfo.companyDesc,
            companySlogan: this.companyInfo.companySlogan,
            advantages: this.companyInfo.advantages,
            companyAddress: this.companyInfo.companyAddress,
            // 保存显示控制开关状态
            showCompanySection: this.companyInfo.showCompanySection,
            showCompanyName: this.companyInfo.showCompanyName,
            showCompanyDesc: this.companyInfo.showCompanyDesc,
            showCompanySlogan: this.companyInfo.showCompanySlogan,
            showCompanyAdvantages: this.companyInfo.showCompanyAdvantages,
            showCompanyAddress: this.companyInfo.showCompanyAddress
          };
        } else {
          updatedUserInfo = {
            ...currentUserInfo,
            description: this.userInfo.description,
            tags: this.userInfo.tags,
            achievements: this.userInfo.achievements,
            education: this.userInfo.education,
            // 保存显示控制开关状态
            showProfileSection: this.userInfo.showProfileSection,
            showDescription: this.userInfo.showDescription,
            showTags: this.userInfo.showTags,
            showAchievements: this.userInfo.showAchievements,
            showEducation: this.userInfo.showEducation
          };
        }
        common_vendor.index.setStorageSync("userInfo", updatedUserInfo);
        const result = await services_userInfoService.userInfoService.updateUserInfo(updatedUserInfo);
        if (result.success) {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success",
            duration: 2e3,
            success: () => {
              setTimeout(() => {
                common_vendor.index.navigateBack();
              }, 1500);
            }
          });
        } else {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: result.message || "保存失败，请重试",
            icon: "none",
            duration: 3e3
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/card/profile.vue:928", "保存个人介绍失败:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "保存失败，请检查网络连接",
          icon: "none",
          duration: 3e3
        });
      }
    }
  }
};
const __injectCSSVars__ = () => {
  common_vendor.useCssVars((_ctx) => ({
    "576124a2": _ctx.pageType === "company" ? "#ec4aa9" : "#4f46e5"
  }));
};
const __setup__ = _sfc_main.setup;
_sfc_main.setup = __setup__ ? (props, ctx) => {
  __injectCSSVars__();
  return __setup__(props, ctx);
} : __injectCSSVars__;
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.t($data.pageType === "company" ? "企业介绍" : "个人介绍"),
    c: common_vendor.s($options.navbarStyle),
    d: $data.pageType === "personal"
  }, $data.pageType === "personal" ? common_vendor.e({
    e: $data.userInfo.showProfileSection,
    f: common_vendor.o((e) => $data.userInfo.showProfileSection = e.detail.value),
    g: common_vendor.t($data.userInfo.showDescription ? "显示" : "隐藏"),
    h: $data.userInfo.showDescription,
    i: common_vendor.o((e) => $data.userInfo.showDescription = e.detail.value),
    j: !$data.userInfo.showProfileSection,
    k: $data.userInfo.showDescription && $data.userInfo.showProfileSection
  }, $data.userInfo.showDescription && $data.userInfo.showProfileSection ? {
    l: !$data.userInfo.showProfileSection,
    m: $data.userInfo.description,
    n: common_vendor.o(($event) => $data.userInfo.description = $event.detail.value),
    o: common_vendor.t($data.userInfo.description ? $data.userInfo.description.length : 0)
  } : !$data.userInfo.showDescription && $data.userInfo.showProfileSection ? {} : {}, {
    p: !$data.userInfo.showDescription && $data.userInfo.showProfileSection,
    q: !$data.userInfo.showProfileSection ? 1 : "",
    r: common_vendor.t($data.userInfo.showTags ? "显示" : "隐藏"),
    s: $data.userInfo.showTags,
    t: common_vendor.o((e) => $data.userInfo.showTags = e.detail.value),
    v: !$data.userInfo.showProfileSection,
    w: $data.userInfo.showTags && $data.userInfo.showProfileSection
  }, $data.userInfo.showTags && $data.userInfo.showProfileSection ? common_vendor.e({
    x: common_vendor.f($data.userInfo.tags, (tag, index, i0) => {
      return {
        a: common_vendor.t(tag),
        b: common_vendor.o(($event) => $options.deleteTag(index), index),
        c: index
      };
    }),
    y: $data.userInfo.tags.length < 6
  }, $data.userInfo.tags.length < 6 ? {
    z: common_vendor.o((...args) => $options.showAddTagModal && $options.showAddTagModal(...args))
  } : {}) : !$data.userInfo.showTags && $data.userInfo.showProfileSection ? {} : {}, {
    A: !$data.userInfo.showTags && $data.userInfo.showProfileSection,
    B: !$data.userInfo.showProfileSection ? 1 : "",
    C: common_vendor.t($data.userInfo.showAchievements ? "显示" : "隐藏"),
    D: $data.userInfo.showAchievements,
    E: common_vendor.o((e) => $data.userInfo.showAchievements = e.detail.value),
    F: !$data.userInfo.showProfileSection,
    G: $data.userInfo.showAchievements && $data.userInfo.showProfileSection
  }, $data.userInfo.showAchievements && $data.userInfo.showProfileSection ? common_vendor.e({
    H: common_vendor.f($data.userInfo.achievements, (achievement, index, i0) => {
      return {
        a: common_vendor.t(achievement),
        b: common_vendor.o(($event) => $options.deleteAchievement(index), index),
        c: index
      };
    }),
    I: $data.userInfo.achievements.length < 3
  }, $data.userInfo.achievements.length < 3 ? {
    J: common_vendor.o((...args) => $options.showAddAchievementModal && $options.showAddAchievementModal(...args))
  } : {}) : !$data.userInfo.showAchievements && $data.userInfo.showProfileSection ? {} : {}, {
    K: !$data.userInfo.showAchievements && $data.userInfo.showProfileSection,
    L: !$data.userInfo.showProfileSection ? 1 : "",
    M: common_vendor.t($data.userInfo.showEducation ? "显示" : "隐藏"),
    N: $data.userInfo.showEducation,
    O: common_vendor.o((e) => $data.userInfo.showEducation = e.detail.value),
    P: !$data.userInfo.showProfileSection,
    Q: $data.userInfo.showEducation && $data.userInfo.showProfileSection
  }, $data.userInfo.showEducation && $data.userInfo.showProfileSection ? {
    R: !$data.userInfo.showProfileSection,
    S: $data.userInfo.education,
    T: common_vendor.o(($event) => $data.userInfo.education = $event.detail.value)
  } : !$data.userInfo.showEducation && $data.userInfo.showProfileSection ? {} : {}, {
    U: !$data.userInfo.showEducation && $data.userInfo.showProfileSection,
    V: !$data.userInfo.showProfileSection ? 1 : ""
  }) : common_vendor.e({
    W: $data.companyInfo.showCompanySection,
    X: common_vendor.o((e) => $data.companyInfo.showCompanySection = e.detail.value),
    Y: common_vendor.t($data.companyInfo.showCompanyName ? "显示" : "隐藏"),
    Z: $data.companyInfo.showCompanyName,
    aa: common_vendor.o((e) => $data.companyInfo.showCompanyName = e.detail.value),
    ab: !$data.companyInfo.showCompanySection,
    ac: $data.companyInfo.showCompanyName && $data.companyInfo.showCompanySection
  }, $data.companyInfo.showCompanyName && $data.companyInfo.showCompanySection ? {
    ad: !$data.companyInfo.showCompanySection,
    ae: $data.companyInfo.companyName,
    af: common_vendor.o(($event) => $data.companyInfo.companyName = $event.detail.value)
  } : !$data.companyInfo.showCompanyName && $data.companyInfo.showCompanySection ? {} : {}, {
    ag: !$data.companyInfo.showCompanyName && $data.companyInfo.showCompanySection,
    ah: !$data.companyInfo.showCompanySection ? 1 : "",
    ai: common_vendor.t($data.companyInfo.showCompanyDesc ? "显示" : "隐藏"),
    aj: $data.companyInfo.showCompanyDesc,
    ak: common_vendor.o((e) => $data.companyInfo.showCompanyDesc = e.detail.value),
    al: !$data.companyInfo.showCompanySection,
    am: $data.companyInfo.showCompanyDesc && $data.companyInfo.showCompanySection
  }, $data.companyInfo.showCompanyDesc && $data.companyInfo.showCompanySection ? {
    an: !$data.companyInfo.showCompanySection,
    ao: $data.companyInfo.companyDesc,
    ap: common_vendor.o(($event) => $data.companyInfo.companyDesc = $event.detail.value),
    aq: common_vendor.t($data.companyInfo.companyDesc ? $data.companyInfo.companyDesc.length : 0)
  } : !$data.companyInfo.showCompanyDesc && $data.companyInfo.showCompanySection ? {} : {}, {
    ar: !$data.companyInfo.showCompanyDesc && $data.companyInfo.showCompanySection,
    as: !$data.companyInfo.showCompanySection ? 1 : "",
    at: common_vendor.t($data.companyInfo.showCompanySlogan ? "显示" : "隐藏"),
    av: $data.companyInfo.showCompanySlogan,
    aw: common_vendor.o((e) => $data.companyInfo.showCompanySlogan = e.detail.value),
    ax: !$data.companyInfo.showCompanySection,
    ay: $data.companyInfo.showCompanySlogan && $data.companyInfo.showCompanySection
  }, $data.companyInfo.showCompanySlogan && $data.companyInfo.showCompanySection ? {
    az: !$data.companyInfo.showCompanySection,
    aA: $data.companyInfo.companySlogan,
    aB: common_vendor.o(($event) => $data.companyInfo.companySlogan = $event.detail.value)
  } : !$data.companyInfo.showCompanySlogan && $data.companyInfo.showCompanySection ? {} : {}, {
    aC: !$data.companyInfo.showCompanySlogan && $data.companyInfo.showCompanySection,
    aD: !$data.companyInfo.showCompanySection ? 1 : "",
    aE: common_vendor.t($data.companyInfo.showCompanyAdvantages ? "显示" : "隐藏"),
    aF: $data.companyInfo.showCompanyAdvantages,
    aG: common_vendor.o((e) => $data.companyInfo.showCompanyAdvantages = e.detail.value),
    aH: !$data.companyInfo.showCompanySection,
    aI: $data.companyInfo.showCompanyAdvantages && $data.companyInfo.showCompanySection
  }, $data.companyInfo.showCompanyAdvantages && $data.companyInfo.showCompanySection ? common_vendor.e({
    aJ: common_vendor.f($data.companyInfo.advantages, (advantage, index, i0) => {
      return {
        a: common_vendor.t(advantage),
        b: common_vendor.o(($event) => $options.deleteAdvantage(index), index),
        c: index
      };
    }),
    aK: $data.companyInfo.advantages.length < 6
  }, $data.companyInfo.advantages.length < 6 ? {
    aL: common_vendor.o((...args) => $options.showAddAdvantageModal && $options.showAddAdvantageModal(...args))
  } : {}) : !$data.companyInfo.showCompanyAdvantages && $data.companyInfo.showCompanySection ? {} : {}, {
    aM: !$data.companyInfo.showCompanyAdvantages && $data.companyInfo.showCompanySection,
    aN: !$data.companyInfo.showCompanySection ? 1 : "",
    aO: common_vendor.t($data.companyInfo.showCompanyAddress ? "显示" : "隐藏"),
    aP: $data.companyInfo.showCompanyAddress,
    aQ: common_vendor.o((e) => $data.companyInfo.showCompanyAddress = e.detail.value),
    aR: !$data.companyInfo.showCompanySection,
    aS: $data.companyInfo.showCompanyAddress && $data.companyInfo.showCompanySection
  }, $data.companyInfo.showCompanyAddress && $data.companyInfo.showCompanySection ? {
    aT: !$data.companyInfo.showCompanySection,
    aU: $data.companyInfo.companyAddress,
    aV: common_vendor.o(($event) => $data.companyInfo.companyAddress = $event.detail.value)
  } : !$data.companyInfo.showCompanyAddress && $data.companyInfo.showCompanySection ? {} : {}, {
    aW: !$data.companyInfo.showCompanyAddress && $data.companyInfo.showCompanySection,
    aX: !$data.companyInfo.showCompanySection ? 1 : ""
  }), {
    aY: common_vendor.s($options.mainStyle),
    aZ: $data.showTagPopup
  }, $data.showTagPopup ? {
    ba: common_vendor.o((...args) => $options.closeTagPopup && $options.closeTagPopup(...args)),
    bb: common_vendor.o([($event) => $data.tagInput = $event.detail.value, (...args) => $options.onTagInputChange && $options.onTagInputChange(...args)]),
    bc: $data.tagInput,
    bd: common_vendor.o((...args) => $options.closeTagPopup && $options.closeTagPopup(...args)),
    be: common_vendor.o((...args) => $options.confirmAddTag && $options.confirmAddTag(...args))
  } : {}, {
    bf: $data.showAchievementPopup
  }, $data.showAchievementPopup ? {
    bg: common_vendor.o((...args) => $options.closeAchievementPopup && $options.closeAchievementPopup(...args)),
    bh: $data.achievementInput,
    bi: common_vendor.o(($event) => $data.achievementInput = $event.detail.value),
    bj: common_vendor.o((...args) => $options.closeAchievementPopup && $options.closeAchievementPopup(...args)),
    bk: common_vendor.o((...args) => $options.confirmAddAchievement && $options.confirmAddAchievement(...args))
  } : {}, {
    bl: $data.showAdvantagePopup
  }, $data.showAdvantagePopup ? {
    bm: common_vendor.o((...args) => $options.closeAdvantagePopup && $options.closeAdvantagePopup(...args)),
    bn: $data.advantageInput,
    bo: common_vendor.o(($event) => $data.advantageInput = $event.detail.value),
    bp: common_vendor.o((...args) => $options.closeAdvantagePopup && $options.closeAdvantagePopup(...args)),
    bq: common_vendor.o((...args) => $options.confirmAddAdvantage && $options.confirmAddAdvantage(...args))
  } : {}, {
    br: common_vendor.o((...args) => $options.saveProfile && $options.saveProfile(...args)),
    bs: common_vendor.s(_ctx.__cssVars())
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e84d0549"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/card/profile.js.map
