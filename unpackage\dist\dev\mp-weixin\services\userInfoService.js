"use strict";
const common_vendor = require("../common/vendor.js");
const utils_config = require("../utils/config.js");
const services_userService = require("./userService.js");
class UserInfoService {
  /**
   * 获取用户详细信息
   * @returns {Promise} 用户信息
   */
  async getUserInfo() {
    try {
      if (!services_userService.userService.isLoggedIn()) {
        throw new Error("请先登录");
      }
      const token = services_userService.userService.getToken();
      const response = await common_vendor.index.request({
        url: `${utils_config.API_BASE_URL}/user/read`,
        method: "GET",
        header: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        }
      });
      if (response.statusCode === 200 && response.data.code === 200) {
        return {
          success: true,
          data: response.data.data
        };
      } else {
        common_vendor.index.__f__("error", "at services/userInfoService.js:37", "获取用户信息失败 - 服务器响应:", response.data);
        throw new Error(response.data.message || "获取用户信息失败");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at services/userInfoService.js:41", "获取用户信息失败:", error);
      return {
        success: false,
        message: error.message || "网络请求失败"
      };
    }
  }
  /**
   * 更新用户信息
   * @param {object} userInfo 用户信息
   * @returns {Promise} 更新结果
   */
  async updateUserInfo(userInfo) {
    try {
      if (!services_userService.userService.isLoggedIn()) {
        throw new Error("请先登录");
      }
      const token = services_userService.userService.getToken();
      const updateData = {
        id: userInfo.id,
        name: userInfo.name,
        phone: userInfo.phone,
        email: userInfo.email,
        wechat: userInfo.wechat,
        position: userInfo.position,
        company: userInfo.company,
        address: userInfo.address,
        description: userInfo.description,
        avatar: userInfo.avatar,
        // 个人介绍相关字段 - 确保数组字段转换为普通数组
        tags: Array.isArray(userInfo.tags) ? [...userInfo.tags] : userInfo.tags || [],
        achievements: Array.isArray(userInfo.achievements) ? [...userInfo.achievements] : userInfo.achievements || [],
        education: userInfo.education || "",
        showProfileSection: userInfo.showProfileSection,
        showDescription: userInfo.showDescription,
        showTags: userInfo.showTags,
        showAchievements: userInfo.showAchievements,
        showEducation: userInfo.showEducation,
        // 企业介绍相关字段
        companyName: userInfo.companyName,
        companyDesc: userInfo.companyDesc,
        companySlogan: userInfo.companySlogan,
        advantages: userInfo.advantages,
        companyAddress: userInfo.companyAddress,
        showCompanySection: userInfo.showCompanySection,
        showCompanyName: userInfo.showCompanyName,
        showCompanyDesc: userInfo.showCompanyDesc,
        showCompanySlogan: userInfo.showCompanySlogan,
        showCompanyAdvantages: userInfo.showCompanyAdvantages,
        showCompanyAddress: userInfo.showCompanyAddress
      };
      if (userInfo.customStyle) {
        updateData.customStyle = userInfo.customStyle;
      }
      const filteredUpdateData = {};
      const protectedFields = ["tags", "achievements", "education"];
      for (const [key, value] of Object.entries(updateData)) {
        if (protectedFields.includes(key)) {
          filteredUpdateData[key] = value;
        } else if (value !== void 0 && value !== null) {
          filteredUpdateData[key] = value;
        }
      }
      const response = await common_vendor.index.request({
        url: `${utils_config.API_BASE_URL}/user/save`,
        method: "POST",
        header: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        },
        data: filteredUpdateData
      });
      if (response.statusCode === 200 && response.data.code === 200) {
        return {
          success: true,
          message: "更新成功"
        };
      } else {
        common_vendor.index.__f__("error", "at services/userInfoService.js:136", "更新用户信息失败 - 服务器响应:", response.data);
        throw new Error(response.data.message || "更新失败");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at services/userInfoService.js:140", "更新用户信息失败:", error);
      return {
        success: false,
        message: error.message || "网络请求失败"
      };
    }
  }
  /**
   * 上传头像
   * @param {string} filePath 文件路径
   * @returns {Promise} 上传结果
   */
  async uploadAvatar(filePath) {
    try {
      if (!services_userService.userService.isLoggedIn()) {
        throw new Error("请先登录");
      }
      const token = services_userService.userService.getToken();
      return new Promise((resolve, reject) => {
        const uploadUrl = `${utils_config.API_BASE_URL}/upload/avatar`;
        common_vendor.index.uploadFile({
          url: uploadUrl,
          filePath,
          name: "avatar",
          header: {
            "Authorization": `Bearer ${token}`
          },
          success: (res) => {
            try {
              common_vendor.index.__f__("log", "at services/userInfoService.js:175", "上传响应原始数据:", res);
              if (res.statusCode !== 200) {
                reject(new Error(`服务器响应错误: ${res.statusCode}`));
                return;
              }
              if (!res.data) {
                reject(new Error("服务器返回空数据"));
                return;
              }
              const data = JSON.parse(res.data);
              common_vendor.index.__f__("log", "at services/userInfoService.js:190", "解析后的数据:", data);
              if (data.code === 200) {
                resolve({
                  success: true,
                  data: data.data,
                  url: data.data.url
                });
              } else {
                reject(new Error(data.message || "上传失败"));
              }
            } catch (error) {
              common_vendor.index.__f__("error", "at services/userInfoService.js:202", "解析响应数据失败:", error);
              common_vendor.index.__f__("error", "at services/userInfoService.js:203", "原始响应数据:", res.data);
              reject(new Error(`响应数据解析失败: ${error.message}`));
            }
          },
          fail: () => {
            reject(new Error("上传请求失败"));
          }
        });
      });
    } catch (error) {
      common_vendor.index.__f__("error", "at services/userInfoService.js:213", "上传头像失败:", error);
      return {
        success: false,
        message: error.message || "上传失败"
      };
    }
  }
  /**
   * 获取当前登录用户信息
   * @returns {Promise} 当前用户信息
   */
  async getCurrentUserInfo() {
    try {
      return await this.getUserInfo();
    } catch (error) {
      common_vendor.index.__f__("error", "at services/userInfoService.js:229", "获取当前用户信息失败:", error);
      return {
        success: false,
        message: error.message || "获取用户信息失败"
      };
    }
  }
  /**
   * 验证手机号格式
   * @param {string} phone 手机号
   * @returns {boolean} 是否有效
   */
  validatePhone(phone) {
    return /^1[3-9]\d{9}$/.test(phone);
  }
  /**
   * 验证邮箱格式
   * @param {string} email 邮箱
   * @returns {boolean} 是否有效
   */
  validateEmail(email) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  }
}
const userInfoService = new UserInfoService();
exports.userInfoService = userInfoService;
//# sourceMappingURL=../../.sourcemap/mp-weixin/services/userInfoService.js.map
