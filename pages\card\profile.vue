<template>
	<view class="profile-container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="navbarStyle">
			<view class="navbar-left">
				<view class="back-btn" @click="goBack">
					<text class="back-icon">←</text>
					<text class="back-text">返回</text>
				</view>
			</view>
			<view class="navbar-title">{{ pageType === 'company' ? '企业介绍' : '个人介绍' }}</view>
		</view>

		<!-- 主要内容 -->
		<view class="profile-content" :style="mainStyle">
			<!-- 个人介绍部分 -->
			<view v-if="pageType === 'personal'">
				<!-- 总开关 -->
				<view class="section-block master-switch-block">
					<view class="master-switch-row">
						<view class="master-switch-left">
							<text class="master-switch-title">显示个人介绍板块</text>
							<text class="master-switch-desc">关闭后整个个人介绍部分将不会显示</text>
						</view>
						<switch 
							:checked="userInfo.showProfileSection" 
							@change="(e) => userInfo.showProfileSection = e.detail.value"
							color="#4f46e5"
							style="transform:scale(0.8)"
						/>
					</view>
				</view>
				
				<!-- 个人介绍 -->
				<view class="section-block" :class="{'section-disabled-all': !userInfo.showProfileSection}">
					<view class="section-header">
						<view class="header-left">
							<text class="section-title">个人介绍</text>
							<text class="section-desc">介绍自己的专业背景、工作经验等</text>
						</view>
						<view class="switch-container">
							<text class="switch-label">{{userInfo.showDescription ? '显示' : '隐藏'}}</text>
							<switch 
								:checked="userInfo.showDescription" 
								@change="(e) => userInfo.showDescription = e.detail.value"
								color="#4f46e5"
								style="transform:scale(0.7)"
								:disabled="!userInfo.showProfileSection"
							/>
						</view>
					</view>
					<view class="textarea-wrapper" v-if="userInfo.showDescription && userInfo.showProfileSection">
						<textarea
							class="profile-textarea"
							v-model="userInfo.description"
							placeholder="请输入您的个人介绍，建议200字以内"
							maxlength="300"
							auto-height
							:disabled="!userInfo.showProfileSection"
						/>
						<text class="word-count">{{userInfo.description ? userInfo.description.length : 0}}/300</text>
					</view>
					<view class="section-disabled" v-else-if="!userInfo.showDescription && userInfo.showProfileSection">
						<text class="disabled-text">此内容已隐藏，不会在名片上显示</text>
					</view>
					<view class="section-disabled" v-else>
						<text class="disabled-text">总开关已关闭，整个个人介绍板块将不会显示</text>
					</view>
				</view>
				
				<!-- 个人标签 -->
				<view class="section-block" :class="{'section-disabled-all': !userInfo.showProfileSection}">
					<view class="section-header">
						<view class="header-left">
							<text class="section-title">个人标签</text>
							<text class="section-desc">添加能够代表您的关键词标签</text>
						</view>
						<view class="switch-container">
							<text class="switch-label">{{userInfo.showTags ? '显示' : '隐藏'}}</text>
							<switch 
								:checked="userInfo.showTags" 
								@change="(e) => userInfo.showTags = e.detail.value"
								color="#4f46e5"
								style="transform:scale(0.7)"
								:disabled="!userInfo.showProfileSection"
							/>
						</view>
					</view>
					<view v-if="userInfo.showTags && userInfo.showProfileSection">
						<view class="tags-container">
							<view 
								v-for="(tag, index) in userInfo.tags" 
								:key="index" 
								class="tag-item"
							>
								<text class="tag-text">{{tag}}</text>
								<text class="tag-delete" @click="deleteTag(index)">×</text>
							</view>
							<view class="tag-add" @click="showAddTagModal" v-if="userInfo.tags.length < 6">
								<text class="tag-add-icon">+</text>
								<text class="tag-add-text">添加标签</text>
							</view>
						</view>
					</view>
					<view class="section-disabled" v-else-if="!userInfo.showTags && userInfo.showProfileSection">
						<text class="disabled-text">此内容已隐藏，不会在名片上显示</text>
					</view>
					<view class="section-disabled" v-else>
						<text class="disabled-text">总开关已关闭，整个个人介绍板块将不会显示</text>
					</view>
				</view>
				
				<!-- 个人成就 -->
				<view class="section-block" :class="{'section-disabled-all': !userInfo.showProfileSection}">
					<view class="section-header">
						<view class="header-left">
							<text class="section-title">个人成就</text>
							<text class="section-desc">展示您的专业资质、奖项等</text>
						</view>
						<view class="switch-container">
							<text class="switch-label">{{userInfo.showAchievements ? '显示' : '隐藏'}}</text>
							<switch 
								:checked="userInfo.showAchievements" 
								@change="(e) => userInfo.showAchievements = e.detail.value"
								color="#4f46e5"
								style="transform:scale(0.7)"
								:disabled="!userInfo.showProfileSection"
							/>
						</view>
					</view>
					<view v-if="userInfo.showAchievements && userInfo.showProfileSection">
						<view class="achievements-container">
							<view 
								v-for="(achievement, index) in userInfo.achievements" 
								:key="index" 
								class="achievement-item"
							>
								<text class="achievement-text">{{achievement}}</text>
								<text class="achievement-delete" @click="deleteAchievement(index)">×</text>
							</view>
							<view class="achievement-add" @click="showAddAchievementModal" v-if="userInfo.achievements.length < 3">
								<text class="achievement-add-icon">+</text>
								<text class="achievement-add-text">添加成就</text>
							</view>
						</view>
					</view>
					<view class="section-disabled" v-else-if="!userInfo.showAchievements && userInfo.showProfileSection">
						<text class="disabled-text">此内容已隐藏，不会在名片上显示</text>
					</view>
					<view class="section-disabled" v-else>
						<text class="disabled-text">总开关已关闭，整个个人介绍板块将不会显示</text>
					</view>
				</view>
				
				<!-- 教育背景 -->
				<view class="section-block" :class="{'section-disabled-all': !userInfo.showProfileSection}">
					<view class="section-header">
						<view class="header-left">
							<text class="section-title">教育背景</text>
							<text class="section-desc">填写您的学历信息</text>
						</view>
						<view class="switch-container">
							<text class="switch-label">{{userInfo.showEducation ? '显示' : '隐藏'}}</text>
							<switch 
								:checked="userInfo.showEducation" 
								@change="(e) => userInfo.showEducation = e.detail.value"
								color="#4f46e5"
								style="transform:scale(0.7)"
								:disabled="!userInfo.showProfileSection"
							/>
						</view>
					</view>
					<view class="input-wrapper" v-if="userInfo.showEducation && userInfo.showProfileSection">
						<input 
							class="profile-input" 
							v-model="userInfo.education" 
							placeholder="例如：清华大学 计算机科学 硕士"
							:disabled="!userInfo.showProfileSection"
						/>
					</view>
					<view class="section-disabled" v-else-if="!userInfo.showEducation && userInfo.showProfileSection">
						<text class="disabled-text">此内容已隐藏，不会在名片上显示</text>
					</view>
					<view class="section-disabled" v-else>
						<text class="disabled-text">总开关已关闭，整个个人介绍板块将不会显示</text>
					</view>
				</view>
			</view>
			
			<!-- 企业介绍部分 -->
			<view v-else>
				<!-- 总开关 -->
				<view class="section-block master-switch-block company-block">
					<view class="master-switch-row">
						<view class="master-switch-left">
							<text class="master-switch-title">显示企业介绍板块</text>
							<text class="master-switch-desc">关闭后整个企业介绍部分将不会显示</text>
						</view>
						<switch 
							:checked="companyInfo.showCompanySection" 
							@change="(e) => companyInfo.showCompanySection = e.detail.value"
							color="#ec4aa9"
							style="transform:scale(0.8)"
						/>
					</view>
				</view>
				
				<!-- 公司名称 -->
				<view class="section-block company-block" :class="{'section-disabled-all': !companyInfo.showCompanySection}">
					<view class="section-header">
						<view class="header-left">
							<text class="section-title">公司名称</text>
							<text class="section-desc">填写您的公司全称</text>
						</view>
						<view class="switch-container">
							<text class="switch-label">{{companyInfo.showCompanyName ? '显示' : '隐藏'}}</text>
							<switch 
								:checked="companyInfo.showCompanyName" 
								@change="(e) => companyInfo.showCompanyName = e.detail.value"
								color="#ec4aa9"
								style="transform:scale(0.7)"
								:disabled="!companyInfo.showCompanySection"
							/>
						</view>
					</view>
					<view class="input-wrapper" v-if="companyInfo.showCompanyName && companyInfo.showCompanySection">
						<input 
							class="company-input" 
							v-model="companyInfo.companyName" 
							placeholder="请输入公司全称"
							:disabled="!companyInfo.showCompanySection"
						/>
					</view>
					<view class="section-disabled" v-else-if="!companyInfo.showCompanyName && companyInfo.showCompanySection">
						<text class="disabled-text">此内容已隐藏，不会在名片上显示</text>
					</view>
					<view class="section-disabled" v-else>
						<text class="disabled-text">总开关已关闭，整个企业介绍板块将不会显示</text>
					</view>
				</view>
				
				<!-- 公司简介 -->
				<view class="section-block company-block" :class="{'section-disabled-all': !companyInfo.showCompanySection}">
					<view class="section-header">
						<view class="header-left">
							<text class="section-title">公司简介</text>
							<text class="section-desc">介绍公司业务范围、发展历程等</text>
						</view>
						<view class="switch-container">
							<text class="switch-label">{{companyInfo.showCompanyDesc ? '显示' : '隐藏'}}</text>
							<switch 
								:checked="companyInfo.showCompanyDesc" 
								@change="(e) => companyInfo.showCompanyDesc = e.detail.value"
								color="#ec4aa9"
								style="transform:scale(0.7)"
								:disabled="!companyInfo.showCompanySection"
							/>
						</view>
					</view>
					<view class="textarea-wrapper" v-if="companyInfo.showCompanyDesc && companyInfo.showCompanySection">
						<textarea
							class="company-textarea"
							v-model="companyInfo.companyDesc"
							placeholder="请输入公司简介，建议300字以内"
							maxlength="400"
							auto-height
							:disabled="!companyInfo.showCompanySection"
						/>
						<text class="word-count">{{companyInfo.companyDesc ? companyInfo.companyDesc.length : 0}}/400</text>
					</view>
					<view class="section-disabled" v-else-if="!companyInfo.showCompanyDesc && companyInfo.showCompanySection">
						<text class="disabled-text">此内容已隐藏，不会在名片上显示</text>
					</view>
					<view class="section-disabled" v-else>
						<text class="disabled-text">总开关已关闭，整个企业介绍板块将不会显示</text>
					</view>
				</view>
				
				<!-- 公司标语 -->
				<view class="section-block company-block" :class="{'section-disabled-all': !companyInfo.showCompanySection}">
					<view class="section-header">
						<view class="header-left">
							<text class="section-title">公司标语</text>
							<text class="section-desc">填写公司slogan或宣传语</text>
						</view>
						<view class="switch-container">
							<text class="switch-label">{{companyInfo.showCompanySlogan ? '显示' : '隐藏'}}</text>
							<switch 
								:checked="companyInfo.showCompanySlogan" 
								@change="(e) => companyInfo.showCompanySlogan = e.detail.value"
								color="#ec4aa9"
								style="transform:scale(0.7)"
								:disabled="!companyInfo.showCompanySection"
							/>
						</view>
					</view>
					<view class="input-wrapper" v-if="companyInfo.showCompanySlogan && companyInfo.showCompanySection">
						<input 
							class="company-input" 
							v-model="companyInfo.companySlogan" 
							placeholder="例如：科技改变生活"
							:disabled="!companyInfo.showCompanySection"
						/>
					</view>
					<view class="section-disabled" v-else-if="!companyInfo.showCompanySlogan && companyInfo.showCompanySection">
						<text class="disabled-text">此内容已隐藏，不会在名片上显示</text>
					</view>
					<view class="section-disabled" v-else>
						<text class="disabled-text">总开关已关闭，整个企业介绍板块将不会显示</text>
					</view>
				</view>
				
				<!-- 企业优势 -->
				<view class="section-block company-block" :class="{'section-disabled-all': !companyInfo.showCompanySection}">
					<view class="section-header">
						<view class="header-left">
							<text class="section-title">企业优势</text>
							<text class="section-desc">添加企业核心竞争力标签</text>
						</view>
						<view class="switch-container">
							<text class="switch-label">{{companyInfo.showCompanyAdvantages ? '显示' : '隐藏'}}</text>
							<switch 
								:checked="companyInfo.showCompanyAdvantages" 
								@change="(e) => companyInfo.showCompanyAdvantages = e.detail.value"
								color="#ec4aa9"
								style="transform:scale(0.7)"
								:disabled="!companyInfo.showCompanySection"
							/>
						</view>
					</view>
					<view v-if="companyInfo.showCompanyAdvantages && companyInfo.showCompanySection">
						<view class="tags-container company-tags">
							<view 
								v-for="(advantage, index) in companyInfo.advantages" 
								:key="index" 
								class="tag-item company-tag"
							>
								<text class="tag-text company-tag-text">{{advantage}}</text>
								<text class="tag-delete company-tag-delete" @click="deleteAdvantage(index)">×</text>
							</view>
							<view class="tag-add company-tag-add" @click="showAddAdvantageModal" v-if="companyInfo.advantages.length < 6">
								<text class="tag-add-icon">+</text>
								<text class="tag-add-text">添加优势</text>
							</view>
						</view>
					</view>
					<view class="section-disabled" v-else-if="!companyInfo.showCompanyAdvantages && companyInfo.showCompanySection">
						<text class="disabled-text">此内容已隐藏，不会在名片上显示</text>
					</view>
					<view class="section-disabled" v-else>
						<text class="disabled-text">总开关已关闭，整个企业介绍板块将不会显示</text>
					</view>
				</view>
				
				<!-- 公司地址 -->
				<view class="section-block company-block" :class="{'section-disabled-all': !companyInfo.showCompanySection}">
					<view class="section-header">
						<view class="header-left">
							<text class="section-title">公司地址</text>
							<text class="section-desc">填写公司详细地址</text>
						</view>
						<view class="switch-container">
							<text class="switch-label">{{companyInfo.showCompanyAddress ? '显示' : '隐藏'}}</text>
							<switch 
								:checked="companyInfo.showCompanyAddress" 
								@change="(e) => companyInfo.showCompanyAddress = e.detail.value"
								color="#ec4aa9"
								style="transform:scale(0.7)"
								:disabled="!companyInfo.showCompanySection"
							/>
						</view>
					</view>
					<view class="input-wrapper" v-if="companyInfo.showCompanyAddress && companyInfo.showCompanySection">
						<input 
							class="company-input" 
							v-model="companyInfo.companyAddress" 
							placeholder="请输入公司地址"
							:disabled="!companyInfo.showCompanySection"
						/>
					</view>
					<view class="section-disabled" v-else-if="!companyInfo.showCompanyAddress && companyInfo.showCompanySection">
						<text class="disabled-text">此内容已隐藏，不会在名片上显示</text>
					</view>
					<view class="section-disabled" v-else>
						<text class="disabled-text">总开关已关闭，整个企业介绍板块将不会显示</text>
					</view>
				</view>
			</view>
			
			<!-- 页面底部占位，确保内容不被悬浮按钮遮挡 -->
			<view class="bottom-placeholder"></view>
		</view>
		
		<!-- 添加标签弹窗 -->
		<view class="custom-popup" v-if="showTagPopup">
			<view class="popup-mask" @click="closeTagPopup"></view>
			<view class="popup-content">
				<view class="popup-title">添加标签</view>
				<input
					class="popup-input"
					v-model="tagInput"
					placeholder="请输入标签内容(8字以内)"
					maxlength="8"
					@input="onTagInputChange"
				/>
				<view class="popup-buttons">
					<button class="popup-btn cancel-btn" @click="closeTagPopup">取消</button>
					<button class="popup-btn confirm-btn" @click="confirmAddTag">确定</button>
				</view>
			</view>
		</view>
		
		<!-- 添加成就弹窗 -->
		<view class="custom-popup" v-if="showAchievementPopup">
			<view class="popup-mask" @click="closeAchievementPopup"></view>
			<view class="popup-content">
				<view class="popup-title">添加成就</view>
				<input 
					class="popup-input" 
					v-model="achievementInput" 
					placeholder="请输入成就内容(20字以内)"
					maxlength="20"
				/>
				<view class="popup-buttons">
					<button class="popup-btn cancel-btn" @click="closeAchievementPopup">取消</button>
					<button class="popup-btn confirm-btn" @click="confirmAddAchievement">确定</button>
				</view>
			</view>
		</view>
		
		<!-- 添加企业优势弹窗 -->
		<view class="custom-popup" v-if="showAdvantagePopup">
			<view class="popup-mask" @click="closeAdvantagePopup"></view>
			<view class="popup-content company-popup">
				<view class="popup-title">添加企业优势</view>
				<input 
					class="popup-input" 
					v-model="advantageInput" 
					placeholder="请输入企业优势标签(10字以内)"
					maxlength="10"
				/>
				<view class="popup-buttons">
					<button class="popup-btn cancel-btn" @click="closeAdvantagePopup">取消</button>
					<button class="popup-btn confirm-btn company-confirm-btn" @click="confirmAddAdvantage">确定</button>
				</view>
			</view>
		</view>
		
		<!-- 悬浮保存按钮 -->
		<view class="floating-save-btn-wrapper">
			<button class="floating-save-btn" @click="saveProfile">保存</button>
		</view>
	</view>
</template>

<script>
import userService from '../../services/userService.js';
import userInfoService from '../../services/userInfoService.js';

export default {
	data() {
		return {
			statusBarHeight: 44, // 状态栏高度
			pageType: 'personal', // 页面类型：personal 或 company
			userInfo: {
				description: '',
				tags: [],
				achievements: [],
				education: '',
				// 显示控制开关
				showProfileSection: true, // 总开关
				showDescription: true,
				showTags: true,
				showAchievements: true,
				showEducation: true
			},
			companyInfo: {
				companyName: '',
				companyDesc: '',
				companySlogan: '',
				advantages: [],
				companyAddress: '',
				// 显示控制开关
				showCompanySection: true, // 总开关
				showCompanyName: true,
				showCompanyDesc: true,
				showCompanySlogan: true,
				showCompanyAdvantages: true,
				showCompanyAddress: true
			},
			tagInput: '',
			achievementInput: '',
			// 弹窗控制
			showTagPopup: false,
			showAchievementPopup: false,
			// 企业优势弹窗控制
			showAdvantagePopup: false,
			advantageInput: ''
		}
	},
	computed: {
		navbarStyle() {
			return {
				height: `${this.statusBarHeight + 45}px`
			}
		},
		mainStyle() {
			return {
				marginTop: `${this.statusBarHeight + 55}px`
			}
		}
	},
	async onLoad(option) {
		this.setStatusBarHeight();

		// 根据参数决定页面类型
		if (option && option.type === 'company') {
			this.pageType = 'company';
			await this.loadCompanyInfo();
		} else {
			this.pageType = 'personal';
			await this.loadUserInfo();
		}
	},

	onShow() {
		// 页面显示时不重新加载数据，避免覆盖用户输入
	},

	methods: {
		async loadUserInfo() {
			// 如果用户已登录，优先从服务器加载数据
			if (userService.isLoggedIn()) {
				try {
					const result = await userInfoService.getCurrentUserInfo();

					if (result.success && result.data) {
						const serverData = result.data;

						// 使用服务器数据，但保留当前用户输入
						this.userInfo = {
							description: serverData.description || this.userInfo.description || '',
							tags: serverData.tags || this.userInfo.tags || [],
							achievements: serverData.achievements || this.userInfo.achievements || [],
							education: serverData.education || this.userInfo.education || '',
							// 显示控制开关，优先使用服务器数据
							showProfileSection: serverData.showProfileSection !== undefined ? serverData.showProfileSection : (this.userInfo.showProfileSection !== undefined ? this.userInfo.showProfileSection : true),
							showDescription: serverData.showDescription !== undefined ? serverData.showDescription : (this.userInfo.showDescription !== undefined ? this.userInfo.showDescription : true),
							showTags: serverData.showTags !== undefined ? serverData.showTags : (this.userInfo.showTags !== undefined ? this.userInfo.showTags : true),
							showAchievements: serverData.showAchievements !== undefined ? serverData.showAchievements : (this.userInfo.showAchievements !== undefined ? this.userInfo.showAchievements : true),
							showEducation: serverData.showEducation !== undefined ? serverData.showEducation : (this.userInfo.showEducation !== undefined ? this.userInfo.showEducation : true)
						};

						// 同步到本地存储
						const currentUserInfo = uni.getStorageSync('userInfo') || {};
						const updatedUserInfo = {
							...currentUserInfo,
							...this.userInfo
						};
						uni.setStorageSync('userInfo', updatedUserInfo);

						return;
					}
				} catch (error) {
					console.error('从服务器加载个人介绍数据失败:', error);
				}
			}

			// 服务器加载失败或未登录，使用本地数据
			this.loadUserInfoFromLocal();
		},

		loadUserInfoFromLocal() {
			const savedInfo = uni.getStorageSync('userInfo') || {};

			// 保留当前用户输入的数据，只更新未设置的字段
			this.userInfo = {
				description: savedInfo.description || this.userInfo.description || '',
				tags: savedInfo.tags || this.userInfo.tags || [],
				achievements: savedInfo.achievements || this.userInfo.achievements || [],
				education: savedInfo.education || this.userInfo.education || '',
				// 显示控制开关，默认为true，如果已保存则使用保存的值
				showProfileSection: savedInfo.showProfileSection !== undefined ? savedInfo.showProfileSection : (this.userInfo.showProfileSection !== undefined ? this.userInfo.showProfileSection : true),
				showDescription: savedInfo.showDescription !== undefined ? savedInfo.showDescription : (this.userInfo.showDescription !== undefined ? this.userInfo.showDescription : true),
				showTags: savedInfo.showTags !== undefined ? savedInfo.showTags : (this.userInfo.showTags !== undefined ? this.userInfo.showTags : true),
				showAchievements: savedInfo.showAchievements !== undefined ? savedInfo.showAchievements : (this.userInfo.showAchievements !== undefined ? this.userInfo.showAchievements : true),
				showEducation: savedInfo.showEducation !== undefined ? savedInfo.showEducation : (this.userInfo.showEducation !== undefined ? this.userInfo.showEducation : true)
			};
		},
		
		async loadCompanyInfo() {
			// 如果用户已登录，优先从服务器加载数据
			if (userService.isLoggedIn()) {
				try {
					const result = await userInfoService.getCurrentUserInfo();

					if (result.success && result.data) {
						const serverData = result.data;

						// 使用服务器数据
						this.companyInfo = {
							companyName: serverData.companyName || '',
							companyDesc: serverData.companyDesc || '',
							companySlogan: serverData.companySlogan || '',
							advantages: serverData.advantages || [],
							companyAddress: serverData.companyAddress || '',
							// 显示控制开关，默认为true，如果已保存则使用保存的值
							showCompanySection: serverData.showCompanySection !== undefined ? serverData.showCompanySection : true,
							showCompanyName: serverData.showCompanyName !== undefined ? serverData.showCompanyName : true,
							showCompanyDesc: serverData.showCompanyDesc !== undefined ? serverData.showCompanyDesc : true,
							showCompanySlogan: serverData.showCompanySlogan !== undefined ? serverData.showCompanySlogan : true,
							showCompanyAdvantages: serverData.showCompanyAdvantages !== undefined ? serverData.showCompanyAdvantages : true,
							showCompanyAddress: serverData.showCompanyAddress !== undefined ? serverData.showCompanyAddress : true
						};

						// 同步到本地存储
						const currentUserInfo = uni.getStorageSync('userInfo') || {};
						const updatedUserInfo = {
							...currentUserInfo,
							...this.companyInfo
						};
						uni.setStorageSync('userInfo', updatedUserInfo);

						return;
					}
				} catch (error) {
					console.error('从服务器加载企业介绍数据失败:', error);
				}
			}

			// 服务器加载失败或未登录，使用本地数据
			this.loadCompanyInfoFromLocal();
		},

		loadCompanyInfoFromLocal() {
			const savedInfo = uni.getStorageSync('userInfo') || {};

			// 确保所有字段都存在
			this.companyInfo = {
				companyName: savedInfo.companyName || '',
				companyDesc: savedInfo.companyDesc || '',
				companySlogan: savedInfo.companySlogan || '',
				advantages: savedInfo.advantages || [],
				companyAddress: savedInfo.companyAddress || '',
				// 显示控制开关，默认为true，如果已保存则使用保存的值
				showCompanySection: savedInfo.showCompanySection !== undefined ? savedInfo.showCompanySection : true,
				showCompanyName: savedInfo.showCompanyName !== undefined ? savedInfo.showCompanyName : true,
				showCompanyDesc: savedInfo.showCompanyDesc !== undefined ? savedInfo.showCompanyDesc : true,
				showCompanySlogan: savedInfo.showCompanySlogan !== undefined ? savedInfo.showCompanySlogan : true,
				showCompanyAdvantages: savedInfo.showCompanyAdvantages !== undefined ? savedInfo.showCompanyAdvantages : true,
				showCompanyAddress: savedInfo.showCompanyAddress !== undefined ? savedInfo.showCompanyAddress : true
			};
		},
		
		setStatusBarHeight() {
			// 获取系统信息，设置状态栏高度
			try {
				const windowInfo = uni.getWindowInfo();
				const statusBarHeight = windowInfo.statusBarHeight || 0;
				this.statusBarHeight = statusBarHeight;
			} catch (error) {
				console.log('获取状态栏高度失败:', error);
				this.statusBarHeight = 44; // 默认值
			}
		},
		
		goBack() {
			uni.navigateBack();
		},
		
		showAddTagModal() {
			this.tagInput = ''; // 清空输入
			this.showTagPopup = true;

			// 确保tags数组已初始化
			if (!this.userInfo.tags) {
				this.userInfo.tags = [];
			}
		},
		
		closeTagPopup() {
			this.showTagPopup = false;
		},

		onTagInputChange(e) {
			this.tagInput = e.detail.value;
		},
		
		confirmAddTag() {
			const value = this.tagInput;

			if (value && value.trim() && value.trim().length > 0 && value.trim().length <= 8) {
				// 检查是否已存在相同标签
				if (!this.userInfo.tags.includes(value.trim())) {
					this.userInfo.tags.push(value.trim());
					this.closeTagPopup();
					uni.showToast({
						title: '标签添加成功',
						icon: 'success'
					});
				} else {
					uni.showToast({
						title: '标签已存在',
						icon: 'none'
					});
				}
			} else if (value && value.trim().length > 8) {
				uni.showToast({
					title: '标签不能超过8个字',
					icon: 'none'
				});
			} else {
				uni.showToast({
					title: '请输入标签内容',
					icon: 'none'
				});
			}
		},
		
		deleteTag(index) {
			this.userInfo.tags.splice(index, 1);
		},
		
		showAddAchievementModal() {
			this.achievementInput = ''; // 清空输入
			this.showAchievementPopup = true;
		},
		
		closeAchievementPopup() {
			this.showAchievementPopup = false;
		},
		
		confirmAddAchievement() {
			const value = this.achievementInput;
			if (value && value.trim() && value.length <= 20) {
				this.userInfo.achievements.push(value.trim());
				this.closeAchievementPopup();
			} else if (value.length > 20) {
				uni.showToast({
					title: '成就内容不能超过20个字',
					icon: 'none'
				});
			} else {
				uni.showToast({
					title: '请输入成就内容',
					icon: 'none'
				});
			}
		},
		
		deleteAchievement(index) {
			this.userInfo.achievements.splice(index, 1);
		},
		
		showAddAdvantageModal() {
			this.advantageInput = '';
			this.showAdvantagePopup = true;
		},
		
		closeAdvantagePopup() {
			this.showAdvantagePopup = false;
		},
		
		confirmAddAdvantage() {
			const value = this.advantageInput;
			if (value && value.trim() && value.length <= 10) {
				// 检查是否已存在相同优势
				if (!this.companyInfo.advantages.includes(value.trim())) {
					this.companyInfo.advantages.push(value.trim());
					this.closeAdvantagePopup();
				} else {
					uni.showToast({
						title: '标签已存在',
						icon: 'none'
					});
				}
			} else if (value.length > 10) {
				uni.showToast({
					title: '标签不能超过10个字',
					icon: 'none'
				});
			} else {
				uni.showToast({
					title: '请输入标签内容',
					icon: 'none'
				});
			}
		},
		
		deleteAdvantage(index) {
			this.companyInfo.advantages.splice(index, 1);
		},
		
		async saveProfile() {
			// 检查登录状态
			if (!userService.isLoggedIn()) {
				uni.showModal({
					title: '需要登录',
					content: '保存功能需要登录后才能使用，是否前往登录？',
					confirmText: '去登录',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/auth/auth'
							});
						}
					}
				});
				return;
			}

			// 检查是否为专业版或企业版用户
			const isMember = uni.getStorageSync('isMember');
			const memberLevel = uni.getStorageSync('memberLevel') || 0;
			const memberExpireDate = uni.getStorageSync('memberExpireDate');

			// 检查是否过期
			let isExpired = false;
			if (memberExpireDate) {
				const now = new Date();
				// 修复iOS日期格式兼容性问题
				const formattedDate = memberExpireDate.replace(/\s/g, 'T');
				const expireDate = new Date(formattedDate);
				isExpired = now > expireDate;
			}

			// 检查权限：需要专业版或企业版且未过期
			const hasPermission = isMember && memberLevel >= 1 && !isExpired;

			if (!hasPermission) {
				uni.showModal({
					title: '专业版功能',
					content: this.pageType === 'company' ? '企业介绍保存功能需要专业版或企业版权限' : '个人介绍保存功能需要专业版或企业版权限',
					confirmText: '立即升级',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							// 跳转到会员开通页面
							uni.navigateTo({
								url: '/pages/company/premium'
							});
						}
					}
				});
				return;
			}

			// 显示保存中状态
			uni.showLoading({
				title: '保存中...',
				mask: true
			});

			try {
				// 获取当前存储的完整用户信息
				const currentUserInfo = uni.getStorageSync('userInfo') || {};
				let updatedUserInfo;

				// 根据页面类型准备不同的数据
				if (this.pageType === 'company') {
					// 更新企业介绍相关字段
					updatedUserInfo = {
						...currentUserInfo,
						companyName: this.companyInfo.companyName,
						companyDesc: this.companyInfo.companyDesc,
						companySlogan: this.companyInfo.companySlogan,
						advantages: this.companyInfo.advantages,
						companyAddress: this.companyInfo.companyAddress,
						// 保存显示控制开关状态
						showCompanySection: this.companyInfo.showCompanySection,
						showCompanyName: this.companyInfo.showCompanyName,
						showCompanyDesc: this.companyInfo.showCompanyDesc,
						showCompanySlogan: this.companyInfo.showCompanySlogan,
						showCompanyAdvantages: this.companyInfo.showCompanyAdvantages,
						showCompanyAddress: this.companyInfo.showCompanyAddress
					};
				} else {
					// 更新个人介绍相关字段
					updatedUserInfo = {
						...currentUserInfo,
						description: this.userInfo.description,
						tags: this.userInfo.tags,
						achievements: this.userInfo.achievements,
						education: this.userInfo.education,
						// 保存显示控制开关状态
						showProfileSection: this.userInfo.showProfileSection,
						showDescription: this.userInfo.showDescription,
						showTags: this.userInfo.showTags,
						showAchievements: this.userInfo.showAchievements,
						showEducation: this.userInfo.showEducation
					};
				}



				// 先更新本地存储（乐观更新）
				uni.setStorageSync('userInfo', updatedUserInfo);

				// 同步到服务器
				const result = await userInfoService.updateUserInfo(updatedUserInfo);

				if (result.success) {
					// 服务器保存成功
					uni.hideLoading();
					uni.showToast({
						title: '保存成功',
						icon: 'success',
						duration: 2000,
						success: () => {
							// 延迟返回上一页
							setTimeout(() => {
								uni.navigateBack();
							}, 1500);
						}
					});
				} else {
					// 服务器保存失败，但本地已保存
					uni.hideLoading();
					uni.showToast({
						title: result.message || '保存失败，请重试',
						icon: 'none',
						duration: 3000
					});
				}

			} catch (error) {
				console.error('保存个人介绍失败:', error);

				uni.hideLoading();
				uni.showToast({
					title: '保存失败，请检查网络连接',
					icon: 'none',
					duration: 3000
				});
			}
		}
	}
}
</script>

<style scoped>
.profile-container {
	min-height: 100vh;
	background: #f8fafc;
	position: relative;
}

/* 自定义导航栏 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
	padding: 20rpx 32rpx 20rpx 32rpx;
	box-sizing: border-box;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
}

.navbar-left {
	display: flex;
	align-items: center;
}

.back-btn {
	padding: 8rpx 16rpx;
	background: white;
	border-radius: 20rpx;
	border: 1rpx solid #e2e8f0;
	display: flex;
	align-items: center;
	gap: 6rpx;
}

.back-icon {
	font-size: 28rpx;
	color: #64748b;
}

.back-text {
	font-size: 24rpx;
	color: #64748b;
}

.navbar-title {
	color: #111827;
	font-size: 36rpx;
	font-weight: 600;
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	letter-spacing: 0.5rpx;
}

/* 主要内容 */
.profile-content {
	padding: 32rpx;
}

.section-block {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.section-header {
	margin-bottom: 16rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.header-left {
	flex: 1;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #111827;
	display: block;
	margin-bottom: 8rpx;
}

.section-desc {
	font-size: 24rpx;
	color: #64748b;
	display: block;
}

.switch-container {
	display: flex;
	align-items: center;
}

.switch-label {
	font-size: 24rpx;
	color: #64748b;
	margin-right: 8rpx;
}

.section-disabled {
	background: #f1f5f9;
	padding: 24rpx;
	border-radius: 12rpx;
	text-align: center;
}

.disabled-text {
	font-size: 26rpx;
	color: #94a3b8;
}

/* 总开关样式 */
.master-switch-block {
	background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
	border-left: 6rpx solid #4f46e5;
}

.master-switch-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.master-switch-left {
	flex: 1;
}

.master-switch-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #4f46e5;
	display: block;
	margin-bottom: 8rpx;
}

.master-switch-desc {
	font-size: 24rpx;
	color: #6b7280;
	display: block;
}

/* 总开关关闭时的灰度样式 */
.section-disabled-all {
	opacity: 0.6;
	position: relative;
}

.textarea-wrapper {
	position: relative;
	margin-top: 16rpx;
}

.profile-textarea {
	width: 100%;
	padding: 20rpx;
	background: #f1f5f9;
	border-radius: 12rpx;
	font-size: 28rpx;
	color: #334155;
	min-height: 200rpx;
	box-sizing: border-box;
}

.word-count {
	position: absolute;
	bottom: 12rpx;
	right: 20rpx;
	font-size: 24rpx;
	color: #94a3b8;
}

.input-wrapper {
	margin-top: 16rpx;
}

.profile-input {
	width: 100%;
	padding: 20rpx;
	background: #f1f5f9;
	border-radius: 12rpx;
	font-size: 28rpx;
	color: #334155;
	box-sizing: border-box;
	height: 88rpx;
	line-height: 48rpx;
}

/* 标签样式 */
.tags-container {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
	margin-top: 16rpx;
}

.tag-item {
	display: flex;
	align-items: center;
	background: rgba(99, 102, 241, 0.1);
	padding: 10rpx 20rpx;
	border-radius: 40rpx;
	border: 1rpx solid rgba(99, 102, 241, 0.2);
}

.tag-text {
	font-size: 26rpx;
	color: #4f46e5;
	margin-right: 8rpx;
}

.tag-delete {
	font-size: 28rpx;
	color: #4f46e5;
	font-weight: bold;
	padding: 0 4rpx;
}

.tag-add {
	display: flex;
	align-items: center;
	background: #f1f5f9;
	padding: 10rpx 20rpx;
	border-radius: 40rpx;
	border: 1rpx dashed #cbd5e1;
}

.tag-add-icon {
	font-size: 26rpx;
	color: #64748b;
	margin-right: 8rpx;
}

.tag-add-text {
	font-size: 26rpx;
	color: #64748b;
}

/* 成就样式 */
.achievements-container {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
	margin-top: 16rpx;
}

.achievement-item {
	display: flex;
	align-items: center;
	background: rgba(79, 70, 229, 0.05);
	padding: 16rpx;
	border-radius: 12rpx;
	border-left: 4rpx solid #4f46e5;
}

.achievement-text {
	font-size: 26rpx;
	color: #4f46e5;
	flex: 1;
}

.achievement-delete {
	font-size: 28rpx;
	color: #4f46e5;
	font-weight: bold;
	padding: 0 4rpx;
}

.achievement-add {
	display: flex;
	align-items: center;
	justify-content: center;
	background: #f1f5f9;
	padding: 16rpx;
	border-radius: 12rpx;
	border: 1rpx dashed #cbd5e1;
}

.achievement-add-icon {
	font-size: 26rpx;
	color: #64748b;
	margin-right: 8rpx;
}

.achievement-add-text {
	font-size: 26rpx;
	color: #64748b;
}

/* 弹窗样式 */
.custom-popup {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
}

.popup-mask {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
}

.popup-content {
	width: 80%;
	background: white;
	border-radius: 16rpx;
	padding: 40rpx;
	position: relative;
	z-index: 10000;
}

.popup-title {
	text-align: center;
	font-size: 32rpx;
	font-weight: bold;
	color: #4f46e5;
	margin-bottom: 30rpx;
}

.popup-input {
	width: 100%;
	height: 88rpx;
	background: #f1f5f9;
	border-radius: 12rpx;
	padding: 0 24rpx;
	margin-bottom: 30rpx;
	box-sizing: border-box;
	font-size: 28rpx;
}

.popup-buttons {
	display: flex;
	gap: 20rpx;
}

.popup-btn {
	flex: 1;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	border-radius: 12rpx;
	border: none;
}

.cancel-btn {
	background: #f1f5f9;
	color: #64748b;
}

.confirm-btn {
	background: #4f46e5;
	color: white;
}

/* 浮动保存按钮 */
.floating-save-btn-wrapper {
	position: fixed;
	bottom: 40rpx;
	left: 0;
	right: 0;
	display: flex;
	justify-content: center;
	z-index: 100;
}

.floating-save-btn {
	width: 80%;
	height: 88rpx;
	line-height: 88rpx;
	background-color: v-bind(pageType === 'company' ? '#ec4aa9' : '#4f46e5');
	color: white;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: bold;
	box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
}

.bottom-placeholder {
	height: 140rpx;
}

/* 企业介绍样式 */
.company-block {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
	border-left: 6rpx solid #ec4aa9;
}

.company-block.master-switch-block {
	background: linear-gradient(135deg, #fff0f7 0%, #fce7f3 100%);
}

.company-block .master-switch-title {
	color: #ec4aa9;
}

.company-textarea {
	width: 100%;
	min-height: 200rpx;
	background: #fdf2f8;
	border-radius: 12rpx;
	padding: 20rpx;
	font-size: 28rpx;
	color: #1e293b;
}

.company-input {
	width: 100%;
	height: 80rpx;
	background: #fdf2f8;
	border-radius: 12rpx;
	padding: 0 24rpx;
	font-size: 28rpx;
	color: #1e293b;
}

.company-tags .tag-item {
	background-color: #fdf2ff;
	border: 1rpx solid #fbcfe8;
}

.company-tag-text {
	color: #ec4aa9;
}

.company-tag-delete {
	color: #ec4aa9;
}

.company-tag-add {
	border: 1rpx dashed #fbcfe8;
}

.company-popup .popup-title {
	color: #ec4aa9;
}

.company-confirm-btn {
	background-color: #ec4aa9;
}
</style> 