
.edit-container.data-v-a9a206dd {
  background: #f7f8fa;
  min-height: 100vh;
  padding-bottom: 140rpx;
}
.custom-navbar.data-v-a9a206dd {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  padding: 20rpx 32rpx 20rpx 32rpx;
  box-sizing: border-box;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.navbar-left.data-v-a9a206dd {
  display: flex;
  align-items: center;
}
.back-btn.data-v-a9a206dd {
  padding: 8rpx 16rpx;
  background: linear-gradient(to bottom, #ffffff, #f5f5f5);
  border-radius: 20rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 6rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}
.back-btn.data-v-a9a206dd:active {
  transform: scale(0.96);
  background: linear-gradient(to bottom, #f5f5f5, #eeeeee);
}
.back-icon.data-v-a9a206dd {
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 4rpx;
  color: #4f46e5;
}
.back-text.data-v-a9a206dd {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}
.navbar-title.data-v-a9a206dd {
  color: #111827;
  font-size: 36rpx;
  font-weight: 700;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  letter-spacing: 0.5rpx;
  background: linear-gradient(to right, #4f46e5, #6366f1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.top-section.data-v-a9a206dd {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #f7f8fa;
}
.card-preview.data-v-a9a206dd {
  padding: 0;
  background-color: #f7f8fa;
}
.fixed-tab-bar.data-v-a9a206dd {
  padding: 0 20rpx;
  margin-bottom: 0; /* 减小底部间距 */
}
.tab-bar.data-v-a9a206dd {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
  overflow: hidden;
}
.tab-item.data-v-a9a206dd {
  flex: 1;
  text-align: center;
  font-size: 28rpx; /* 减小字体大小 */
  color: #888;
  padding: 20rpx 0; /* 减小内边距 */
  background: #fff;
  transition: color 0.2s, background 0.2s;
}
.tab-item.active.data-v-a9a206dd {
  color: #2c5aa0;
  background: #f0f4ff;
  font-weight: bold;
}
.content-area.data-v-a9a206dd {
  min-height: 100vh;
  padding-top: 0; /* 初始值为0，由JS动态设置 */
  transition: padding-top 0.2s ease-out; /* 添加平滑过渡效果 */
}
.form-section.data-v-a9a206dd {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);
}
.section-header.data-v-a9a206dd {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.section-title.data-v-a9a206dd {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c5aa0;
  margin-right: 16rpx;
}
.section-line.data-v-a9a206dd {
  flex: 1;
  height: 2rpx;
  background: linear-gradient(90deg, #4a6ef2, rgba(74,110,242,0.1));
}
.form-group.data-v-a9a206dd {
  margin-bottom: 20rpx;
}
.form-divider.data-v-a9a206dd {
  height: 1rpx;
  background: #f0f0f0;
  margin: 20rpx 0;
}
.modern-form-item.data-v-a9a206dd {
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
}
.form-label.data-v-a9a206dd {
  width: 120rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.input-container.data-v-a9a206dd {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
}
.input-icon.data-v-a9a206dd {
  width: 36rpx;
  height: 36rpx;
  position: absolute;
  left: 16rpx;
  opacity: 0.6;
}
.clear-btn.data-v-a9a206dd {
  position: absolute;
  right: 16rpx;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e0e0e0;
  border-radius: 50%;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.clear-btn.data-v-a9a206dd:active {
  transform: scale(0.9);
  background: #d0d0d0;
  box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.15);
}
.clear-icon.data-v-a9a206dd {
  font-size: 24rpx;
  color: #666;
  font-weight: bold;
  line-height: 1;
}
.error-tip.data-v-a9a206dd {
  font-size: 24rpx;
  color: #ff4757;
  margin-top: 8rpx;
  margin-left: 16rpx;
}
.form-input.data-v-a9a206dd {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #e5e5e5;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background: #f8f8f8;
  transition: all 0.3s;
}
.form-input.with-icon.data-v-a9a206dd {
  padding-left: 60rpx;
  padding-right: 60rpx;
}
.form-input.data-v-a9a206dd:focus {
  border-color: #4a6ef2;
  background: #f0f4ff;
}
.form-textarea.data-v-a9a206dd {
  flex: 1;
  min-height: 80rpx;
  border: 1rpx solid #e5e5e5;
  border-radius: 8rpx;
  padding: 8rpx 16rpx;
  font-size: 26rpx;
  background: #f8f8f8;
}
.form-btn.data-v-a9a206dd {
  font-size: 26rpx;
  color: #4a6ef2;
  background: #f0f4ff;
  border-radius: 8rpx;
  padding: 0 24rpx;
  height: 56rpx;
  border: none;
}
.picker-view.data-v-a9a206dd {
  flex: 1;
  height: 64rpx;
  display: flex;
  align-items: center;
  border: 1rpx solid #e5e5e5;
  border-radius: 8rpx;
  padding: 0 16rpx;
  font-size: 26rpx;
  background: #f8f8f8;
}
.diy-container.data-v-a9a206dd {
  padding: 10rpx 20rpx; /* 减小上下内边距 */
  background-color: #f5f7fa;
}
.diy-card.data-v-a9a206dd {
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);
  margin-bottom: 16rpx; /* 减小底部外边距 */
  overflow: hidden;
}
.diy-card-header.data-v-a9a206dd {
  padding: 16rpx; /* 减小内边距 */
  border-bottom: 1rpx solid #f0f0f0;
}
.diy-card-title.data-v-a9a206dd {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}
.diy-card-content.data-v-a9a206dd {
  padding: 16rpx; /* 减小内边距 */
}
.diy-option-group.data-v-a9a206dd {
  margin-bottom: 24rpx;
}
.diy-option-group.data-v-a9a206dd:last-child {
  margin-bottom: 0;
}
.diy-option-group-title.data-v-a9a206dd {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
  display: block;
}
.diy-option-row.data-v-a9a206dd {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  align-items: center;
  justify-content: flex-start;
  padding: 6rpx 0;
}
.diy-option-item.data-v-a9a206dd {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}
.diy-option-preview.data-v-a9a206dd {
  width: 60rpx;
  height: 40rpx;
  border-radius: 8rpx;
  margin-bottom: 8rpx;
  transition: all 0.2s;
}
.diy-option-label.data-v-a9a206dd {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50rpx;
  line-height: 1.2;
  padding: 0 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-word;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -webkit-box;
}
.diy-color-block.data-v-a9a206dd {
  width: 44rpx;
  height: 44rpx;
  border-radius: 8rpx;
  border: 2rpx solid transparent;
  transition: all 0.2s;
  margin: 6rpx;
  position: relative; /* 添加相对定位，用于白色边框 */
}
.diy-color-block.active.data-v-a9a206dd {
  border-color: #4a6ef2;
  transform: scale(1.1);
  box-shadow: 0 2rpx 8rpx rgba(74,110,242,0.3);
  z-index: 1; /* 确保激活状态的颜色块显示在上层 */
}
.diy-color-block-border.data-v-a9a206dd {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 1rpx solid #ddd;
  border-radius: 7rpx;
  pointer-events: none; /* 确保不影响点击事件 */
}
.diy-image-block.data-v-a9a206dd {
  width: 140rpx;
  height: 110rpx;
  border-radius: 10rpx;
  overflow: hidden;
  border: 2rpx solid transparent;
  transition: all 0.2s;
  margin: 10rpx;
}
.diy-image-block.active.data-v-a9a206dd {
  border-color: #4a6ef2;
  transform: scale(1.05);
  box-shadow: 0 4rpx 12rpx rgba(74,110,242,0.3);
}
.diy-image-preview.data-v-a9a206dd {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.diy-upload-btn.data-v-a9a206dd {
  width: 140rpx;
  height: 110rpx;
  border-radius: 10rpx;
  border: 2rpx dashed #999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  transition: all 0.2s;
  margin: 10rpx;
}
.diy-upload-btn.data-v-a9a206dd:active {
  background-color: #f0f0f0;
}
.diy-upload-icon.data-v-a9a206dd {
  font-size: 36rpx;
  color: #999;
  line-height: 1;
  margin-bottom: 6rpx;
}
.diy-upload-text.data-v-a9a206dd {
  font-size: 28rpx;
  color: #999;
  line-height: 1;
  margin-top: 8rpx;
}
.diy-text-size-block.data-v-a9a206dd {
  min-width: 64rpx;
  height: 48rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.2s;
}
.diy-text-size-block.active.data-v-a9a206dd {
  border-color: #4a6ef2;
  background-color: #e6f0ff;
}
.diy-avatar-block.data-v-a9a206dd {
  width: 48rpx;
  height: 48rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid transparent;
  transition: all 0.2s;
}
.diy-avatar-block.active.data-v-a9a206dd {
  border-color: #4a6ef2;
  background-color: #e6f0ff;
}
.diy-avatar-preview.data-v-a9a206dd {
  width: 36rpx;
  height: 36rpx;
  background-color: #999;
}
.diy-switch-btn.data-v-a9a206dd {
  min-width: 64rpx;
  height: 48rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12rpx;
  border: 2rpx solid transparent;
  font-size: 26rpx;
  color: #666;
  transition: all 0.2s;
}
.diy-switch-btn.active.data-v-a9a206dd {
  border-color: #4a6ef2;
  background-color: #e6f0ff;
  color: #4a6ef2;
}

/* 地址输入样式优化 */
.address-item.data-v-a9a206dd {
  align-items: flex-start;
}
.address-input-container.data-v-a9a206dd {
  flex: 1;
}
.address-preview.data-v-a9a206dd {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  border: 1rpx solid #e5e5e5;
  border-radius: 12rpx;
  padding: 0 20rpx 0 60rpx;
  font-size: 28rpx;
  background: #f8f8f8;
}
.address-text.data-v-a9a206dd {
  flex: 1;
  color: #333;
  word-break: break-all;
}
.address-icon.data-v-a9a206dd {
  color: #999;
  font-size: 28rpx;
  margin-left: 10rpx;
}

/* 地址选择弹窗 */
.address-modal.data-v-a9a206dd {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: flex-end;
}
.address-modal-mask.data-v-a9a206dd {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.address-modal-content.data-v-a9a206dd {
  position: relative;
  width: 100%;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 10000;
}
.address-modal-header.data-v-a9a206dd {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.address-modal-title.data-v-a9a206dd {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.address-modal-close.data-v-a9a206dd {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}
.address-tabs.data-v-a9a206dd {
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}
.address-tab.data-v-a9a206dd {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}
.address-tab.active.data-v-a9a206dd {
  color: #4a6ef2;
  font-weight: bold;
}
.address-tab.active.data-v-a9a206dd::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #4a6ef2;
  border-radius: 2rpx;
}
.address-content.data-v-a9a206dd {
  padding: 30rpx 24rpx;
  min-height: 400rpx;
}
.address-manual.data-v-a9a206dd {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.address-picker-group.data-v-a9a206dd {
  border: 1rpx solid #e5e5e5;
  border-radius: 8rpx;
  overflow: hidden;
}
.region-picker.data-v-a9a206dd {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f8f8f8;
}
.picker-arrow.data-v-a9a206dd {
  color: #999;
  font-size: 24rpx;
}
.address-detail-input.data-v-a9a206dd {
  border: 1rpx solid #e5e5e5;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  background-color: #f8f8f8;
  min-height: 100rpx;
}
.map-container.data-v-a9a206dd {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}
.map-placeholder.data-v-a9a206dd {
  width: 100%;
  height: 300rpx;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.map-image.data-v-a9a206dd {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}
.map-tip.data-v-a9a206dd {
  font-size: 28rpx;
  color: #999;
}
.map-btn.data-v-a9a206dd {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #4A90E2, #357abd);
  color: #fff;
  font-size: 28rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}
.address-modal-footer.data-v-a9a206dd {
  display: flex;
  padding: 24rpx;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}
.address-cancel-btn.data-v-a9a206dd,
.address-confirm-btn.data-v-a9a206dd {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  border: none;
}
.address-cancel-btn.data-v-a9a206dd {
  background-color: #f5f5f5;
  color: #666;
}
.address-confirm-btn.data-v-a9a206dd {
  background: linear-gradient(135deg, #4A90E2, #357abd);
  color: #fff;
}

/* 名片风格横向滚动样式 */
.style-scroll-view.data-v-a9a206dd {
  width: 100%;
  white-space: nowrap;
  margin: 10rpx 0;
  height: 260rpx;
  padding-bottom: 50rpx;
}
.style-scroll-content.data-v-a9a206dd {
  display: inline-flex;
  padding: 10rpx 0;
  height: 100%;
}
.style-option-item.data-v-a9a206dd {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  margin-right: 24rpx;
  width: 160rpx;
  transition: all 0.2s;
  position: relative;
  padding-bottom: 50rpx;
  height: 200rpx; /* 固定高度 */
}
.style-option-preview.data-v-a9a206dd {
  width: 160rpx;
  height: 200rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.12);
  padding: 16rpx;
  box-sizing: border-box;
  transition: all 0.2s;
  position: relative;
  overflow: hidden;
}
.style-preview-content.data-v-a9a206dd {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.style-preview-avatar.data-v-a9a206dd {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: rgba(0,0,0,0.2);
  margin-bottom: 16rpx;
}
.style-preview-info.data-v-a9a206dd {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
.style-preview-name.data-v-a9a206dd {
  height: 16rpx;
  width: 80rpx;
  background-color: rgba(0,0,0,0.2);
  border-radius: 4rpx;
}
.style-preview-position.data-v-a9a206dd {
  height: 12rpx;
  width: 60rpx;
  background-color: rgba(0,0,0,0.15);
  border-radius: 4rpx;
}
.style-option-label.data-v-a9a206dd {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50rpx;
  line-height: 1.2;
  padding: 0 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-word;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  background-color: rgba(255,255,255,0.7);
  border-radius: 0 0 12rpx 12rpx;
  margin-top: 4rpx;
}
.style-option-item.active .style-option-preview.data-v-a9a206dd {
  transform: scale(1.05);
  box-shadow: 0 4rpx 16rpx rgba(74,110,242,0.5);
  border: 3rpx solid #4a6ef2;
}
.style-option-item.active .style-option-label.data-v-a9a206dd {
  color: #4a6ef2;
  font-weight: bold;
}

/* 保存按钮样式 */
.save-btn-float.data-v-a9a206dd {
  position: fixed;
  left: 5%;
  width: 90%;
  bottom: 40rpx;
  height: 88rpx;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #4A90E2, #357abd);
  color: #fff;
  font-size: 30rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
  border: none;
  opacity: 0;
  transform: translateY(100%);
  transition: all 0.3s cubic-bezier(.4,0,.2,1);
  z-index: 9999;
}
.save-btn-float.show.data-v-a9a206dd {
  opacity: 1;
  transform: translateY(0);
}

/* 名片样式 */
.classic-card-v2.data-v-a9a206dd {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.12);
  padding: 32rpx 30rpx 28rpx 30rpx;
  position: relative;
  border: 1rpx solid #e0e0e0;
  overflow: hidden; /* 确保背景图片不会溢出 */
}

/* 背景图片样式 */
.classic-card-v2-bg-image.data-v-a9a206dd {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

/* 背景图片覆盖层 */
.classic-card-v2-overlay.data-v-a9a206dd {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

/* 内容容器 */
.classic-card-v2-content.data-v-a9a206dd {
  position: relative;
  z-index: 2;
}

/* 为背景图片添加暗色遮罩，提高文字可读性 */
.classic-card-v2.data-v-a9a206dd {
  isolation: isolate; /* 创建新的层叠上下文 */
}
.classic-card-v2.data-v-a9a206dd::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;
}
.classic-card-v2[style*="background-image"].data-v-a9a206dd::before {
  opacity: 1;
}
.classic-card-v2-header.data-v-a9a206dd,
.classic-card-v2-divider.data-v-a9a206dd,
.classic-card-v2-contact-list.data-v-a9a206dd {
  position: relative;
  z-index: 2;
}
.classic-card-v2-header.data-v-a9a206dd {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
}
.classic-card-v2-info.data-v-a9a206dd {
  flex: 1;
  min-width: 0;
}
.classic-card-v2-name-row.data-v-a9a206dd {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}
.classic-card-v2-name.data-v-a9a206dd {
  font-size: 48rpx;
  font-weight: bold;
  color: #222;
  margin-right: 18rpx;
}
.classic-card-v2-position.data-v-a9a206dd {
  font-size: 28rpx;
  border-radius: 24rpx;
  padding: 4rpx 20rpx;
  margin-left: 8rpx;
  transition: all 0.3s ease;
}
.classic-card-v2-company.data-v-a9a206dd {
  font-size: 28rpx;
  color: #4a6ef2;
  margin-bottom: 0;
}
.classic-card-v2-avatar-box.data-v-a9a206dd {
  position: relative;
  width: 120rpx;
  height: 120rpx;
}
.classic-card-v2-avatar-bg.data-v-a9a206dd {
  background: #f7bfa3;
  border-radius: 50%;
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.classic-card-v2-avatar.data-v-a9a206dd {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 2rpx solid rgba(255,255,255,0.8);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}
.classic-card-v2-cert.data-v-a9a206dd {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #fff;
  border: 4rpx solid #fff;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
}
.classic-card-v2-divider.data-v-a9a206dd {
  height: 1rpx;
  background: #f0f0f0;
  margin: 24rpx 0 18rpx 0;
}
.classic-card-v2-contact-list.data-v-a9a206dd {
  margin-top: 0;
}
.classic-card-v2-contact-item.data-v-a9a206dd {
  display: flex;
  align-items: center;
  margin-bottom: 18rpx;
}
.classic-card-v2-icon.data-v-a9a206dd {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}
.classic-card-v2-label.data-v-a9a206dd {
  font-size: 28rpx;
  color: #888;
  margin-right: 8rpx;
}
.classic-card-v2-value.data-v-a9a206dd {
  font-size: 28rpx;
  color: #4a6ef2;
  word-break: break-all;
}

/* 小屏幕设备样式调整 - 进一步减小 */
@media screen and (max-height: 700px) {
.classic-card-v2.data-v-a9a206dd {
    padding: 24rpx 24rpx 20rpx 24rpx;
}
.tab-item.data-v-a9a206dd {
    padding: 16rpx 0;
    font-size: 26rpx;
}
.form-section.data-v-a9a206dd {
    padding: 16rpx 14rpx 6rpx 14rpx;
}
.form-title.data-v-a9a206dd {
    margin-bottom: 10rpx;
}
.form-item.data-v-a9a206dd {
    margin-bottom: 10rpx;
}
}

/* 确保名片预览中的文字颜色可以被动态修改 */
.classic-card-v2-name.data-v-a9a206dd,
.classic-card-v2-company.data-v-a9a206dd,
.classic-card-v2-value.data-v-a9a206dd {
  transition: color 0.3s ease;
}

/* 头像选择器样式 */
.avatar-item.data-v-a9a206dd {
  align-items: flex-start;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx dashed #e0e0e0;
}
.avatar-selector.data-v-a9a206dd {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 30rpx;
}
.avatar-preview.data-v-a9a206dd {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  border: 4rpx solid #f0f0f0;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
  margin-bottom: 20rpx;
}
.avatar-buttons.data-v-a9a206dd {
  display: flex;
  flex-direction: row;
  gap: 20rpx;
  margin-top: 16rpx;
  align-items: center;
  justify-content: center;
}
.avatar-btn.data-v-a9a206dd {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(135deg, #1aad19, #2dc653);
  color: white;
  font-size: 24rpx;
  padding: 16rpx 20rpx;
  border-radius: 16rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(26, 173, 25, 0.3);
  min-width: 120rpx;
}
.avatar-btn.data-v-a9a206dd::after {
  border: none;
}
.wechat-avatar-btn.data-v-a9a206dd {
  background: linear-gradient(135deg, #1aad19, #2dc653);
}
.btn-icon.data-v-a9a206dd {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}
.btn-text.data-v-a9a206dd {
  font-size: 26rpx;
  font-weight: 500;
  color: white;
}
.avatar-change-btn.data-v-a9a206dd {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: white;
  padding: 12rpx 32rpx;
  border-radius: 50rpx;
  font-size: 26rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
  transition: all 0.3s ease;
  text-align: center;
  height: 60rpx;
  line-height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.avatar-change-btn.data-v-a9a206dd:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.4);
  background: linear-gradient(135deg, #389e0d, #52c41a);
}

/* 微信小程序button组件样式重置 */
.avatar-change-btn.data-v-a9a206dd::after {
  border: none;
}

/* 悬停效果（支持的平台） */
.avatar-change-btn.data-v-a9a206dd:hover {
  background: linear-gradient(135deg, #73d13d, #95de64);
  box-shadow: 0 6rpx 16rpx rgba(82, 196, 26, 0.4);
  transform: translateY(-2rpx);
}







/* 底部安全区域，防止保存按钮遮挡内容 */
.bottom-safe-area.data-v-a9a206dd {
  height: 120rpx;
  width: 100%;
}

/* 为背景图片场景单独添加类 */
.classic-card-v2-with-bg-image.data-v-a9a206dd::before {
  opacity: 1 !important;
}

/* 个人介绍样式 */
.profile-section.data-v-a9a206dd {
	background: linear-gradient(145deg, #ffffff, #f8faff);
	border-radius: 20rpx;
	padding: 0;
	margin-top: 30rpx;
	box-shadow: 0 10rpx 30rpx rgba(59, 130, 246, 0.08);
	position: relative;
	overflow: hidden;
	border: 1rpx solid rgba(59, 130, 246, 0.1);
}
.profile-section.data-v-a9a206dd::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 1rpx;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}
.profile-header.data-v-a9a206dd {
	padding: 28rpx 24rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1rpx solid rgba(59, 130, 246, 0.08);
	background: linear-gradient(90deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.02));
	position: relative;
}
.profile-header.data-v-a9a206dd::after {
	content: "";
	position: absolute;
	bottom: 0;
	left: 24rpx;
	right: 24rpx;
	height: 1rpx;
	background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
}
.profile-header-left.data-v-a9a206dd {
	display: flex;
	align-items: center;
}
.profile-icon-wrapper.data-v-a9a206dd {
	width: 48rpx;
	height: 48rpx;
	border-radius: 12rpx;
	background: linear-gradient(135deg, #3b82f6, #60a5fa);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.2);
}
.profile-icon.data-v-a9a206dd {
	width: 32rpx;
	height: 32rpx;
	filter: brightness(0) invert(1);
}
.profile-title.data-v-a9a206dd {
	font-size: 34rpx;
	font-weight: 600;
	color: #1e40af;
	position: relative;
	letter-spacing: 2rpx;
}
.profile-header-badge.data-v-a9a206dd {
	background: linear-gradient(90deg, #3b82f6, #60a5fa);
	border-radius: 30rpx;
	padding: 8rpx 20rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.15);
}
.profile-badge-text.data-v-a9a206dd {
	font-size: 22rpx;
	color: white;
	font-weight: 500;
	letter-spacing: 1rpx;
}
.badge-dot.data-v-a9a206dd {
	width: 8rpx;
	height: 8rpx;
	border-radius: 50%;
	background: #ffffff;
	margin-left: 8rpx;
	box-shadow: 0 0 4rpx rgba(255, 255, 255, 0.8);
}
.profile-content.data-v-a9a206dd {
	padding: 30rpx 24rpx;
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}
.profile-desc-container.data-v-a9a206dd {
	background: rgba(59, 130, 246, 0.02);
	border-radius: 16rpx;
	padding: 24rpx;
	border-left: 4rpx solid rgba(59, 130, 246, 0.2);
	position: relative;
}
.profile-desc-decoration-top.data-v-a9a206dd {
	position: absolute;
	top: 12rpx;
	right: 12rpx;
	width: 20rpx;
	height: 20rpx;
	border-top: 3rpx solid rgba(59, 130, 246, 0.2);
	border-right: 3rpx solid rgba(59, 130, 246, 0.2);
}
.profile-desc-decoration-bottom.data-v-a9a206dd {
	position: absolute;
	bottom: 12rpx;
	right: 12rpx;
	width: 20rpx;
	height: 20rpx;
	border-bottom: 3rpx solid rgba(59, 130, 246, 0.2);
	border-right: 3rpx solid rgba(59, 130, 246, 0.2);
}
.profile-desc.data-v-a9a206dd {
	font-size: 28rpx;
	color: #4b5563;
	line-height: 1.8;
	text-align: justify;
	letter-spacing: 0.5rpx;
}
.profile-section-header.data-v-a9a206dd {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}
.section-header-line.data-v-a9a206dd {
	width: 8rpx;
	height: 28rpx;
	background: linear-gradient(to bottom, #3b82f6, #60a5fa);
	border-radius: 4rpx;
	margin-right: 12rpx;
}
.section-header-icon.data-v-a9a206dd {
	font-size: 28rpx;
	margin-right: 10rpx;
}
.section-header-title.data-v-a9a206dd {
	font-size: 30rpx;
	color: #1e40af;
	font-weight: 600;
	letter-spacing: 1rpx;
}
.profile-tags-container.data-v-a9a206dd {
	margin-top: 10rpx;
}
.profile-tags-wrapper.data-v-a9a206dd {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
	margin-top: 20rpx;
}
.profile-tag-item.data-v-a9a206dd {
	display: flex;
	align-items: center;
	background: linear-gradient(145deg, #ffffff, #f8faff);
	padding: 12rpx 20rpx;
	border-radius: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.06);
	border: 1rpx solid rgba(59, 130, 246, 0.08);
	transition: all 0.3s ease;
}
.profile-tag-item.data-v-a9a206dd:active {
	transform: translateY(-4rpx);
	box-shadow: 0 8rpx 20rpx rgba(59, 130, 246, 0.1);
}
.tag-dot.data-v-a9a206dd {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #3b82f6, #60a5fa);
	margin-right: 10rpx;
	box-shadow: 0 2rpx 6rpx rgba(59, 130, 246, 0.2);
}
.tag-text.data-v-a9a206dd {
	font-size: 26rpx;
	color: #1e40af;
	font-weight: 500;
}
.profile-achievements-container.data-v-a9a206dd {
	margin-top: 10rpx;
}
.profile-achievements-list.data-v-a9a206dd {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	margin-top: 20rpx;
}
.profile-achievement-item.data-v-a9a206dd {
	display: flex;
	align-items: flex-start;
	background: linear-gradient(145deg, #ffffff, #f8faff);
	padding: 20rpx;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.06);
	border: 1rpx solid rgba(59, 130, 246, 0.08);
	transition: all 0.3s ease;
}
.profile-achievement-item.data-v-a9a206dd:active {
	transform: translateY(-4rpx);
	box-shadow: 0 8rpx 20rpx rgba(59, 130, 246, 0.1);
}
.achievement-icon.data-v-a9a206dd {
	width: 44rpx;
	height: 44rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #3b82f6, #60a5fa);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
	flex-shrink: 0;
	box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.15);
}
.achievement-icon-text.data-v-a9a206dd {
	font-size: 22rpx;
	color: white;
	font-weight: bold;
}
.achievement-content.data-v-a9a206dd {
	flex: 1;
	position: relative;
}
.achievement-text.data-v-a9a206dd {
	font-size: 28rpx;
	color: #1e40af;
	font-weight: 500;
	line-height: 1.6;
}
.achievement-decoration.data-v-a9a206dd {
	height: 2rpx;
	width: 60rpx;
	background: linear-gradient(90deg, #3b82f6, rgba(59, 130, 246, 0.2));
	margin-top: 12rpx;
}
.profile-education-container.data-v-a9a206dd {
	margin-top: 10rpx;
}
.profile-education-timeline.data-v-a9a206dd {
	margin-top: 20rpx;
}
.profile-education-item.data-v-a9a206dd {
	display: flex;
	margin-bottom: 24rpx;
}
.profile-education-item.data-v-a9a206dd:last-child {
	margin-bottom: 0;
}
.education-timeline-left.data-v-a9a206dd {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-right: 16rpx;
	padding-top: 8rpx;
}
.timeline-dot.data-v-a9a206dd {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #3b82f6, #60a5fa);
	box-shadow: 0 0 0 6rpx rgba(59, 130, 246, 0.1);
}
.timeline-line.data-v-a9a206dd {
	width: 2rpx;
	flex: 1;
	background: linear-gradient(to bottom, #3b82f6, rgba(59, 130, 246, 0.2));
	margin: 8rpx 0;
}
.education-content.data-v-a9a206dd {
	background: linear-gradient(145deg, #ffffff, #f8faff);
	padding: 20rpx;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.06);
	border: 1rpx solid rgba(59, 130, 246, 0.08);
	flex: 1;
}
.education-school.data-v-a9a206dd {
	font-size: 28rpx;
	color: #1e40af;
	font-weight: 600;
	margin-bottom: 8rpx;
	display: block;
}
.education-major.data-v-a9a206dd {
	font-size: 26rpx;
	color: #4b5563;
	margin-bottom: 8rpx;
	display: block;
}
.education-time.data-v-a9a206dd {
	font-size: 24rpx;
	color: #6b7280;
	display: block;
}
.profile-footer.data-v-a9a206dd {
	padding: 16rpx 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-top: 1rpx solid rgba(59, 130, 246, 0.05);
}
.footer-line.data-v-a9a206dd {
	width: 60rpx;
	height: 2rpx;
	background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
}
.footer-dot.data-v-a9a206dd {
	width: 6rpx;
	height: 6rpx;
	border-radius: 50%;
	background: rgba(59, 130, 246, 0.3);
	margin: 0 4rpx;
}

/* 删除企业版/专业版限制遮罩样式 */

/* 添加禁用状态样式 */
.form-input.data-v-a9a206dd:disabled {
  background-color: #f5f5f5;
  color: #999;
}
.avatar-upload-btn.disabled.data-v-a9a206dd, .address-preview.disabled.data-v-a9a206dd {
  opacity: 0.6;
  background: #f5f5f5;
  color: #999;
}

/* 删除会员提示条样式 */

/* CSS-based contact icons */
.contact-icon.data-v-a9a206dd {
  width: 24px;
  height: 24px;
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Phone icon */
.phone-icon.data-v-a9a206dd::before {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-radius: 2px;
  transform: rotate(45deg);
  top: 2px;
  left: 4px;
}
.phone-icon.data-v-a9a206dd::after {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
  top: 8px;
  left: 8px;
}

/* Chat icon */
.chat-icon.data-v-a9a206dd::before {
  content: '';
  position: absolute;
  width: 18px;
  height: 14px;
  border: 2px solid currentColor;
  border-radius: 4px;
  top: 4px;
  left: 3px;
}
.chat-icon.data-v-a9a206dd::after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 6px solid currentColor;
  bottom: 2px;
  left: 7px;
}

/* Email icon */
.email-icon.data-v-a9a206dd::before {
  content: '';
  position: absolute;
  width: 18px;
  height: 14px;
  border: 2px solid currentColor;
  border-radius: 2px;
  top: 4px;
  left: 3px;
}
.email-icon.data-v-a9a206dd::after {
  content: '';
  position: absolute;
  width: 14px;
  height: 8px;
  border-left: 2px solid currentColor;
  border-right: 2px solid currentColor;
  border-top: 2px solid currentColor;
  border-radius: 2px 2px 0 0;
  transform: rotate(-45deg);
  top: 7px;
  left: 5px;
}

/* Location icon */
.location-icon.data-v-a9a206dd::before {
  content: '';
  position: absolute;
  width: 14px;
  height: 14px;
  border: 2px solid currentColor;
  border-radius: 50% 50% 50% 0;
  transform: rotate(-45deg);
  top: 2px;
  left: 5px;
}
.location-icon.data-v-a9a206dd::after {
  content: '';
  position: absolute;
  width: 4px;
  height: 4px;
  background-color: currentColor;
  border-radius: 50%;
  top: 7px;
  left: 10px;
}
