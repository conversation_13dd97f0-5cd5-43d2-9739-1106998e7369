<?php
declare(strict_types=1);

namespace app\api\controller;

use think\Request;
use think\admin\Storage;
use think\admin\model\SystemFile;
use think\facade\Db;
use app\common\model\BusinessUser;

/**
 * 文件上传API控制器
 */
class Upload
{

    /**
     * 上传头像
     */
    public function avatar(Request $request)
    {
        try {
            $file = $request->file('avatar');

            if (!$file) {
                return json(['code' => 400, 'message' => '请选择要上传的文件']);
            }

            // 验证文件类型
            $allowedTypes = ['jpg', 'jpeg', 'png', 'gif'];
            $extension = strtolower($file->getOriginalExtension());

            if (!in_array($extension, $allowedTypes)) {
                return json(['code' => 400, 'message' => '只支持 JPG、PNG、GIF 格式的图片']);
            }

            // 验证文件大小（2MB）
            if ($file->getSize() > 2 * 1024 * 1024) {
                return json(['code' => 400, 'message' => '文件大小不能超过2MB']);
            }

            // 获取当前用户
            $user = $this->getCurrentUser($request);

            if (!$user) {
                // 记录调试信息
                $token = $request->header('Authorization');
                error_log("头像上传失败 - 用户未登录: token=" . ($token ? '有token' : '无token'));

                // 临时允许未登录用户上传（仅用于调试）
                // TODO: 生产环境中应该移除这个临时方案
                error_log("临时允许未登录用户上传头像（调试模式）");
            } else {
                // 记录成功获取用户信息
                error_log("头像上传 - 用户ID: {$user->id}, 用户名: {$user->name}");
            }

            // 使用 ThinkAdmin 存储引擎
            $storage = Storage::instance();

            // 读取文件内容
            $fileContent = file_get_contents($file->getPathname());
            $fileHash = md5($fileContent);

            // 删除用户的旧头像文件（如果存在）
            $this->deleteUserOldAvatar($user, $storage);

            // 按日期分类的路径前缀
            $datePrefix = 'avatars/' . date('Y/m/d');

            // 使用用户ID + 时间戳确保每次上传都是新文件
            $timestamp = time();
            $saveFileName = $datePrefix . "/user_{$user->id}_{$timestamp}.{$extension}";

            // 保存新头像文件
            $info = $storage->set($saveFileName, $fileContent, false, $file->getOriginalName());

            if (isset($info['url'])) {
                // 确保URL使用HTTPS协议
                $httpsUrl = $this->ensureHttpsUrl($info['url']);

                // 更新用户头像
                $user->avatar = $httpsUrl;
                $user->save();

                return json([
                    'code' => 200,
                    'message' => '上传成功',
                    'data' => [
                        'url' => $httpsUrl
                    ]
                ]);
            } else {
                return json(['code' => 500, 'message' => '文件保存失败']);
            }

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '上传失败：' . $e->getMessage()]);
        }
    }

    /**
     * 确保URL使用HTTPS协议
     * @param string $url
     * @return string
     */
    private function ensureHttpsUrl($url)
    {
        if (empty($url)) {
            return $url;
        }

        // 如果是相对路径，添加当前域名
        if (strpos($url, '//') === 0) {
            return 'https:' . $url;
        }

        // 如果是HTTP，转换为HTTPS
        if (strpos($url, 'http://') === 0) {
            return str_replace('http://', 'https://', $url);
        }

        // 如果是相对路径，添加完整域名
        if (strpos($url, '/') === 0) {
            $domain = $_SERVER['HTTP_HOST'] ?? 'mp.hwkj01.xin';
            return 'https://' . $domain . $url;
        }

        return $url;
    }

    /**
     * 获取当前用户
     */
    private function getCurrentUser($request)
    {
        $token = $request->header('Authorization');
        if (!$token) {
            return null;
        }

        // 移除 "Bearer " 前缀
        $token = str_replace('Bearer ', '', $token);

        try {
            return BusinessUser::validateAccessToken($token);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 确保用户头像表存在
     */
    private function ensureUserAvatarsTable()
    {
        try {
            // 检查表是否存在
            $tables = Db::query("SHOW TABLES LIKE 'user_avatars'");

            if (empty($tables)) {
                // 创建用户头像管理表
                $sql = "
                CREATE TABLE `user_avatars` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `user_id` varchar(64) NOT NULL COMMENT '用户标识',
                    `avatar_url` varchar(500) NOT NULL COMMENT '头像URL',
                    `file_path` varchar(500) NOT NULL COMMENT '文件存储路径',
                    `file_hash` varchar(64) NOT NULL COMMENT '文件MD5哈希',
                    `file_size` int(11) NOT NULL DEFAULT '0' COMMENT '文件大小（字节）',
                    `mime_type` varchar(100) DEFAULT NULL COMMENT '文件MIME类型',
                    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `user_id` (`user_id`),
                    KEY `file_hash` (`file_hash`),
                    KEY `created_at` (`created_at`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户头像管理表';
                ";

                Db::execute($sql);
                error_log("用户头像表 user_avatars 创建成功");
            }
        } catch (\Exception $e) {
            error_log("创建用户头像表失败: " . $e->getMessage());
            // 不抛出异常，让程序继续执行
        }
    }

    /**
     * 检查是否为相同头像
     */
    private function isSameAvatar($user, $newFileHash)
    {
        // 如果用户有头像哈希记录，比较哈希值
        if (!empty($user->avatar_hash)) {
            return $user->avatar_hash === $newFileHash;
        }

        return false;
    }

    /**
     * 删除用户的旧头像文件
     */
    private function deleteUserOldAvatar($user, $storage)
    {
        try {
            // 如果没有头像，直接返回
            if (empty($user->avatar)) {
                return;
            }

            // 从URL提取文件路径
            $oldFilePath = $this->extractFilePathFromUrl($user->avatar);
            if ($oldFilePath && $storage->has($oldFilePath)) {
                $storage->del($oldFilePath);
            }

            // 如果直接删除失败，尝试查找可能的旧头像文件
            $this->findAndDeleteUserAvatars($user, $storage);

        } catch (\Exception $e) {
            // 删除失败时不影响主流程
        }
    }

    /**
     * 查找并删除用户的所有头像文件
     */
    private function findAndDeleteUserAvatars($user, $storage)
    {
        try {
            $extensions = ['jpg', 'jpeg', 'png', 'gif'];
            $userId = $user->id;

            // 查找最近30天的可能路径
            for ($i = 0; $i < 30; $i++) {
                $date = date('Y/m/d', strtotime("-{$i} days"));

                foreach ($extensions as $ext) {
                    $possiblePath = "avatars/{$date}/user_{$userId}.{$ext}";

                    if ($storage->has($possiblePath)) {
                        $storage->del($possiblePath);
                        error_log("删除旧头像文件: {$possiblePath}");
                    }
                }
            }

            // 也检查没有日期分类的旧格式
            foreach ($extensions as $ext) {
                $oldFormatPath = "avatars/user_{$userId}.{$ext}";
                if ($storage->has($oldFormatPath)) {
                    $storage->del($oldFormatPath);
                    error_log("删除旧格式头像文件: {$oldFormatPath}");
                }
            }

        } catch (\Exception $e) {
            error_log("查找删除用户头像失败: " . $e->getMessage());
        }
    }

    /**
     * 更新用户头像信息
     */
    private function updateUserAvatarInfo($user, $newAvatarUrl, $fileHash, $filePath = null)
    {
        try {
            // 更新用户头像字段和哈希值
            $user->avatar = $newAvatarUrl;

            // 如果用户表有 avatar_hash 字段，则更新它
            if (isset($user->avatar_hash)) {
                $user->avatar_hash = $fileHash;
            }

            // 如果用户表有 avatar_path 字段，则更新它
            if (isset($user->avatar_path) && $filePath) {
                $user->avatar_path = $filePath;
            }

            $user->save();

        } catch (\Exception $e) {
            error_log("更新用户头像信息失败: " . $e->getMessage());
        }
    }



    /**
     * 删除用户的旧背景图片文件
     */
    private function deleteUserOldBackgroundImage($user, $storage)
    {
        try {
            // 如果没有背景图片，直接返回
            if (empty($user->background_image)) {
                return;
            }

            // 从URL提取文件路径
            $oldFilePath = $this->extractFilePathFromUrl($user->background_image);
            if ($oldFilePath && $storage->has($oldFilePath)) {
                $storage->del($oldFilePath);
            }

        } catch (\Exception $e) {
            // 删除失败时不影响主流程
        }
    }





    /**
     * 删除旧头像文件
     */
    private function deleteOldAvatarFile($avatarUrl)
    {
        try {
            // 跳过默认头像和外部URL
            if (empty($avatarUrl) ||
                strpos($avatarUrl, '/static/default-avatar.png') !== false ||
                strpos($avatarUrl, 'http://tmp/') === 0) {
                return;
            }

            $storage = Storage::instance();

            // 从URL中提取文件路径
            $filePath = $this->extractFilePathFromUrl($avatarUrl);

            if ($filePath && $storage->has($filePath)) {
                $storage->del($filePath);
                error_log("删除旧头像文件成功: {$filePath}");
            }
        } catch (\Exception $e) {
            error_log("删除旧头像文件失败: {$avatarUrl}, 错误: " . $e->getMessage());
        }
    }

    /**
     * 从URL中提取文件路径
     */
    private function extractFilePathFromUrl($url)
    {
        if (empty($url)) {
            return null;
        }

        // 处理七牛云等云存储URL
        if (strpos($url, 'http') === 0) {
            $path = parse_url($url, PHP_URL_PATH);

            // 移除可能的前缀路径
            if (strpos($path, '/upload/') === 0) {
                return substr($path, 8); // 移除 "/upload/"
            }

            // 对于七牛云等，可能直接就是文件路径
            return ltrim($path, '/');
        }

        // 处理相对路径
        if (strpos($url, '/') === 0) {
            if (strpos($url, '/upload/') === 0) {
                return substr($url, 8);
            }
            return ltrim($url, '/');
        }

        // 直接返回（可能已经是文件路径）
        return $url;
    }

    /**
     * 上传图片（主要用于背景图片）
     */
    public function image(Request $request)
    {
        try {
            $file = $request->file('image');

            if (!$file) {
                return json(['code' => 400, 'message' => '请选择要上传的文件']);
            }

            // 验证文件类型
            $allowedTypes = ['jpg', 'jpeg', 'png', 'gif'];
            $extension = strtolower($file->getOriginalExtension());

            if (!in_array($extension, $allowedTypes)) {
                return json(['code' => 400, 'message' => '只支持 JPG、PNG、GIF 格式的图片']);
            }

            // 验证文件大小（5MB）
            if ($file->getSize() > 5 * 1024 * 1024) {
                return json(['code' => 400, 'message' => '文件大小不能超过5MB']);
            }

            // 获取当前用户
            $user = $this->getCurrentUser($request);

            if (!$user) {
                return json(['code' => 401, 'message' => '请先登录后上传图片']);
            }

            // 使用 ThinkAdmin 存储引擎
            $storage = Storage::instance();

            // 读取文件内容
            $fileContent = file_get_contents($file->getPathname());

            // 删除用户的旧背景图片文件（如果存在）
            $this->deleteUserOldBackgroundImage($user, $storage);

            // 按日期分类的路径前缀 + 用户ID + 时间戳确保唯一性
            $datePrefix = 'backgrounds/' . date('Y/m/d');
            $timestamp = time();
            $saveFileName = $datePrefix . "/user_{$user->id}_{$timestamp}.{$extension}";

            // 保存文件到存储引擎
            $info = $storage->set($saveFileName, $fileContent, false, $file->getOriginalName());

            if (isset($info['url'])) {
                // 确保URL使用HTTPS协议
                $httpsUrl = $this->ensureHttpsUrl($info['url']);

                // 更新用户背景图片字段
                $user->background_image = $httpsUrl;
                $user->save();

                return json([
                    'code' => 200,
                    'message' => '上传成功',
                    'data' => [
                        'url' => $httpsUrl
                    ]
                ]);
            } else {
                return json(['code' => 500, 'message' => '文件保存失败']);
            }
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '上传失败：' . $e->getMessage()]);
        }
    }

    /**
     * 批量上传图片
     */
    public function batchImages(Request $request)
    {
        try {
            $files = $request->file('images');

            if (!$files || !is_array($files)) {
                return json(['code' => 400, 'message' => '请选择要上传的文件']);
            }

            if (count($files) > 9) {
                return json(['code' => 400, 'message' => '一次最多上传9张图片']);
            }

            $uploadedFiles = [];
            $storage = Storage::instance();
            $allowedTypes = ['jpg', 'jpeg', 'png', 'gif'];

            foreach ($files as $file) {
                $extension = strtolower($file->getOriginalExtension());

                if (!in_array($extension, $allowedTypes)) {
                    continue; // 跳过不支持的文件类型
                }

                if ($file->getSize() > 5 * 1024 * 1024) {
                    continue; // 跳过过大的文件
                }

                try {
                    // 读取文件内容
                    $fileContent = file_get_contents($file->getPathname());
                    $fileHash = md5($fileContent);

                    // 按日期分类的路径前缀
                    $datePrefix = 'images/' . date('Y/m/d');

                    // 生成文件名（包含日期路径）
                    $saveFileName = Storage::name($fileContent, $extension, $datePrefix, 'md5');

                    // 检查是否已存在相同文件（防重复）
                    if ($storage->has($saveFileName)) {
                        // 文件已存在，直接返回URL
                        $existingInfo = $storage->info($saveFileName);
                        $httpsUrl = $this->ensureHttpsUrl($existingInfo['url']);

                        $uploadedFiles[] = [
                            'url' => $httpsUrl
                        ];
                        continue;
                    }

                    // 保存文件到存储引擎
                    $info = $storage->set($saveFileName, $fileContent, false, $file->getOriginalName());

                    if (isset($info['url'])) {
                        // 确保URL使用HTTPS协议
                        $httpsUrl = $this->ensureHttpsUrl($info['url']);

                        $uploadedFiles[] = [
                            'url' => $httpsUrl
                        ];
                    }
                } catch (\Exception $e) {
                    // 单个文件上传失败，继续处理下一个
                    continue;
                }
            }
            
            return json([
                'code' => 200,
                'message' => '上传完成',
                'data' => [
                    'files' => $uploadedFiles,
                    'count' => count($uploadedFiles)
                ]
            ]);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '上传失败：' . $e->getMessage()]);
        }
    }

    /**
     * 清理旧文件（可选功能）
     * 删除超过指定天数的文件和孤儿文件
     */
    public function cleanup()
    {
        try {
            $cleanupDays = 30; // 保留30天
            $cutoffTime = time() - ($cleanupDays * 24 * 60 * 60);

            $storage = Storage::instance();
            $deletedCount = 0;
            $orphanCount = 0;

            // 清理头像目录
            $this->cleanupDirectory($storage, 'avatars', $cutoffTime, $deletedCount);

            // 清理背景图片目录
            $this->cleanupDirectory($storage, 'backgrounds', $cutoffTime, $deletedCount);

            // 清理通用图片目录
            $this->cleanupDirectory($storage, 'images', $cutoffTime, $deletedCount);

            // 清理孤儿头像文件（数据库中没有记录的文件）
            $orphanCount = $this->cleanupOrphanAvatars($storage);

            return json([
                'code' => 200,
                'message' => '清理完成',
                'data' => [
                    'deleted_count' => $deletedCount,
                    'orphan_count' => $orphanCount,
                    'cleanup_days' => $cleanupDays
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '清理失败：' . $e->getMessage()]);
        }
    }

    /**
     * 清理孤儿头像文件
     */
    private function cleanupOrphanAvatars($storage)
    {
        $orphanCount = 0;

        try {
            // 获取数据库中所有用户头像和背景图片URL
            $avatarUrls = Db::table('bc_users')->where('avatar', '<>', '')->column('avatar');
            $backgroundUrls = Db::table('bc_users')->where('background_image', '<>', '')->column('background_image');
            $validPaths = [];

            // 从头像URL中提取文件路径
            foreach ($avatarUrls as $url) {
                $path = $this->extractFilePathFromUrl($url);
                if ($path) {
                    $validPaths[] = $path;
                }
            }

            // 从背景图片URL中提取文件路径
            foreach ($backgroundUrls as $url) {
                $path = $this->extractFilePathFromUrl($url);
                if ($path) {
                    $validPaths[] = $path;
                }
            }

            // 对于本地存储，可以扫描目录
            if (get_class($storage) === 'think\\admin\\storage\\LocalStorage') {
                // 清理头像目录
                $avatarPath = public_path('upload/avatars');
                if (is_dir($avatarPath)) {
                    $orphanCount += $this->scanAndCleanOrphans($avatarPath, $validPaths);
                }

                // 清理背景图片目录
                $backgroundPath = public_path('upload/backgrounds');
                if (is_dir($backgroundPath)) {
                    $orphanCount += $this->scanAndCleanOrphans($backgroundPath, $validPaths);
                }

                // 清理通用图片目录
                $imagePath = public_path('upload/images');
                if (is_dir($imagePath)) {
                    $orphanCount += $this->scanAndCleanOrphans($imagePath, $validPaths);
                }
            }

        } catch (\Exception $e) {
            error_log("清理孤儿文件失败: " . $e->getMessage());
        }

        return $orphanCount;
    }

    /**
     * 扫描并清理孤儿文件
     */
    private function scanAndCleanOrphans($dir, $validPaths)
    {
        $orphanCount = 0;

        if (!is_dir($dir)) return $orphanCount;

        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dir),
            \RecursiveIteratorIterator::LEAVES_ONLY
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $relativePath = str_replace(public_path('upload/'), '', $file->getPathname());
                $relativePath = str_replace('\\', '/', $relativePath);

                // 检查文件是否在数据库记录中
                if (!in_array($relativePath, $validPaths)) {
                    if (unlink($file->getPathname())) {
                        $orphanCount++;
                    }
                }
            }
        }

        return $orphanCount;
    }

    /**
     * 清理指定目录下的旧文件
     */
    private function cleanupDirectory($storage, $baseDir, $cutoffTime, &$deletedCount)
    {
        // 对于本地存储，可以直接操作文件系统
        if (get_class($storage) === 'think\\admin\\storage\\LocalStorage') {
            $uploadPath = public_path('upload/' . $baseDir);
            if (is_dir($uploadPath)) {
                $this->cleanupLocalDirectory($uploadPath, $cutoffTime, $deletedCount);
            }
        }

        // 对于云存储，需要调用相应的API
        // 这里暂时不实现，可以根据需要扩展
    }

    /**
     * 清理本地目录
     */
    private function cleanupLocalDirectory($dir, $cutoffTime, &$deletedCount)
    {
        if (!is_dir($dir)) return;

        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dir),
            \RecursiveIteratorIterator::LEAVES_ONLY
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getMTime() < $cutoffTime) {
                if (unlink($file->getPathname())) {
                    $deletedCount++;
                }
            }
        }

        // 删除空目录
        $this->removeEmptyDirectories($dir);
    }

    /**
     * 删除空目录
     */
    private function removeEmptyDirectories($dir)
    {
        if (!is_dir($dir)) return;

        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dir),
            \RecursiveIteratorIterator::CHILD_FIRST
        );

        foreach ($iterator as $file) {
            if ($file->isDir() && !$file->isDot()) {
                $dirPath = $file->getPathname();
                if ($this->isDirEmpty($dirPath)) {
                    rmdir($dirPath);
                }
            }
        }
    }

    /**
     * 检查目录是否为空
     */
    private function isDirEmpty($dir)
    {
        $handle = opendir($dir);
        while (false !== ($entry = readdir($handle))) {
            if ($entry != "." && $entry != "..") {
                closedir($handle);
                return false;
            }
        }
        closedir($handle);
        return true;
    }


}
