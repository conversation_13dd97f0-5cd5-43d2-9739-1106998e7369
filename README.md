# 电子名片系统

一个专业的数字化名片解决方案，包含小程序前端和完整的后台管理系统。基于 uni-app + ThinkAdmin 架构，提供用户管理、名片展示、会员系统、数据分析等全套功能。

## 🌟 系统特色

- **前后端分离**：小程序前端 + ThinkAdmin 后台管理
- **完整业务闭环**：从用户注册到会员付费的完整流程
- **专业后台管理**：基于 ThinkAdmin 的企业级管理系统
- **RESTful API**：标准化的 API 接口，支持多端调用
- **会员体系**：完整的会员等级和权限管理
- **数据分析**：详细的访问统计和 AI 分析功能

## 功能特性

### 🔐 用户认证
- **选项卡切换**：登录和注册在同一页面，通过选项卡切换
- **简洁表单**：精简的表单设计，首屏完整展示
- **账号密码登录**：简单的手机号密码登录系统
- **用户注册**：手机号、密码注册
- **记住密码**：可选择记住登录信息，提升用户体验
- **工作台登录**：只有访问工作台功能时才需要登录验证
- **微信一键登录**：支持微信授权手机号快速登录，已完成配置和调试

### 📱 名片功能
- **个人名片展示**：展示头像、姓名、职位、联系方式等个人信息
- **智能头像上传**：支持微信头像一键选择和相册上传，自动优化存储
- **个性定制系统**：完整的DIY样式定制，包括背景、颜色、字体、圆角等
- **背景图片上传**：支持自定义背景图片，打造独特的名片风格
- **跨设备同步**：换设备登录自动同步个性定制设置和背景图片
- **名片分享**：支持一键转发分享，以名片为封面图
- **访问统计**：查看名片浏览、分享、收藏次数
- **个人简介**：支持个人简介展示，替代原有的公司介绍
- **实时预览**：编辑时实时预览效果，所见即所得

### ⚙️ 工作台功能
- **个人设置**：编辑个人信息、名片设置
- **数据统计**：查看访问统计、分享数据和AI分析
- **系统设置**：应用设置、关于我们
- **会员专区**：VIP功能特权和开通入口
- **安全退出**：支持安全退出登录功能

### 🌟 会员功能
#### 专业版特权
- **多样化模板**：解锁全部高级设计模板，让名片更加专业
- **AI数据分析**：智能分析访问数据和趋势，优化名片展示效果
- **无限分享**：突破普通版分享限制，无限制分享名片
- **高级个性定制**：解锁更多背景样式和字体选择

#### 企业版特权
- **包含专业版全部功能**：享有专业版的所有特权功能
- **完整个性定制**：解锁所有个性定制功能，包括背景图片上传
- **团队协作**：多人协作管理企业名片，适合销售团队使用
- **企业API**：支持与企业现有系统集成，实现数据对接
- **专属客服**：7×24小时优先服务，快速解决问题
- **自动状态同步**：会员状态跨设备自动同步，无需手动刷新

#### 会员开通方式
- **在线支付**：支持微信支付快速开通
- **激活码激活**：通过预购买的激活码一键开通
- **定价策略**：专业版588元/年，企业版1288元/年
- **会员管理**：在工作台查看会员状态和到期时间

### 🔄 导航设计
- **顶部工作台入口**：在名片页面通过顶部左侧按钮进入工作台
- **简洁导航**：移除底部 tabBar，专注于名片功能
- **单页面应用**：以名片为核心，通过导航按钮访问其他功能

### 🖥️ 后台管理系统
- **ThinkAdmin 框架**：基于 ThinkAdmin 的专业后台管理
- **用户管理**：用户列表、状态管理、权限控制
- **订单管理**：会员订单、支付状态、退款处理
- **激活码管理**：批量生成、使用状态、有效期管理
- **数据统计**：用户增长、收入统计、访问分析
- **系统配置**：应用设置、会员套餐、功能开关

### 🔌 API 接口系统
- **用户认证**：注册、登录、数据库Token验证、微信登录
- **名片功能**：信息获取、分享统计、收藏功能、实时数据同步
- **个性定制**：样式保存、跨设备同步、JSON数据存储
- **文件上传**：智能头像上传、背景图片上传、七牛云存储
- **会员系统**：套餐查询、订单创建、激活码验证、状态自动同步
- **数据分析**：访问统计、AI 分析、趋势报告
- **跨域支持**：完整的 CORS 配置，支持小程序调用
- **状态管理**：优先服务器数据、本地缓存备用、冲突自动解决

## 技术栈

### 前端
- **框架**：uni-app (支持微信小程序、H5、App)
- **开发语言**：Vue.js + JavaScript ES6+
- **样式**：CSS3 + Flexbox + 响应式设计
- **状态管理**：Vuex + 本地存储 + 服务器同步
- **文件上传**：多种上传方式 + 进度显示 + 错误处理
- **图标**：自定义图标 + SVG + 字体图标
- **UI设计**：自适应布局 + 渐变色 + 微动效 + 实时预览

### 后端
- **管理框架**：ThinkAdmin (基于 ThinkPHP 8.0)
- **开发语言**：PHP 8.1+
- **数据库**：MySQL 5.7+ (JSON字段支持)
- **文件存储**：七牛云对象存储 + 本地存储
- **架构**：MVC + RESTful API + 管理后台
- **部署**：Nginx + PHP-FPM + HTTPS
- **依赖管理**：Composer
- **认证方式**：Bearer Token + 中间件验证
- **数据同步**：实时同步 + 冲突解决机制

## 项目结构

```
├── pages/                 # 前端页面文件
│   ├── auth/              # 认证相关页面
│   ├── card/              # 名片相关页面
│   │   ├── card.vue       # 名片主页
│   │   ├── edit.vue       # 名片编辑
│   │   ├── profile.vue    # 个人简介
│   │   └── company.vue    # 企业介绍
│   ├── company/           # 企业相关页面
│   │   └── premium.vue    # 会员专区
│   ├── index/             # 索引页面
│   ├── share/             # 分享页面
│   ├── stats/             # 统计页面
│   │   ├── stats.vue      # 数据统计
│   │   └── ai-analysis.vue# AI分析
│   └── workspace/         # 工作台页面
├── static/                # 静态资源
│   ├── default-avatar.png # 默认头像
│   ├── icons/             # 图标资源
│   ├── template/          # 模板图片
│   └── logo.png           # 应用logo
├── components/            # 公共组件
│   ├── NavBar.vue         # 导航栏组件
│   └── SwitchItem.vue     # 开关组件
├── services/              # 前端服务层
│   ├── appService.js      # 应用服务
│   ├── companyService.js  # 企业服务
│   ├── httpService.js     # HTTP请求封装
│   ├── userService.js     # 用户认证服务 + 会员状态同步
│   └── userInfoService.js # 用户信息同步服务 + 文件上传
├── utils/                 # 工具函数
│   ├── async.js           # 异步处理
│   ├── image.js           # 图片处理
│   ├── storage.js         # 存储工具
│   └── validation.js      # 验证工具
├── server/                # 后端 ThinkAdmin 项目
│   ├── app/               # 应用目录
│   │   ├── admin/         # 后台管理模块
│   │   │   ├── controller/# 后台控制器
│   │   │   │   ├── Business.php        # 电子名片业务管理
│   │   │   │   ├── BusinessUser.php    # 电子名片用户管理
│   │   │   │   ├── BusinessStats.php   # 电子名片数据统计
│   │   │   │   ├── WechatConfig.php    # 微信配置管理
│   │   │   │   └── Test.php            # 系统测试
│   │   │   └── view/      # 后台视图模板
│   │   │       ├── business_user/      # 用户管理视图
│   │   │       │   ├── index.html      # 用户列表
│   │   │       │   └── form.html       # 用户表单
│   │   │       ├── business_stats/     # 数据统计视图
│   │   │       │   └── index.html      # 统计面板
│   │   │       ├── wechat_config/      # 微信配置视图
│   │   │       │   ├── index.html      # 配置列表
│   │   │       │   └── form.html       # 配置表单
│   │   │       └── test/               # 测试页面
│   │   │           └── index.html      # 测试视图
│   │   ├── api/           # API 接口模块
│   │   │   ├── controller/# API 控制器
│   │   │   │   ├── User.php    # 用户 API (信息管理、状态同步)
│   │   │   │   ├── Card.php    # 名片 API (展示、分享、统计)
│   │   │   │   ├── Premium.php # 会员 API (状态查询、权限验证)
│   │   │   │   ├── Upload.php  # 上传 API (头像、背景图片)
│   │   │   │   └── Auth.php    # 认证 API (登录、Token验证)
│   │   │   ├── middleware/# API 中间件
│   │   │   │   ├── Auth.php    # 认证中间件
│   │   │   │   └── Cors.php    # 跨域中间件
│   │   │   └── route/     # API 路由
│   │   └── common/        # 公共模块
│   │       └── model/     # 数据模型
│   │           ├── BusinessUser.php   # 用户模型
│   │           ├── PremiumPlan.php    # 套餐模型
│   │           ├── PremiumOrder.php   # 订单模型
│   │           ├── ActivationCode.php # 激活码模型
│   │           └── CardStats.php      # 统计模型
│   ├── config/            # 配置文件
│   ├── database/          # 数据库文件
│   │   └── migrations/    # 数据库表结构
│   ├── public/            # Web 入口目录
│   │   ├── index.php      # 主入口文件
│   │   ├── admin.php      # 后台入口文件
│   │   ├── api_test.html  # API 测试页面
│   │   └── storage/       # 文件存储目录
│   ├── vendor/            # Composer 依赖
│   └── runtime/           # 运行时目录
├── App.vue                # 应用入口文件
├── pages.json             # 页面配置文件
└── manifest.json          # 应用配置文件
```

## 🚀 快速开始

### 环境要求
- **PHP**: 8.1+ (推荐 8.1.29)
- **MySQL**: 5.7+ (推荐 5.7.38)
- **Web服务器**: Nginx 1.22+ 或 Apache 2.4+
- **Composer**: 2.0+
- **Node.js**: 16+ (用于小程序开发)
- **HBuilderX**: 最新版本 (小程序开发工具)

### 后端安装

#### 1. 克隆项目
```bash
git clone [项目地址]
cd business-card-system
```

#### 2. 安装后端依赖
```bash
cd server
composer install
```

#### 3. 配置数据库
编辑 `config/database.php` 文件，配置数据库连接信息：
```php
'hostname' => '127.0.0.1',
'database' => 'your_database_name',
'username' => 'your_username',
'password' => 'your_password',
```

#### 4. 一键安装系统
访问 `https://mp.hwkj01.xin/install.php` 进行完整安装：

**安装流程：**
1. **环境检查** - 自动检测 PHP 版本、扩展、权限等
2. **数据库配置** - 可视化配置数据库连接信息，支持自动创建数据库
3. **系统安装** - 自动创建所有数据表和管理员账号
4. **安装完成** - 获取访问信息和安全提醒

**安装特性：**
- **智能环境检测**：自动检查 PHP 8.1+、MySQL 5.7+、必要扩展
- **可视化配置**：友好的数据库配置界面，支持连接测试
- **自动数据库创建**：如果数据库不存在会自动创建
- **完整表结构安装**：ThinkAdmin 系统表 + 电子名片业务表
- **默认数据初始化**：管理员账号、会员套餐、测试数据
- **安装锁定机制**：防止重复安装，保护已有数据
- **错误处理和回滚**：安装失败时自动清理，支持重新安装

**安装内容：**
- ThinkAdmin 系统表（用户、菜单、权限、配置等）
- 电子名片业务表（用户、订单、激活码、统计等）
- 个性定制数据表（JSON格式存储用户自定义样式）
- 文件上传日志表（头像、背景图片上传记录）
- 默认管理员账号（admin/admin）
- 会员套餐配置（专业版/企业版）
- 测试激活码和示例数据
- 七牛云存储配置（支持文件上传）

#### 5. 配置 Web 服务器
**Nginx 配置示例：**
```nginx
location ~* (runtime|application)/{
    return 403;
}
location / {
    if (!-e $request_filename){
        rewrite ^(.*)$ /index.php?s=$1 last; break;
    }
}
```

#### 6. 设置目录权限
```bash
chmod -R 755 vendor/
chmod -R 777 runtime/
chmod -R 777 public/storage/
```

### 前端安装

#### 1. 安装小程序依赖
```bash
cd miniprogram
npm install
```

#### 2. 配置 API 地址
编辑 `utils/config.js`：
```javascript
export const API_BASE_URL = 'https://mp.hwkj01.xin/api'
```

#### 3. 验证安装
安装完成后可以访问：
- **后台管理**：`https://mp.hwkj01.xin/admin` (admin/admin)
- **API 测试**：`https://mp.hwkj01.xin/api_test.html`
- **安装界面**：`https://mp.hwkj01.xin/install.php`（首次安装）
- **小程序测试**：在开发工具中测试注册登录功能

### ⚠️ 安装注意事项

1. **环境要求**：
   - PHP 8.1+
   - MySQL 5.7+
   - 必要的 PHP 扩展（PDO、MBString、OpenSSL、CURL、GD）

2. **权限设置**：
   - `runtime/` 目录需要 777 权限
   - `public/storage/` 目录需要 755 权限

3. **安全提醒**：
   - 安装完成后请立即修改默认密码
   - 建议删除或重命名 `install.php` 文件
   - 生产环境请关闭调试模式

4. **常见问题**：
   - 如遇到表不存在错误，请使用 `install.php` 重新安装
   - 如遇到权限问题，请检查目录权限设置
   - 如遇到密码错误，默认密码是 `admin`
   - 如遇到安装失败，系统会自动回滚并提供重试选项
   - 安装完成后建议删除或重命名 `install.php` 文件

#### 3. 在 HBuilderX 中运行
- 打开 HBuilderX
- 导入项目
- 运行到微信开发者工具

## � 技术文档

### 前后端状态管理文档
详细的Token认证机制、数据同步策略和状态管理实现请参考：
📄 **[前后端状态管理文档.md](./前后端状态管理文档.md)**

该文档包含：
- Token认证机制详解
- 前端状态管理架构
- 数据同步策略和时机
- API接口设计规范
- 后端管理功能说明
- 安全考虑和错误处理
- 性能优化建议
- 故障排查指南

## �📡 API 接口文档

### 用户相关接口
- `POST /api/user/register` - 用户注册
- `POST /api/user/login` - 用户登录（返回数据库Token）
- `POST /api/user/wechat-login` - 微信基础登录
- `POST /api/user/wechat-phone-login` - 微信授权手机号登录
- `GET /api/user/read` - 获取用户信息（需要Token认证）
- `POST /api/user/save` - 更新用户信息（需要Token认证）
- `POST /api/user/change-password` - 修改密码
- `POST /api/user/refresh-token` - 刷新Token

### 名片相关接口
- `GET /api/card/info/{user_id}` - 获取名片信息
- `POST /api/card/share/{user_id}` - 分享名片
- `POST /api/card/collect/{user_id}` - 收藏名片
- `GET /api/card/stats` - 获取统计数据
- `GET /api/card/visitors` - 获取访客记录
- `GET /api/card/ai-analysis` - AI数据分析

### 会员相关接口
- `GET /api/premium/plans` - 获取套餐列表
- `GET /api/premium/status` - 获取会员状态
- `POST /api/premium/create-order` - 创建订单
- `POST /api/premium/verify-code` - 验证激活码
- `POST /api/premium/activate` - 激活会员
- `GET /api/premium/features` - 获取功能列表
- `GET /api/premium/orders` - 获取订单列表

### 文件上传接口
- `POST /api/upload/avatar` - 上传头像
- `POST /api/upload/image` - 上传图片
- `POST /api/upload/batch-images` - 批量上传图片

### 测试接口
- `GET /api/test/index` - 基础测试
- `GET /api/test/hello` - Hello测试
- `GET /api/test/db` - 数据库测试

### 认证说明
需要身份验证的接口需要在请求头中包含 `Authorization` 字段：
```
Authorization: Bearer {token}
```

### 响应格式
所有API接口都返回统一的JSON格式：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

### API 测试
访问 `https://mp.hwkj01.xin/api_test.html` 进行在线 API 测试。

## �️ 安装脚本详解

### 安装脚本特性
电子名片系统提供了完善的一键安装脚本 (`install.php`)，具有以下特性：

#### 智能环境检测
- **PHP 版本检查**：确保 PHP 8.1+ 版本
- **扩展检查**：验证 PDO、MBString、OpenSSL、CURL、GD 等必要扩展
- **目录权限检查**：检查 runtime、storage 等目录的读写权限
- **数据库连接测试**：验证数据库连接参数的正确性

#### 可视化配置界面
- **步骤化安装流程**：环境检查 → 数据库配置 → 系统安装 → 完成
- **友好的表单界面**：直观的数据库配置表单
- **实时连接测试**：配置后立即测试数据库连接
- **自动数据库创建**：如果指定数据库不存在会自动创建

#### 完整的数据初始化
- **系统表安装**：ThinkAdmin 框架所需的所有系统表
- **业务表安装**：电子名片系统的业务数据表
- **默认数据插入**：管理员账号、会员套餐、测试数据
- **索引和约束**：自动创建数据库索引和外键约束

#### 错误处理和安全机制
- **安装锁定**：防止重复安装，保护已有数据
- **错误回滚**：安装失败时自动清理已创建的资源
- **详细日志**：显示每个安装步骤的详细信息
- **安全提醒**：安装完成后提供安全配置建议

### 安装后验证
安装完成后，系统会自动验证：
- 数据库表结构完整性
- 管理员账号可用性
- API 接口连通性
- 文件权限正确性

## �🗄️ 数据库结构

### 核心业务表
- `bc_users` - 用户表（用户基本信息、会员状态、Token认证、个性定制）
- `bc_premium_plans` - 会员套餐表（套餐配置、价格、功能）
- `bc_premium_orders` - 会员订单表（订单信息、支付状态）
- `bc_activation_codes` - 激活码表（激活码管理、使用状态）
- `bc_card_stats` - 名片统计表（浏览、分享、收藏数据）
- `bc_visitor_logs` - 访客记录表（访问日志、行为分析）
- `bc_upload_logs` - 文件上传日志表（头像、背景图片上传记录）

### 用户表关键字段
`bc_users` 表包含以下关键字段：

**Token认证字段**：
- `access_token` - 访问令牌（64位随机字符串）
- `token_expires_at` - 令牌过期时间（7天有效期）
- `last_login_at` - 最后登录时间
- `last_login_ip` - 最后登录IP地址

**个性定制字段**：
- `custom_style` - 个性定制样式（JSON格式存储）
- `avatar` - 用户头像URL
- `background_image` - 背景图片URL（存储在custom_style中）

**会员相关字段**：
- `is_member` - 是否为会员
- `member_level` - 会员等级（1=专业版，2=企业版）
- `member_expire_time` - 会员到期时间

### ThinkAdmin 系统表
- `system_user` - 管理员用户表
- `system_auth` - 权限管理表
- `system_menu` - 菜单管理表
- `system_config` - 系统配置表

### 数据库特性
- **字符集**: utf8mb4 (支持 emoji 和特殊字符)
- **引擎**: InnoDB (支持事务和外键)
- **索引优化**: 关键字段建立索引，提升查询性能
- **软删除**: 重要数据支持软删除，保证数据安全

## 🖥️ 后台管理系统

### 访问方式
- **地址**: `https://mp.hwkj01.xin/admin`
- **默认账号**: `admin`
- **默认密码**: `admin`

### 主要功能模块

#### 1. 电子名片管理
- **用户管理**: 用户列表、状态管理、会员信息、个性定制查看
- **文件管理**: 上传记录、文件统计、存储管理
- **订单管理**: 会员订单、支付状态、退款处理
- **激活码管理**: 批量生成、使用状态、有效期管理
- **数据统计**: 用户增长、收入分析、访问统计、会员转化率
- **系统配置**: 会员套餐、功能开关、系统设置

#### 2. 系统管理
- **用户管理**: 管理员账号、权限分配
- **菜单管理**: 后台菜单配置、权限控制
- **权限管理**: 角色权限、功能权限
- **系统配置**: 基础配置、参数设置

#### 3. 数据分析
- **实时统计**: 用户数量、订单金额、访问量
- **趋势分析**: 用户增长趋势、收入趋势
- **行为分析**: 用户行为、功能使用情况

### 菜单配置
登录后台后，需要在 **系统管理 → 菜单管理** 中添加电子名片业务菜单：

1. **添加主菜单**：
   - 菜单名称：`电子名片管理`
   - 菜单图标：`layui-icon-user`
   - 排序：`100`

2. **添加子菜单**：
   - 用户管理：`admin/business_user/index`
   - 数据统计：`admin/business_stats/index`
   - 微信配置管理：`admin/wechat_config/index`
   - 系统测试：`admin/test/index`

> **注意**：如果电子名片管理菜单页面出现"E404 - 服务器繁忙"错误，请确认以下几点：
> 1. 控制器文件是否存在并正确命名
> 2. 视图文件是否位于正确的目录（例如：`app/admin/view/business_user/index.html`）
> 3. 菜单节点路径是否与控制器和方法名匹配
> 4. 在修改后清除缓存：删除 `server/public/runtime/cache/*` 目录下的文件
ps：ThinkAdmin框架对目录结构和命名规范有严格要求，只有当所有组件都按照规范配置好并且缓存得到更新后，菜单页面才能正常显示。
## 💎 会员系统详解

### 专业版与企业版区别

| 功能特性         | 基础版  | 专业版  | 企业版  |
|----------------|--------|--------|--------|
| 基础名片展示      | ✓     | ✓      | ✓      |
| 名片分享         | 有限制  | 无限制  | 无限制  |
| 高级模板         | ✗      | ✓      | ✓      |
| AI数据分析       | ✗      | ✓      | ✓      |
| 团队协作         | ✗      | ✗      | ✓      |
| 企业API集成      | ✗      | ✗      | ✓      |
| 专属客服         | ✗      | ✗      | ✓      |
| 价格            | 免费    | 588元/年 | 1288元/年 |

### 会员开通流程
1. 用户进入会员专区(premium.vue)
2. 选择合适的会员套餐(专业版/企业版)
3. 选择支付方式(在线支付/激活码)
4. 完成支付或输入激活码
5. 系统自动开通会员权限
6. 跳转回名片页面，显示会员标识

### 会员特权解锁机制
- **模板解锁**：会员开通后自动解锁全部高级模板
- **功能解锁**：系统根据会员等级自动解锁相应功能
- **权限控制**：后端API接口会验证会员权限，确保安全

## 功能说明

### 名片分享
- 支持原生一键转发分享功能
- 以名片页面截图作为分享封面
- 支持分享到微信好友和朋友圈
- 会员用户无分享次数限制

### 数据存储和同步
- **双向同步**：用户信息存储在数据库并实时同步到本地
- **离线支持**：支持离线编辑，联网后自动同步
- **冲突处理**：服务器数据优先，本地样式设置保留
- **Token认证**：使用数据库Token系统，安全可靠
- **状态管理**：完整的前后端状态管理机制
- **数据安全**：重要数据采用加密存储和传输

### 会员功能展示
- 专业版会员页面：精美设计，突出核心功能价值
- 企业版特权：强调团队协作和企业级功能
- 真实用户案例：展示会员使用后的实际效果
- 限时优惠：提供折扣以提高转化率

### 简洁设计
- 移除底部 tabBar，专注于名片展示
- 通过顶部导航按钮访问工作台功能
- 优化的单页面应用体验
- 清晰统一的UI设计风格
- 会员专区采用高端设计风格

## 自定义配置

### 主题色彩
在 `App.vue` 中修改全局样式变量：
```css
/* 主色调 */
.btn-primary {
    background-color: #4A90E2; /* 修改为你的主色调 */
}
```

### 后端配置
修改 `server/.env` 文件以配置：
- 数据库连接参数
- 应用调试模式
- 默认时区设置
- 会员功能开关

### ThinkAdmin模板配置
- **模板继承**：后台页面继承自 `table.html` 或 `main.html`
- **控制器命名**：控制器名与URL路径一致，如 `business_user` 对应 `BusinessUser`
- **视图路径**：视图文件放在对应控制器名的目录下，如 `business_user/index.html`
- **命名规范**：目录名使用下划线分隔，类名使用驼峰命名法

### 常见问题解决
- **404错误**：检查控制器和视图文件是否存在，URL路径是否正确
- **菜单不显示**：检查菜单配置和权限设置
- **模板错误**：清除缓存后重试
- **数据库错误**：检查数据库连接参数和表结构

## 注意事项

1. **图片资源**：项目中的图片路径为占位符，实际使用时需要替换为真实的图片文件
2. **API接口**：确保前端服务层中的API地址与后端部署地址一致
3. **权限配置**：根据平台要求配置相应的权限（如相机、相册访问权限）
4. **分享功能**：需要在各平台配置分享参数和回调地址
5. **数据库备份**：定期备份数据库以防数据丢失
6. **支付配置**：正式环境需配置真实的支付接口参数
7. **微信配置**：确保 manifest.json 中的 AppID 与后端数据库中的微信配置一致
8. **代码维护**：已清理所有调试和测试代码，保持代码库整洁
9. **数据同步**：登录用户的联系信息会自动同步到服务器，样式设置仅保存本地
10. **Token管理**：管理员可在后台查看和管理用户Token状态

## 扩展功能

可以考虑添加的功能：
- 在线支付功能扩展（支付宝、银联等）
- 消息推送（会员到期提醒、活动通知）
- 数据统计分析扩展（更多维度分析）
- 多语言支持（支持英文、日文等）
- 主题切换（深色模式）
- 微信/支付宝等第三方登录
- 企业版定制开发服务

## 🚀 部署指南

### 生产环境部署

#### 1. 服务器要求
- **操作系统**: Linux (推荐 CentOS 7+ 或 Ubuntu 18+)
- **Web服务器**: Nginx 1.18+ 或 Apache 2.4+
- **PHP**: 8.1+ (推荐使用 PHP-FPM)
- **数据库**: MySQL 5.7+ 或 MariaDB 10.3+
- **内存**: 最低 2GB，推荐 4GB+
- **存储**: 最低 20GB，推荐 SSD

#### 2. 域名和 SSL
```bash
# 配置域名解析
# 申请 SSL 证书（推荐 Let's Encrypt）
certbot --nginx -d your-domain.com
```

#### 3. 性能优化
```bash
# 开启 OPcache
echo "opcache.enable=1" >> /etc/php/8.1/fpm/php.ini

# 配置 Nginx 缓存
# 在 nginx.conf 中添加静态文件缓存规则
```

#### 4. 安全配置
- 定期更新系统和软件包
- 配置防火墙规则
- 设置数据库访问权限
- 启用 HTTPS 强制跳转

### 监控和维护

#### 1. 日志监控
```bash
# 查看应用日志
tail -f server/runtime/log/error.log

# 查看访问日志
tail -f /var/log/nginx/access.log
```

#### 2. 数据备份
```bash
# 数据库备份脚本
mysqldump -u username -p database_name > backup_$(date +%Y%m%d).sql

# 文件备份
tar -czf files_backup_$(date +%Y%m%d).tar.gz server/public/storage/
```

#### 3. 性能监控
- 使用 New Relic 或 Datadog 监控应用性能
- 配置 MySQL 慢查询日志
- 监控服务器资源使用情况

## 📈 开发计划

### 已完成功能 ✅
- [x] ThinkAdmin 后台管理系统
- [x] 完整的 RESTful API 接口
- [x] 用户注册登录系统
- [x] 微信一键登录（已完成配置和调试）
- [x] 个人名片展示页面
- [x] 名片编辑页面（完整的DIY样式定制）
- [x] 智能头像上传（微信头像 + 相册选择）
- [x] 背景图片上传（自定义背景图片）
- [x] 个性定制系统（JSON数据存储）
- [x] 跨设备数据同步（优先服务器数据）
- [x] 会员系统(专业版/企业版)
- [x] 会员状态自动同步
- [x] 激活码兑换功能
- [x] 名片分享统计
- [x] 七牛云文件存储
- [x] 文件上传日志管理
- [x] 数据库Token认证系统
- [x] 前后端数据同步机制
- [x] 用户状态管理和本地缓存
- [x] 后台Token管理功能
- [x] 数据库设计和优化
- [x] API 测试页面
- [x] 一键安装系统（install.php）
- [x] 智能环境检测和配置
- [x] 安装错误处理和回滚机制
- [x] 代码清理和优化（移除调试代码）

### 开发中功能 🚧
- [ ] 微信支付集成
- [ ] 高级模板系统
- [ ] AI 数据分析功能
- [ ] 团队协作功能
- [ ] 访客记录详情
- [ ] 更多个性定制选项（字体、动效等）

### 计划功能 📋
- [ ] 小程序码生成
- [ ] 多语言支持
- [ ] 数据导出功能
- [ ] API 开放平台
- [ ] 移动端 H5 版本

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进项目。

### 开发规范
- 遵循 PSR-4 自动加载规范
- 使用 ThinkPHP 8.0 开发规范
- API 接口遵循 RESTful 设计原则
- 代码注释使用中文，便于维护

### 提交规范
- feat: 新功能
- fix: 修复问题
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构

## 📄 许可证

MIT License

## 📞 技术支持

- **项目地址**: [GitHub Repository]
- **问题反馈**: [GitHub Issues]
- **系统安装**: `https://mp.hwkj01.xin/install.php`
- **API 测试**: `https://mp.hwkj01.xin/api_test.html`
- **后台管理**: `https://mp.hwkj01.xin/admin` (admin/admin)

## 📝 更新日志

### v2.1.0 (2025-01-27)
**🎨 个性定制系统重大升级**
- ✨ 新增智能头像上传功能（微信头像一键选择 + 相册上传）
- ✨ 新增背景图片上传功能（支持自定义背景图片）
- ✨ 完善个性定制系统（JSON数据存储，支持更多样式选项）
- ✨ 实现跨设备数据同步（换设备登录自动同步个性设置）
- ✨ 新增会员状态自动同步（应用启动、页面切换时自动更新）
- 🔧 优化文件上传机制（时间戳命名，避免文件冲突）
- 🔧 改进数据加载优先级（优先服务器数据，本地缓存备用）
- 🔧 增强错误处理和容错机制
- 🧹 清理调试代码，优化性能

### v2.0.0 (2025-01-20)
**🚀 系统架构全面升级**
- ✨ 完整的ThinkAdmin后台管理系统
- ✨ RESTful API接口体系
- ✨ 数据库Token认证机制
- ✨ 一键安装系统
- ✨ 会员系统和激活码功能
- 🔧 前后端数据同步机制
- 🔧 用户状态管理优化

### v1.0.0 (2024-12-15)
**🎉 项目初始版本**
- ✨ 基础名片展示功能
- ✨ 用户注册登录
- ✨ 微信一键登录
- ✨ 基础个性定制

## 联系方式

如有问题或建议，请通过 GitHub Issues 联系开发者。

---

**最后更新**: 2025年1月27日
**当前版本**: v2.1.0
**主要特性**: 智能头像上传、背景图片定制、跨设备数据同步、会员状态自动同步
