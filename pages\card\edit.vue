<template>
  <view class="edit-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="navbarStyle">
      <view class="navbar-left">
        <view class="back-btn" @click="goBack">
          <text class="back-icon">←</text>
          <text class="back-text">返回</text>
        </view>
      </view>
      <view class="navbar-title">编辑名片</view>
    </view>
    
    <!-- 顶部区域（包含名片预览和选项卡） -->
    <view class="top-section" :style="{ paddingTop: (statusBarHeight + 45) + 'px' }">
      <!-- 名片预览 -->
      <view class="card-preview">
        <view 
          class="classic-card-v2" 
          :class="{'classic-card-v2-with-bg-image': form.customStyle.backgroundType === 'image' && form.customStyle.backgroundImage}"
          :style="Object.assign(cardPreviewMargin, cardPreviewStyle)"
        >
          <!-- 当使用背景图片时，添加一个覆盖层提高文本可读性 -->
          <view 
            v-if="form.customStyle.backgroundType === 'image' && form.customStyle.backgroundImage" 
            class="classic-card-v2-overlay"
          ></view>
          
          <!-- 背景图片使用独立image组件 -->
          <image 
            v-if="form.customStyle.backgroundType === 'image' && form.customStyle.backgroundImage" 
            :src="form.customStyle.backgroundImage" 
            class="classic-card-v2-bg-image" 
            mode="aspectFill"
          ></image>
          
          <view class="classic-card-v2-content">
            <view class="classic-card-v2-header">
              <view class="classic-card-v2-info">
                <view class="classic-card-v2-name-row">
                  <text class="classic-card-v2-name" :style="{color: form.customStyle.textColor}">{{ form.name }}</text>
                  <text class="classic-card-v2-position" :style="positionStyle">{{ form.position }}</text>
                </view>
                <text class="classic-card-v2-company" :style="{color: form.customStyle.textColor}">{{ form.company }}</text>
              </view>
              <view class="classic-card-v2-avatar-box">
                <image class="classic-card-v2-avatar" :src="getDisplayAvatar(form.avatar)" mode="aspectFill" />
                <image class="classic-card-v2-cert" src="/static/icons/rz.png" />
              </view>
            </view>
            <view class="classic-card-v2-divider" :style="{background: form.customStyle.textColor === '#ffffff' ? 'rgba(255,255,255,0.2)' : '#f0f0f0'}"></view>
            <view class="classic-card-v2-contact-list">
              <view class="classic-card-v2-contact-item">
                <image class="classic-card-v2-icon" src="/static/icons/phone.png" mode="aspectFit" :style="iconStyle" />
                <text class="classic-card-v2-label" :style="{color: form.customStyle.textColor === '#ffffff' ? 'rgba(255,255,255,0.7)' : '#888'}">电话：</text>
                <text class="classic-card-v2-value" :style="{color: form.customStyle.textColor}">{{ form.phone }}</text>
              </view>
              <view class="classic-card-v2-contact-item">
                <image class="classic-card-v2-icon" src="/static/icons/chat.png" mode="aspectFit" :style="iconStyle" />
                <text class="classic-card-v2-label" :style="{color: form.customStyle.textColor === '#ffffff' ? 'rgba(255,255,255,0.7)' : '#888'}">微信：</text>
                <text class="classic-card-v2-value" :style="{color: form.customStyle.textColor}">{{ form.wechat }}</text>
              </view>
              <view class="classic-card-v2-contact-item">
                <image class="classic-card-v2-icon" src="/static/icons/email.png" mode="aspectFit" :style="iconStyle" />
                <text class="classic-card-v2-label" :style="{color: form.customStyle.textColor === '#ffffff' ? 'rgba(255,255,255,0.7)' : '#888'}">邮箱：</text>
                <text class="classic-card-v2-value" :style="{color: form.customStyle.textColor}">{{ form.email }}</text>
              </view>
              <view class="classic-card-v2-contact-item">
                <image class="classic-card-v2-icon" src="/static/icons/location.png" mode="aspectFit" :style="iconStyle" />
                <text class="classic-card-v2-label" :style="{color: form.customStyle.textColor === '#ffffff' ? 'rgba(255,255,255,0.7)' : '#888'}">地址：</text>
                <text class="classic-card-v2-value" :style="{color: form.customStyle.textColor}">{{ form.address }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 固定在名片预览下方的选项卡 -->
      <view class="fixed-tab-bar" :style="{ padding: `0 ${adaptiveSpacing.sm}px` }">
        <view class="tab-bar">
          <view :class="['tab-item', {active: activeTab === 0}]" @click="activeTab = 0">联系信息</view>
          <view :class="['tab-item', {active: activeTab === 1}]" @click="activeTab = 1">个性定制</view>
        </view>
      </view>
    </view>

    <!-- 内容区域 - 使用计算样式设置上边距 -->
    <view class="content-area" :style="contentStyle">
      <!-- 移除会员提示条 -->

      <view v-show="activeTab === 0" class="form-section" :style="{ margin: `${adaptiveSpacing.xs}px ${adaptiveSpacing.sm}px ${adaptiveSpacing.md}px` }">
        <view class="section-header">
          <text class="section-title">联系信息</text>
          <view class="section-line"></view>
        </view>
        
        <!-- 头像选择移到最上方 -->
        <view class="form-item avatar-item">
          <text class="form-label">头像</text>
          <view class="avatar-selector">
            <image class="avatar-preview" :src="getDisplayAvatar(form.avatar)" mode="aspectFill"></image>
            <view class="avatar-buttons">
              <!-- #ifdef MP-WEIXIN -->
              <!-- 微信小程序中使用微信头像选择 -->
              <button class="avatar-change-btn" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
                <text class="btn-text">更换头像</text>
              </button>
              <!-- #endif -->

              <!-- #ifndef MP-WEIXIN -->
              <!-- 非微信环境使用ActionSheet -->
              <view class="avatar-change-btn" @click="showAvatarActionSheet">
                <text class="btn-text">更换头像</text>
              </view>
              <!-- #endif -->
            </view>
          </view>
        </view>
        
        <view class="form-group">
          <view class="form-item modern-form-item">
            <text class="form-label">姓名</text>
            <view class="input-container">
              <input class="form-input" v-model="form.name" placeholder="请输入姓名" />
            </view>
          </view>
          <view class="form-item modern-form-item">
            <text class="form-label">职位</text>
            <view class="input-container">
              <input class="form-input" v-model="form.position" placeholder="请输入职位" />
            </view>
          </view>
          <view class="form-item modern-form-item">
            <text class="form-label">公司</text>
            <view class="input-container">
              <input class="form-input" v-model="form.company" placeholder="请输入公司" />
            </view>
          </view>
        </view>
        
        <view class="form-divider"></view>
        
        <view class="form-group">
          <view class="form-item modern-form-item">
            <text class="form-label">电话</text>
            <view class="input-container">
              <image class="input-icon" src="/static/icons/phone.png" mode="aspectFit" />
              <input
                class="form-input with-icon"
                v-model="form.phone"
                placeholder="请输入11位手机号"
                type="number"
                maxlength="11"
                @input="onPhoneInput"
              />
              <view v-if="form.phone" class="clear-btn" @click="clearPhone">
                <text class="clear-icon">×</text>
              </view>
            </view>
            <text v-if="phoneError" class="error-tip">{{ phoneError }}</text>
          </view>
          <view class="form-item modern-form-item">
            <text class="form-label">微信</text>
            <view class="input-container">
              <image class="input-icon" src="/static/icons/chat.png" mode="aspectFit" />
              <input class="form-input with-icon" v-model="form.wechat" placeholder="请输入微信号" />
              <view v-if="form.wechat" class="clear-btn" @click="clearWechat">
                <text class="clear-icon">×</text>
              </view>
            </view>
          </view>
          <view class="form-item modern-form-item">
            <text class="form-label">邮箱</text>
            <view class="input-container">
              <image class="input-icon" src="/static/icons/email.png" mode="aspectFit" />
              <input
                class="form-input with-icon"
                v-model="form.email"
                placeholder="请输入邮箱地址"
                type="email"
                @blur="validateEmail"
              />
              <view v-if="form.email" class="clear-btn" @click="clearEmail">
                <text class="clear-icon">×</text>
              </view>
            </view>
            <text v-if="emailError" class="error-tip">{{ emailError }}</text>
          </view>
          
          <!-- 优化后的地址设置UI -->
          <view class="form-item modern-form-item address-item">
            <text class="form-label">地址</text>
            <view class="input-container">
              <image class="input-icon" src="/static/icons/location.png" mode="aspectFit" />
              <view class="address-preview" @click="showAddressSelector">
                <text class="address-text">{{ form.address || '请选择地址' }}</text>
                <text class="address-icon">></text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <view v-show="activeTab === 1" class="diy-container" :style="{ padding: `${adaptiveSpacing.xs}px ${adaptiveSpacing.sm}px` }">
        <!-- 名片风格 -->
        <view class="diy-card">
          <view class="diy-card-header">
            <text class="diy-card-title">名片风格</text>
          </view>
          <view class="diy-card-content">
            <scroll-view class="style-scroll-view" scroll-x="true" show-scrollbar="false">
              <view class="style-scroll-content">
                <view 
                  v-for="(item, idx) in cardStyles" 
                  :key="item.name" 
                  :class="['style-option-item', {active: form.customStyle.cardStyleIndex === idx}]" 
                  @click="applyCardStyle(idx)"
                >
                  <view class="style-option-preview" :style="item.previewStyle">
                    <view class="style-preview-content">
                      <view class="style-preview-avatar"></view>
                      <view class="style-preview-info">
                        <view class="style-preview-name"></view>
                        <view class="style-preview-position"></view>
                      </view>
                    </view>
                  </view>
                  <text class="style-option-label">{{ item.name }}</text>
                </view>
              </view>
            </scroll-view>
          </view>
        </view>

        <!-- 背景 -->
        <view class="diy-card">
          <view class="diy-card-header">
            <text class="diy-card-title">背景</text>
          </view>
          <view class="diy-card-content">
            <view class="diy-option-group">
              <text class="diy-option-group-title">纯色</text>
              <view class="diy-option-row">
                <view 
                  v-for="(color, idx) in bgColors" 
                  :key="color" 
                  :style="{background: color}" 
                  :class="['diy-color-block', {active: form.customStyle.backgroundType === 'color' && form.customStyle.backgroundColor === color}]" 
                  @click="applyBgColor(color)"
                ></view>
              </view>
            </view>
            
            <view class="diy-option-group">
              <text class="diy-option-group-title">渐变</text>
              <view class="diy-option-row">
                <view 
                  v-for="(gradient, idx) in gradients" 
                  :key="idx" 
                  :style="{background: gradient}" 
                  :class="['diy-color-block', {active: form.customStyle.backgroundType === 'gradient' && form.customStyle.gradientIndex === idx}]" 
                  @click="applyGradient(idx)"
                ></view>
              </view>
            </view>
            
            <view class="diy-option-group">
              <text class="diy-option-group-title">图片</text>
              <view class="diy-option-row">
                <view 
                  class="diy-upload-btn" 
                  @click="uploadBgImage"
                >
                  <text class="diy-upload-icon">+</text>
                  <text class="diy-upload-text">上传</text>
                </view>
                <view 
                  v-for="(img, idx) in bgImages" 
                  :key="img" 
                  :class="['diy-image-block', {active: form.customStyle.backgroundType === 'image' && form.customStyle.backgroundImage === img}]" 
                  @click="applyBgImage(img)"
                >
                  <image :src="img" mode="aspectFill" class="diy-image-preview"></image>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 字体颜色 -->
        <view class="diy-card">
          <view class="diy-card-header">
            <text class="diy-card-title">字体颜色</text>
          </view>
          <view class="diy-card-content">
            <view class="diy-option-row">
              <view 
                v-for="(color, idx) in textColors" 
                :key="color" 
                :style="{background: color}" 
                :class="['diy-color-block', {active: form.customStyle.textColor === color}]" 
                @click="applyTextColor(color)"
              >
                <view v-if="color === '#ffffff'" class="diy-color-block-border"></view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 添加底部安全区域，防止保存按钮遮挡内容 -->
      <view class="bottom-safe-area"></view>
    </view>
    
    <!-- 悬浮保存按钮 -->
    <button class="save-btn-float" :class="{show: showSaveBtn}" @click="saveCard">
      {{ activeTab === 0 ? '保存联系信息' : '保存个性定制' }}
    </button>



    <!-- 地址选择弹窗 -->
    <view class="address-modal" v-if="showAddressModal">
      <view class="address-modal-mask" @click="showAddressModal = false"></view>
      <view class="address-modal-content">
        <view class="address-modal-header">
          <text class="address-modal-title">选择地址</text>
          <text class="address-modal-close" @click="showAddressModal = false">×</text>
        </view>

        <view class="address-tabs">
          <view :class="['address-tab', {active: addressTab === 0}]" @click="addressTab = 0">手动输入</view>
          <view :class="['address-tab', {active: addressTab === 1}]" @click="addressTab = 1">地图选点</view>
        </view>

        <view class="address-content">
          <!-- 手动输入 -->
          <view v-if="addressTab === 0" class="address-manual">
            <view class="address-picker-group">
              <picker mode="region" @change="onRegionChange" :value="regionValue">
                <view class="region-picker">
                  <text>{{ regionText || '选择省/市/区' }}</text>
                  <text class="picker-arrow">></text>
                </view>
              </picker>
            </view>
            <input class="address-detail-input" v-model="addressDetail" placeholder="详细地址，如街道、门牌号等" />
          </view>

          <!-- 地图选点 -->
          <view v-if="addressTab === 1" class="map-container">
            <view class="map-placeholder">
              <image class="map-image" src="/static/icons/location.png" mode="aspectFit" />
              <text class="map-tip">点击获取当前位置</text>
            </view>
            <button class="map-btn" @click="getLocation">获取当前位置</button>
          </view>
        </view>

        <view class="address-modal-footer">
          <button class="address-cancel-btn" @click="showAddressModal = false">取消</button>
          <button class="address-confirm-btn" @click="confirmAddress">确认</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import userService from '../../services/userService.js';
import userInfoService from '../../services/userInfoService.js';
import { getData } from '../../utils/storage.js';
import { TOKEN_KEY, USER_INFO_KEY, API_BASE_URL } from '../../utils/config.js';

export default {
  data() {
    return {
      statusBarHeight: 44,
      activeTab: 0,
      showSaveBtn: true,
      lastScrollTop: 0,


      showAddressModal: false,
      addressTab: 0,
      regionValue: ['', '', ''],
      regionText: '',
      addressDetail: '',
      topSectionHeight: 0, // 将默认值设为0，避免初始大间距
      contentPaddingTop: 0, // 添加新属性用于设置内容区域padding-top
      screenWidth: 375,
      screenHeight: 667,
      deviceRatio: 1,
      isPremiumMember: false, // 添加会员状态标志
      phoneError: '', // 电话号码错误提示
      emailError: '', // 邮箱错误提示
      form: {
        avatar: '/static/default-avatar.png',
        name: '徐敏',
        position: '销售专员',
        company: '德萨大数据股份有限公司',
        phone: '18596320120',
        email: 'www.1099458780.com',
        wechat: 'xumin_wx',
        address: '山东省庄市滕州市北辛西路2000号',
        customStyle: {
          backgroundType: 'color',
          gradientIndex: 0,
          backgroundColor: '#fff',
          backgroundImage: '',
          textColor: '#222',
          borderRadius: 'small',
          borderRadiusIndex: 1,
          cardStyleIndex: 0
        }
      },
      cardStyles: [
        { 
          name: '经典商务', 
          previewStyle: 'background: #ffffff; border: 2px solid #4a6ef2; box-shadow: 0 4px 10px rgba(0,0,0,0.15);'
        },
        { 
          name: '科技蓝', 
          previewStyle: 'background: linear-gradient(90deg, #4a6ef2 0%, #6fc3ff 100%);'
        },
        { 
          name: '简约灰', 
          previewStyle: 'background: #f5f7fa; border: 1px solid #eee;'
        },
        { 
          name: '暗夜黑', 
          previewStyle: 'background: #333333; color: #ffffff;'
        },
        { 
          name: '活力橙', 
          previewStyle: 'background: linear-gradient(90deg, #f2a54a 0%, #ffd36f 100%);'
        },
        { 
          name: '森林绿', 
          previewStyle: 'background: linear-gradient(90deg, #2c9d6d 0%, #6fd39f 100%);'
        },
        { 
          name: '浪漫粉', 
          previewStyle: 'background: linear-gradient(90deg, #f25a8e 0%, #ff9cc0 100%);'
        },
        { 
          name: '高端黑金', 
          previewStyle: 'background: linear-gradient(90deg, #333333 0%, #666666 100%); border: 1px solid #d4af37;'
        },
        { 
          name: '紫色梦幻', 
          previewStyle: 'background: linear-gradient(90deg, #9d2c8d 0%, #d36fc6 100%);'
        },
        { 
          name: '复古棕', 
          previewStyle: 'background: #8b5a2b; color: #f5deb3;'
        },
        { 
          name: '海洋蓝', 
          previewStyle: 'background: linear-gradient(135deg, #1a2980 0%, #26d0ce 100%);'
        },
        { 
          name: '珊瑚红', 
          previewStyle: 'background: #FF6F61; color: #ffffff;'
        },
        { 
          name: '薄荷绿', 
          previewStyle: 'background: #98ff98; color: #006400; border: 1px solid #7ccc7c;'
        },
        { 
          name: '太空灰', 
          previewStyle: 'background: linear-gradient(135deg, #232526 0%, #414345 100%); color: #ffffff;'
        },
        { 
          name: '日落橙', 
          previewStyle: 'background: linear-gradient(135deg, #ff512f 0%, #f09819 100%); color: #ffffff;'
        },
        { 
          name: '翡翠绿', 
          previewStyle: 'background: linear-gradient(135deg, #43c6ac 0%, #191654 100%); color: #ffffff;'
        },
        { 
          name: '商务蓝', 
          previewStyle: 'background: #1e3c72; color: #ffffff; border: 2px solid #2a5298;'
        },
        { 
          name: '玫瑰金', 
          previewStyle: 'background: linear-gradient(135deg, #e6b980 0%, #eacda3 100%); color: #8b4513;'
        },
        { 
          name: '星空紫', 
          previewStyle: 'background: linear-gradient(135deg, #5f2c82 0%, #49a09d 100%); color: #ffffff;'
        }
      ],
      bgColors: [
        '#ffffff', // 纯白
        '#f5f7fa', // 浅灰
        '#e6f0ff', // 浅蓝
        '#f0f9eb', // 浅绿
        '#fef0f0', // 浅红
        '#fdf6ec', // 浅黄
        '#f0f2f5', // 银灰
        '#2c5aa0', // 深蓝
        '#333333', // 深灰
        '#67c23a', // 绿色
        '#1e3c72', // 商务蓝
        '#8b5a2b', // 复古棕
        '#FF6F61', // 珊瑚红
        '#98ff98', // 薄荷绿
        '#5f2c82', // 深紫
        '#b8860b', // 暗金色
        '#20b2aa', // 浅绿松石
        '#4b0082', // 靛青
        '#800000', // 栗色
        '#008080'  // 蓝绿色
      ],
      gradients: [
        'linear-gradient(90deg, #4a6ef2 0%, #6fc3ff 100%)', // 蓝色渐变
        'linear-gradient(90deg, #f2a54a 0%, #ffd36f 100%)', // 橙色渐变
        'linear-gradient(90deg, #2c9d6d 0%, #6fd39f 100%)', // 绿色渐变
        'linear-gradient(90deg, #f25a8e 0%, #ff9cc0 100%)', // 粉色渐变
        'linear-gradient(90deg, #333333 0%, #666666 100%)', // 灰色渐变
        'linear-gradient(90deg, #9d2c8d 0%, #d36fc6 100%)', // 紫色渐变
        'linear-gradient(135deg, #1a2980 0%, #26d0ce 100%)', // 海洋蓝渐变
        'linear-gradient(135deg, #ff512f 0%, #f09819 100%)', // 日落橙渐变
        'linear-gradient(135deg, #43c6ac 0%, #191654 100%)', // 翡翠绿渐变
        'linear-gradient(135deg, #e6b980 0%, #eacda3 100%)', // 玫瑰金渐变
        'linear-gradient(135deg, #5f2c82 0%, #49a09d 100%)', // 星空紫渐变
        'linear-gradient(135deg, #232526 0%, #414345 100%)', // 太空灰渐变
        'linear-gradient(135deg, #2980b9 0%, #6dd5fa 100%)', // 清新天蓝
        'linear-gradient(135deg, #8e2de2 0%, #4a00e0 100%)', // 皇家紫
        'linear-gradient(135deg, #f953c6 0%, #b91d73 100%)', // 热情玫红
        'linear-gradient(135deg, #00b09b 0%, #96c93d 100%)', // 春日绿
        'linear-gradient(135deg, #c31432 0%, #240b36 100%)', // 深红暗紫
        'linear-gradient(135deg, #3a1c71 0%, #d76d77 50%, #ffaf7b 100%)', // 三色渐变
        'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', // 电光蓝
        'linear-gradient(135deg, #7F7FD5 0%, #86A8E7 50%, #91EAE4 100%)' // 薰衣草蓝
      ],
      bgImages: ['/static/template/1.jpg', '/static/template/2.png'],
      textColors: [
        '#222222', // 黑色
        '#ffffff', // 白色
        '#4a6ef2', // 蓝色
        '#f2a54a', // 橙色
        '#2c9d6d', // 绿色
        '#f25a8e', // 粉色
        '#9d2c8d', // 紫色
        '#d4af37', // 金色
        '#1e3c72', // 商务蓝
        '#8b5a2b', // 复古棕
        '#FF6F61', // 珊瑚红
        '#006400', // 深绿色
        '#800000', // 栗色
        '#4b0082', // 靛青
        '#f5deb3', // 小麦色
        '#00b09b', // 薄荷绿
        '#c31432', // 深红色
        '#3a1c71', // 深紫色
        '#4facfe', // 电光蓝
        '#7F7FD5', // 薰衣草蓝
        '#b91d73', // 玫红
        '#96c93d', // 嫩绿
        '#240b36', // 暗紫
        '#ffaf7b', // 杏橙
        '#00f2fe', // 亮青
        '#2E8B57', // 海绿色
        '#CD5C5C', // 印第安红
        '#191970', // 午夜蓝
        '#DAA520', // 金菊色
        '#20B2AA'  // 浅海绿
      ]
    }
  },
  computed: {
    navbarStyle() {
      return {
        height: `${this.statusBarHeight + 45}px`,
        paddingTop: `${this.statusBarHeight}px`,
        zIndex: 1001 // 确保导航栏在最上层
      }
    },
    // 计算实际背景样式
    computedBackgroundStyle() {
      const style = {};
      
      // 根据背景类型设置样式，确保只应用一种背景
      if (this.form.customStyle.backgroundType === 'solid' || this.form.customStyle.backgroundType === 'color') {
        // 纯色背景
        style.background = this.form.customStyle.backgroundColor || '#ffffff';
      } else if (this.form.customStyle.backgroundType === 'gradient') {
        try {
          // 渐变背景
          const gradientIndex = parseInt(this.form.customStyle.gradientIndex || 0);
          
          // 确保索引有效
          if (gradientIndex >= 0 && gradientIndex < this.gradients.length) {
            const gradient = this.gradients[gradientIndex];
            if (gradient) {
              style.background = gradient;
            } else {
              style.background = '#ffffff'; // 回退到默认背景
            }
          } else {
            style.background = '#ffffff'; // 回退到默认背景
          }
        } catch (error) {
          style.background = '#ffffff'; // 回退到默认背景
        }
      } else if (this.form.customStyle.backgroundType === 'image' && this.form.customStyle.backgroundImage) {
        // 图片背景 - 使用独立的image组件，这里不设置background-image
        // 为了避免冲突，我们只设置一个透明背景
        style.background = 'transparent';
      } else {
        // 默认背景
        style.background = '#ffffff';
      }
      
      return style;
    },
    iconStyle() {
      // 根据文字颜色调整图标颜色
      if (this.form.customStyle.textColor === '#ffffff') {
        // 白色文字时，图标也显示为白色
        return {
          filter: 'brightness(0) invert(1)',
          opacity: 0.9
        };
      } else {
        // 深色文字时，保持图标原色
        return {
          opacity: 0.8
        };
      }
    },
    cardPreviewStyle() {
      // 使用计算好的背景样式
      const style = {...this.computedBackgroundStyle};
      

      
      // 圆角
      style.borderRadius = this.form.customStyle.borderRadiusIndex === undefined ? '12rpx' : 
                          ['12rpx', '24rpx', '0'][this.form.customStyle.borderRadiusIndex];
      
      // 阴影
      if (this.form.customStyle.shadow) {
        style.boxShadow = '0 4rpx 16rpx rgba(0,0,0,0.1)';
      }
      
      return style;
    },
    cardTextStyle() {
      return {
        color: this.form.customStyle.textColor || '#222222',
        fontSize: this.fontSizes[this.form.customStyle.fontSizeIndex || 1].previewSize
      };
    },
    avatarPreviewStyle() {
      const style = {
        borderRadius: this.avatarStyles[this.form.customStyle.avatarStyleIndex || 0]
      };
      
      if (this.form.customStyle.avatarBorder) {
        style.border = '2rpx solid #ffffff';
      }
      
      return style;
    },
    adaptiveSpacing() {
      // 基于屏幕宽度计算基础间距单位，减小系数从25改为30
      const baseSpacing = this.screenWidth / 30;
      return {
        xs: baseSpacing * 0.4, // 超小间距（减小系数）
        sm: baseSpacing * 0.7, // 小间距（减小系数）
        md: baseSpacing * 0.9, // 中等间距（减小系数）
        lg: baseSpacing * 1.2, // 大间距（减小系数）
        xl: baseSpacing * 1.5 // 超大间距（减小系数）
      };
    },
    cardPreviewMargin() {
      const spacing = this.adaptiveSpacing;
      return {
        margin: `${spacing.md}px ${spacing.sm}px ${spacing.md}px ${spacing.sm}px`
      };
    },
    contentStyle() {
      // 如果有contentPaddingTop值（由updateContentPadding方法设置），则优先使用
      if (this.contentPaddingTop > 0) {
        return {
          paddingTop: `${this.contentPaddingTop}px`
        };
      }
      
      // 否则使用计算值
      const sectionHeight = this.topSectionHeight || 400;
      // 增加减少量，让内容更靠近选项卡
      const reductionRatio = this.screenHeight < 700 ? 0.25 : 0.2;
      const paddingReduction = sectionHeight * reductionRatio;
      
      return {
        paddingTop: `${this.statusBarHeight + 45 + sectionHeight - paddingReduction}px`
      };
    },
    // 计算职位样式
    positionStyle() {
      const textColor = this.form.customStyle.textColor;
      
      // 根据文字颜色和背景类型决定职位样式
      if (textColor === '#ffffff') {
        // 白色文字时，使用半透明白色背景和白色文字
        return {
          color: '#ffffff',
          background: 'rgba(255, 255, 255, 0.2)'
        };
      } else if (this.form.customStyle.backgroundType === 'gradient' || 
                this.form.customStyle.backgroundColor === '#333333' || 
                this.form.customStyle.backgroundColor === '#2c5aa0') {
        // 深色背景时，使用半透明白色背景和白色文字
        return {
          color: '#ffffff',
          background: 'rgba(255, 255, 255, 0.2)'
        };
      } else {
        // 浅色背景时，使用浅灰色背景和深色文字
        return {
          color: textColor,
          background: 'rgba(0, 0, 0, 0.05)'
        };
      }
    },
  },
  methods: {
    // 同步会员状态
    async syncMemberStatus() {
      try {
        // 静默同步会员状态
        await userService.syncMemberStatus();

        // 重新检查会员状态
        this.checkMemberStatus();
      } catch (error) {
        console.error('会员状态同步失败:', error);
      }
    },

    // 电话号码输入验证
    onPhoneInput(e) {
      let value = e.detail.value;

      // 只允许数字
      value = value.replace(/[^\d]/g, '');

      // 限制最大长度为11位
      if (value.length > 11) {
        value = value.slice(0, 11);
      }

      // 更新表单数据
      this.form.phone = value;

      // 验证手机号格式
      this.validatePhone(value);
    },

    // 验证手机号
    validatePhone(phone) {
      if (!phone) {
        this.phoneError = '';
        return;
      }

      if (phone.length < 11) {
        this.phoneError = '手机号必须为11位';
      } else if (!/^1[3-9]\d{9}$/.test(phone)) {
        this.phoneError = '请输入正确的手机号格式';
      } else {
        this.phoneError = '';
      }
    },

    // 验证邮箱格式
    validateEmail() {
      if (!this.form.email) {
        this.emailError = '';
        return;
      }

      if (!userInfoService.validateEmail(this.form.email)) {
        this.emailError = '请输入正确的邮箱格式';
      } else {
        this.emailError = '';
      }
    },

    // 清除输入框内容的方法
    clearPhone() {
      this.form.phone = '';
      this.phoneError = '';
    },
    clearWechat() {
      this.form.wechat = '';
    },
    clearEmail() {
      this.form.email = '';
      this.emailError = '';
    },

    // 从服务器加载用户信息
    async loadUserInfoFromServer() {
      try {
        // 检查是否已登录
        const isLoggedIn = userService.isLoggedIn();
        const token = getData(TOKEN_KEY);
        const currentUser = getData(USER_INFO_KEY);

        if (!isLoggedIn) {
          return;
        }

        uni.showLoading({
          title: '加载中...',
          mask: true
        });

        const result = await userInfoService.getCurrentUserInfo();

        if (result.success && result.data) {
          // 将服务器数据映射到表单
          const serverData = result.data;

          // 更新基本信息和自定义样式
          const updatedForm = {
            ...this.form,
            id: serverData.id,
            name: serverData.name || '',
            phone: serverData.phone || '',
            email: serverData.email || '',
            wechat: serverData.wechat || '',
            position: serverData.position || '',
            company: serverData.company || '',
            address: serverData.address || '',
            description: serverData.description || '',
            avatar: serverData.avatar || '/static/default-avatar.png',
            // 重要：同步自定义样式，包括背景图片
            customStyle: {
              ...this.form.customStyle,
              ...(serverData.customStyle || {}),
              // 确保关键字段被正确同步
              backgroundType: serverData.customStyle?.backgroundType || this.form.customStyle?.backgroundType || 'color',
              backgroundImage: serverData.customStyle?.backgroundImage || this.form.customStyle?.backgroundImage || '',
              backgroundColor: serverData.customStyle?.backgroundColor || this.form.customStyle?.backgroundColor || '#ffffff',
              gradientIndex: serverData.customStyle?.gradientIndex !== undefined ? serverData.customStyle.gradientIndex : (this.form.customStyle?.gradientIndex || 0),
              textColor: serverData.customStyle?.textColor || this.form.customStyle?.textColor || '#333333',
              borderRadiusIndex: serverData.customStyle?.borderRadiusIndex !== undefined ? serverData.customStyle.borderRadiusIndex : (this.form.customStyle?.borderRadiusIndex || 1),
              cardStyleIndex: serverData.customStyle?.cardStyleIndex !== undefined ? serverData.customStyle.cardStyleIndex : (this.form.customStyle?.cardStyleIndex || 0)
            }
          };

          this.form = updatedForm;

          // 更新本地存储
          uni.setStorageSync('userInfo', this.form);



          // 静默同步，不显示提示
          // uni.showToast({
          //   title: '保存成功',
          //   icon: 'success',
          //   duration: 1500
          // });
        } else {

          uni.showToast({
            title: '网络异常，请检查网络连接',
            icon: 'none',
            duration: 2000
          });
        }
      } catch (error) {
        console.error('加载服务器用户信息失败:', error);
      } finally {
        uni.hideLoading();
      }
    },

    // 静默上传头像（相册和拍照使用，不显示loading避免重复）
    async uploadAvatarSilently(filePath) {
      try {
        // 如果用户已登录，上传到服务器
        if (userService.isLoggedIn()) {
          const result = await userInfoService.uploadAvatar(filePath);

          if (result.success && result.url) {
            // 使用服务器返回的URL
            this.form.avatar = result.url;

            // 强制更新视图
            this.$forceUpdate();

            // 不显示成功提示，避免干扰用户体验
          } else {
            // 上传失败，保持默认头像
            this.form.avatar = '/static/default-avatar.png';
            console.error('头像上传失败:', result.message);
            uni.showToast({
              title: '头像更新失败，请重试',
              icon: 'none'
            });
          }
        } else {
          // 未登录，使用默认头像
          this.form.avatar = '/static/default-avatar.png';
          uni.showToast({
            title: '请先登录后更换头像',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('头像上传失败:', error);

        // 上传失败，使用默认头像
        this.form.avatar = '/static/default-avatar.png';

        // 上传失败时的友好提示
        let errorMessage = '头像上传失败';
        if (error.message.includes('404')) {
          errorMessage = '上传接口不存在，请联系管理员';
        } else if (error.message.includes('directory')) {
          errorMessage = '服务器存储配置问题，请联系管理员';
        } else if (error.message.includes('权限')) {
          errorMessage = '服务器权限问题，请联系管理员';
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        });
      }
      // 注意：这里不需要finally隐藏loading，因为没有显示loading
    },

    // 显示头像选择ActionSheet（仅非微信环境使用）
    showAvatarActionSheet() {
      uni.showActionSheet({
        itemList: ['从相册选择', '拍照'],
        success: (res) => {
          switch(res.tapIndex) {
            case 0: // 从相册选择
              this.chooseAvatarFromAlbum();
              break;
            case 1: // 拍照
              this.chooseAvatarFromCamera();
              break;
          }
        }
      });
    },


    // 从相册选择头像
    chooseAvatarFromAlbum() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album'],
        success: (res) => {
          // 使用静默上传，避免重复loading
          this.uploadAvatarSilently(res.tempFilePaths[0]);
        }
      });
    },

    // 拍照获取头像
    chooseAvatarFromCamera() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['camera'],
        success: (res) => {
          // 使用静默上传，避免重复loading
          this.uploadAvatarSilently(res.tempFilePaths[0]);
        }
      });
    },

    // 上传头像到服务器（微信头像选择使用，会显示loading）
    async uploadAvatarToServer(filePath) {
      try {
        // 显示加载状态
        uni.showLoading({
          title: '上传头像中...',
          mask: true
        });

        // 如果用户已登录，上传到服务器
        if (userService.isLoggedIn()) {
          const result = await userInfoService.uploadAvatar(filePath);

          if (result.success && result.url) {
            // 使用服务器返回的URL
            this.form.avatar = result.url;

            // 强制更新视图
            this.$forceUpdate();

            // 微信头像选择时不显示上传成功提示，避免干扰用户体验
            // uni.showToast({
            //   title: '头像更新成功',
            //   icon: 'success'
            // });
          } else {
            // 上传失败，保持默认头像
            this.form.avatar = '/static/default-avatar.png';
            console.error('头像上传失败:', result.message);
            uni.showToast({
              title: '头像更新失败，请重试',
              icon: 'none'
            });
          }
        } else {
          // 未登录，使用默认头像
          this.form.avatar = '/static/default-avatar.png';
          uni.showToast({
            title: '请先登录后更换头像',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('头像上传失败:', error);

        // 上传失败，使用默认头像
        this.form.avatar = '/static/default-avatar.png';

        // 上传失败时的友好提示
        let errorMessage = '头像上传失败';
        if (error.message.includes('404')) {
          errorMessage = '上传接口不存在，请联系管理员';
        } else if (error.message.includes('directory')) {
          errorMessage = '服务器存储配置问题，请联系管理员';
        } else if (error.message.includes('权限')) {
          errorMessage = '服务器权限问题，请联系管理员';
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        });
      } finally {
        uni.hideLoading();
      }
    },
    
    // 微信头像选择回调（新版API）
    onChooseAvatar(e) {
      if (e.detail && e.detail.avatarUrl) {
        const avatarUrl = e.detail.avatarUrl;
        console.log('微信头像URL:', avatarUrl);

        // 根据微信最新规范：
        // 1. chooseAvatar 返回的是临时文件路径
        // 2. 需要立即上传到服务器保存
        // 3. 临时路径在小程序中可以使用，但有时效性

        // 立即上传到服务器获取永久URL
        this.uploadAvatarToServer(avatarUrl);
      } else {
        uni.showToast({
          title: '头像获取失败，请重试',
          icon: 'none'
        });
      }
    },





    // 添加会员状态检查方法
    checkMemberStatus() {
      try {
        const isMember = uni.getStorageSync('isMember');
        const isPro = uni.getStorageSync('isPro');
        const memberExpireDate = uni.getStorageSync('memberExpireDate');
        
        if (isMember || isPro) {
          this.isPremiumMember = true;
          return true;
        }
        
        return false;
      } catch (e) {
        console.error('加载会员状态失败', e);
        return false;
      }
    },
    
    // 跳转到会员开通页面
    goPremium() {
      uni.navigateTo({ url: '/pages/company/premium' });
    },
    
    // 修改保存方法，根据选项卡实现分级权限检查
    saveCard() {
      // 检查登录状态
      if (!userService.isLoggedIn()) {
        uni.showModal({
          title: '需要登录',
          content: '保存功能需要登录后才能使用，是否前往登录？',
          confirmText: '去登录',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              uni.navigateTo({
                url: '/pages/auth/auth'
              });
            }
          }
        });
        return;
      }

      // 获取会员信息
      const isMember = uni.getStorageSync('isMember');
      const memberLevel = uni.getStorageSync('memberLevel') || 0;
      const memberExpireDate = uni.getStorageSync('memberExpireDate');

      // 检查是否过期
      let isExpired = false;
      if (memberExpireDate) {
        const now = new Date();
        // 修复iOS日期格式兼容性问题
        const formattedDate = memberExpireDate.replace(/\s/g, 'T');
        const expireDate = new Date(formattedDate);
        isExpired = now > expireDate;
      }

      // 根据当前选项卡检查权限
      if (this.activeTab === 0) {
        // 联系信息：需要专业版或企业版
        const hasPermission = isMember && memberLevel >= 1 && !isExpired;
        if (!hasPermission) {
          uni.showModal({
            title: '专业版功能',
            content: '联系信息保存功能需要专业版或企业版权限',
            confirmText: '立即升级',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                this.goPremium();
              }
            }
          });
          return;
        }
      } else if (this.activeTab === 1) {
        // 个性定制：只有企业版
        const isEnterprise = isMember && memberLevel >= 2 && !isExpired;
        if (!isEnterprise) {
          uni.showModal({
            title: '企业版功能',
            content: '个性定制保存功能仅限企业版用户使用，专业版用户需要升级到企业版',
            confirmText: '立即升级',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                this.goPremium();
              }
            }
          });
          return;
        }
      }

      // 有权限，可以正常保存
      // 获取当前存储的完整用户信息
      const currentUserInfo = uni.getStorageSync('userInfo') || {};

      let userInfoToSave;
      let successMessage;

      if (this.activeTab === 0) {
        // 联系信息：只保存联系相关字段
        userInfoToSave = {
          ...currentUserInfo,
          avatar: this.form.avatar || '/static/default-avatar.png',
          name: this.form.name || '徐敏',
          position: this.form.position || '销售专员',
          company: this.form.company || '德萨大数据股份有限公司',
          phone: this.form.phone || '18596320120',
          email: this.form.email || 'www.1099458780.com',
          wechat: this.form.wechat || 'xumin_wx',
          address: this.form.address || '山东省庄市滕州市北辛西路2000号',
          description: this.form.description || '拥有5年销售经验，专注于客户关系维护和业务拓展。热爱学习新技术，善于沟通协调，致力于为客户提供最优质的服务体验。'
        };
        successMessage = '联系信息保存成功';
      } else {
        // 个性定制：只保存样式相关字段
        userInfoToSave = {
          ...currentUserInfo,
          customStyle: {
            ...(currentUserInfo.customStyle || {}),
            backgroundType: this.form.customStyle?.backgroundType || 'color',
            gradientIndex: this.form.customStyle?.gradientIndex || 0,
            backgroundColor: this.form.customStyle?.backgroundColor || '#ffffff',
            backgroundImage: this.form.customStyle?.backgroundImage || '',
            textColor: this.form.customStyle?.textColor || '#333333',
            borderRadiusIndex: this.form.customStyle?.borderRadiusIndex || 1,
            cardStyleIndex: this.form.customStyle?.cardStyleIndex || 0
          }
        };
        successMessage = '个性定制保存成功';
      }

      // 保存到本地存储和服务器
      this.saveToServerAndLocal(userInfoToSave, successMessage);
    },

    // 保存到服务器和本地存储
    async saveToServerAndLocal(userInfoToSave, successMessage) {
      try {
        uni.showLoading({
          title: '保存中...',
          mask: true
        });

        // 先保存到本地存储
        uni.setStorageSync('userInfo', userInfoToSave);

        // 检查用户是否已登录
        const isLoggedIn = await userService.isLoggedIn();

        if (isLoggedIn) {
          // 用户已登录，同步到服务器（联系信息和个性定制都同步）
          const result = await userInfoService.updateUserInfo(userInfoToSave);

          if (result.success) {
            uni.showToast({
              title: successMessage,
              icon: 'success',
              duration: 1500
            });

            // 保存成功后重新从服务器加载数据，确保数据一致性
            setTimeout(() => {
              this.loadUserInfoFromServer();
            }, 500);
          } else {
            // 服务器保存失败，但本地已保存
            uni.showToast({
              title: successMessage + '（网络异常，已保存到本地）',
              icon: 'none',
              duration: 2500
            });
            console.error('服务器保存失败:', result.message);
          }
        } else {
          // 用户未登录，只保存到本地
          uni.showToast({
            title: successMessage,
            icon: 'success',
            duration: 1500
          });
        }

        // 延迟返回，让用户看到保存成功的提示
        setTimeout(() => {
          uni.navigateBack();
        }, 1000);

      } catch (error) {
        console.error('保存失败:', error);
        uni.showToast({
          title: '保存失败，请检查网络后重试',
          icon: 'none',
          duration: 2000
        });
      } finally {
        uni.hideLoading();
      }
    },
    
    // 修改应用样式方法，增加会员检查
    applyCardStyle(idx) {
      // 先设置索引，确保UI显示正确的选中状态
      this.form.customStyle.cardStyleIndex = idx;
      
      // 根据不同风格设置对应的样式
      switch(idx) {
        case 0: // 经典商务
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'solid',
            backgroundColor: '#ffffff',
            backgroundImage: '',
            gradientIndex: -1,
            textColor: '#2c5aa0',
            cardStyleIndex: idx
          };
          break;
        case 1: // 科技蓝
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 0, // 蓝色渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 2: // 简约灰
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'solid',
            backgroundColor: '#f5f7fa',
            backgroundImage: '',
            gradientIndex: -1,
            textColor: '#222222',
            cardStyleIndex: idx
          };
          break;
        case 3: // 暗夜黑
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'solid',
            backgroundColor: '#333333',
            backgroundImage: '',
            gradientIndex: -1,
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 4: // 活力橙
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 1, // 橙色渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 5: // 森林绿
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 2, // 绿色渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 6: // 浪漫粉
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 3, // 粉色渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 7: // 高端黑金
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 4, // 灰色渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#d4af37', // 金色文字
            cardStyleIndex: idx
          };
          break;
        case 8: // 紫色梦幻
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 5, // 紫色渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 9: // 复古棕
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'solid',
            backgroundColor: '#8b5a2b',
            backgroundImage: '',
            gradientIndex: -1,
            textColor: '#f5deb3',
            cardStyleIndex: idx
          };
          break;
        case 10: // 海洋蓝
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 6, // 海洋蓝渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 11: // 珊瑚红
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'solid',
            backgroundColor: '#FF6F61',
            backgroundImage: '',
            gradientIndex: -1,
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 12: // 薄荷绿
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'solid',
            backgroundColor: '#98ff98',
            backgroundImage: '',
            gradientIndex: -1,
            textColor: '#006400',
            cardStyleIndex: idx
          };
          break;
        case 13: // 太空灰
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 11, // 太空灰渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 14: // 日落橙
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 7, // 日落橙渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 15: // 翡翠绿
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 8, // 翡翠绿渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 16: // 商务蓝
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'solid',
            backgroundColor: '#1e3c72',
            backgroundImage: '',
            gradientIndex: -1,
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 17: // 玫瑰金
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 9, // 玫瑰金渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#8b4513',
            cardStyleIndex: idx
          };
          break;
        case 18: // 星空紫
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 10, // 星空紫渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
      }
      
      // 强制更新视图
      this.$forceUpdate();
    },
    
    // 修改 applyBgColor 方法
    applyBgColor(color) {
      this.form.customStyle.backgroundType = 'color';
      this.form.customStyle.backgroundColor = color;
      this.form.customStyle.backgroundImage = '';
      this.form.customStyle.gradientIndex = -1;
      
      // 根据背景色设置文字颜色
      if (color === '#ffffff' || color === '#f5f7fa' || color === '#e6f0ff' || 
          color === '#f0f9eb' || color === '#fef0f0' || color === '#fdf6ec') {
        this.form.customStyle.textColor = '#222222';
      } else {
        this.form.customStyle.textColor = '#ffffff';
      }
      
      // 强制更新视图
      this.$forceUpdate();
    },
    
    // 修改 applyGradient 方法
    applyGradient(idx) {
      
      // 确保索引有效
      if (idx >= 0 && idx < this.gradients.length) {
        this.form.customStyle.backgroundType = 'gradient';
        this.form.customStyle.gradientIndex = idx;
        this.form.customStyle.backgroundImage = '';
        this.form.customStyle.backgroundColor = '';
        this.form.customStyle.textColor = '#ffffff';
        

        
        // 强制更新视图
        this.$forceUpdate();
        
        // 延迟执行，确保样式更新
        setTimeout(() => {
          this.$forceUpdate();
        }, 100);
      } else {
        console.error('无效的渐变索引:', idx);
      }
    },
    
    // 修改 applyBgImage 方法
    applyBgImage(img) {
      this.form.customStyle.backgroundType = 'image';
      this.form.customStyle.backgroundImage = img;
      this.form.customStyle.backgroundColor = '';
      this.form.customStyle.gradientIndex = -1;
      this.form.customStyle.textColor = '#ffffff';
      
      // 强制更新视图
      this.$forceUpdate();
      
      // 延迟执行，确保样式更新
      setTimeout(() => {
        this.$forceUpdate();
      }, 100);
    },
    
    // 修改 applyTextColor 方法
    applyTextColor(color) {
      this.form.customStyle.textColor = color;
      
      // 强制更新视图
      this.$forceUpdate();
    },
    
    // 上传背景图片
    async uploadBgImage() {
      uni.chooseImage({
        count: 1,
        success: async (res) => {
          const tempFilePath = res.tempFilePaths[0];

          try {
            uni.showLoading({
              title: '上传中...',
              mask: true
            });

            // 先使用临时路径显示
            this.form.customStyle.backgroundType = 'image';
            this.form.customStyle.backgroundImage = tempFilePath;
            this.form.customStyle.backgroundColor = '';
            this.form.customStyle.gradientIndex = -1;
            this.form.customStyle.textColor = '#ffffff';
            this.$forceUpdate();

            // 如果用户已登录，上传到服务器
            if (userService.isLoggedIn()) {
              const result = await this.uploadImageToServer(tempFilePath);

              if (result.success && result.url) {
                // 使用服务器返回的URL
                this.form.customStyle.backgroundImage = result.url;
                this.$forceUpdate();

                uni.showToast({
                  title: '背景图片设置成功',
                  icon: 'success'
                });
              } else {
                uni.showToast({
                  title: '背景图片设置成功',
                  icon: 'success'
                });
              }
            } else {
              uni.showToast({
                title: '背景图片设置成功',
                icon: 'success'
              });
            }
          } catch (error) {
            console.error('背景图片上传失败:', error);
            uni.showToast({
              title: '背景图片设置失败',
              icon: 'none'
            });
          } finally {
            uni.hideLoading();
          }
        },
        fail: (err) => {
          console.error('选择图片失败:', err);
          uni.showToast({ title: '图片选择失败，请重试', icon: 'none' });
        }
      });
    },

    // 获取可显示的头像URL
    getDisplayAvatar(avatarUrl) {
      if (!avatarUrl) {
        return '/static/default-avatar.png';
      }

      // 根据微信最新规范：
      // 1. 微信临时路径在小程序内部可以显示
      // 2. 但我们优先使用服务器的HTTPS URL
      // 3. 如果是HTTP路径且不是微信临时路径，则使用默认头像

      if (avatarUrl.startsWith('https://')) {
        // HTTPS URL 可以正常显示
        return avatarUrl;
      } else if (avatarUrl.startsWith('http://tmp/')) {
        // 微信临时路径，在小程序中可以显示
        return avatarUrl;
      } else if (avatarUrl.startsWith('http://')) {
        // 其他HTTP路径，使用默认头像
        return '/static/default-avatar.png';
      } else if (avatarUrl.startsWith('/')) {
        // 相对路径，直接使用
        return avatarUrl;
      }

      return avatarUrl;
    },

    // 上传图片到服务器
    async uploadImageToServer(filePath) {
      try {
        if (!userService.isLoggedIn()) {
          throw new Error('请先登录');
        }

        const token = userService.getToken();

        return new Promise((resolve, reject) => {
          const uploadUrl = `${API_BASE_URL}/upload/image`;

          uni.uploadFile({
            url: uploadUrl,
            filePath: filePath,
            name: 'image',
            header: {
              'Authorization': `Bearer ${token}`
            },
            success: (res) => {
              try {
                console.log('图片上传响应:', res);

                if (res.statusCode !== 200) {
                  reject(new Error(`服务器响应错误: ${res.statusCode}`));
                  return;
                }

                if (!res.data) {
                  reject(new Error('服务器返回空数据'));
                  return;
                }

                const data = JSON.parse(res.data);
                console.log('解析后的数据:', data);

                if (data.code === 200) {
                  resolve({
                    success: true,
                    data: data.data,
                    url: data.data.url
                  });
                } else {
                  reject(new Error(data.message || '上传失败'));
                }
              } catch (error) {
                console.error('解析响应数据失败:', error);
                reject(new Error(`响应数据解析失败: ${error.message}`));
              }
            },
            fail: (error) => {
              console.error('上传请求失败:', error);
              reject(new Error('上传请求失败'));
            }
          });
        });
      } catch (error) {
        console.error('上传图片失败:', error);
        return {
          success: false,
          message: error.message || '上传失败'
        };
      }
    },

    setBorderRadius(idx) {
      this.form.customStyle.borderRadiusIndex = idx;
      this.form.customStyle.borderRadius = this.borderRadiusMap[idx];
    },
    setAvatarStyle(idx) {
      this.form.customStyle.avatarStyleIndex = idx;
      this.form.customStyle.avatarStyle = this.avatarStyleMap[idx];
    },
    setBgColor(color) {
      this.form.customStyle.backgroundType = 'color';
      this.form.customStyle.backgroundColor = color;
      // 清除背景图片
      this.form.customStyle.backgroundImage = '';
      // 根据背景色设置文字颜色
      if (color === '#ffffff' || color === '#f5f7fa' || color === '#e6f0ff' || 
          color === '#f0f9eb' || color === '#fef0f0' || color === '#fdf6ec') {
        this.form.customStyle.textColor = '#222222';
      } else {
        this.form.customStyle.textColor = '#ffffff';
      }
    },
    setTextColor(color) {
      this.form.customStyle.textColor = color;
    },
    setCardStyle(idx) {
      this.form.customStyle.cardStyleIndex = idx;
      
      // 根据不同风格设置对应的样式
      switch(idx) {
        case 0: // 经典商务
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'color',
            backgroundColor: '#ffffff',
            backgroundImage: '',
            gradientIndex: -1,
            textColor: '#2c5aa0',
            cardStyleIndex: idx
          };
          break;
        case 1: // 科技蓝
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 0, // 蓝色渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 2: // 简约灰
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'color',
            backgroundColor: '#f5f7fa',
            backgroundImage: '',
            gradientIndex: -1,
            textColor: '#222222',
            cardStyleIndex: idx
          };
          break;
        case 3: // 暗夜黑
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'color',
            backgroundColor: '#333333',
            backgroundImage: '',
            gradientIndex: -1,
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 4: // 活力橙
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 1, // 橙色渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 5: // 森林绿
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 2, // 绿色渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 6: // 浪漫粉
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 3, // 粉色渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 7: // 高端黑金
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 4, // 灰色渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#d4af37', // 金色文字
            cardStyleIndex: idx
          };
          break;
        case 8: // 紫色梦幻
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 5, // 紫色渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 9: // 复古棕
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'color',
            backgroundColor: '#8b5a2b',
            backgroundImage: '',
            gradientIndex: -1,
            textColor: '#f5deb3',
            cardStyleIndex: idx
          };
          break;
        case 10: // 海洋蓝
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 6, // 海洋蓝渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 11: // 珊瑚红
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'solid',
            backgroundColor: '#FF6F61',
            backgroundImage: '',
            gradientIndex: -1,
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 12: // 薄荷绿
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'solid',
            backgroundColor: '#98ff98',
            backgroundImage: '',
            gradientIndex: -1,
            textColor: '#006400',
            cardStyleIndex: idx
          };
          break;
        case 13: // 太空灰
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 11, // 太空灰渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 14: // 日落橙
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 7, // 日落橙渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 15: // 翡翠绿
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 8, // 翡翠绿渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 16: // 商务蓝
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'color',
            backgroundColor: '#1e3c72',
            backgroundImage: '',
            gradientIndex: -1,
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
        case 17: // 玫瑰金
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 9, // 玫瑰金渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#8b4513',
            cardStyleIndex: idx
          };
          break;
        case 18: // 星空紫
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: 'gradient',
            gradientIndex: 10, // 星空紫渐变
            backgroundColor: '',
            backgroundImage: '',
            textColor: '#ffffff',
            cardStyleIndex: idx
          };
          break;
      }
    },
    setGradient(idx) {
      this.form.customStyle.backgroundType = 'gradient';
      this.form.customStyle.gradientIndex = idx;
      // 清除背景图片和纯色背景
      this.form.customStyle.backgroundImage = '';
      this.form.customStyle.backgroundColor = '';
      this.form.customStyle.textColor = '#ffffff';
      
      // 强制更新视图
      this.$forceUpdate();
    },
    setShadow(isShadow) {
      this.form.customStyle.shadow = isShadow;
    },
    setFontSize(idx) {
      this.form.customStyle.fontSizeIndex = idx;
    },
    setAvatarBorder(hasBorder) {
      this.form.customStyle.avatarBorder = hasBorder;
    },
    goBack() {
      uni.navigateBack();
    },
    handleScroll() {
      // #ifdef H5
      const scrollTop = window.scrollY || document.documentElement.scrollTop;
      if (scrollTop > this.lastScrollTop) {
        // 向下滑动，隐藏按钮
        this.showSaveBtn = false;
      } else {
        // 向上滑动，显示按钮
        this.showSaveBtn = true;
      }
      this.lastScrollTop = scrollTop;
      // #endif
    },
    chooseBgImage() {
      this.form.customStyle.backgroundType = 'image';
    },
    setBgImage(img) {
      this.form.customStyle.backgroundType = 'image';
      this.form.customStyle.backgroundImage = img;
      // 清除纯色背景和渐变背景
      this.form.customStyle.backgroundColor = '';
      this.form.customStyle.gradientIndex = -1;
      this.form.customStyle.textColor = '#ffffff';
      
      // 强制更新视图
      this.$forceUpdate();
      
      // 延迟执行，确保样式更新
      setTimeout(() => {
        this.$forceUpdate();
      }, 100);
    },
    onRegionChange(e) {
      this.regionValue = e.detail.value;
      this.regionText = this.regionValue.join(' ');
    },
    getLocation() {
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          const latitude = res.latitude;
          const longitude = res.longitude;
          
          // 逆地理编码，将经纬度转换为地址
          uni.request({
            url: `https://apis.map.qq.com/ws/geocoder/v1/?location=${latitude},${longitude}&key=您的腾讯地图KEY`,
            success: (res) => {
              if (res.data && res.data.result) {
                const address = res.data.result.address;
                const province = res.data.result.address_component.province;
                const city = res.data.result.address_component.city;
                const district = res.data.result.address_component.district;
                
                this.regionValue = [province, city, district];
                this.regionText = this.regionValue.join(' ');
                this.addressDetail = address.replace(this.regionText, '');
                
                uni.showToast({
                  title: '位置信息已更新',
                  icon: 'success'
                });
              }
            },
            fail: () => {
              uni.showToast({
                title: '地址解析失败，请重试',
                icon: 'none'
              });
            }
          });
        },
        fail: () => {
          uni.showToast({
            title: '位置获取失败，请检查定位权限',
            icon: 'none'
          });
        }
      });
    },
    confirmAddress() {
      const fullAddress = this.regionText + ' ' + this.addressDetail;
      this.form.address = fullAddress.trim();
      this.showAddressModal = false;
      
      // 保存到本地
      uni.setStorageSync('userAddress', {
        regionValue: this.regionValue,
        regionText: this.regionText,
        addressDetail: this.addressDetail
      });
    },
    updateContentPadding() {
      // 直接设置样式，不使用动态类
      if (this.topSectionHeight > 0) {
        const paddingTop = this.statusBarHeight + 45 + this.topSectionHeight;
        const reductionRatio = this.screenHeight < 700 ? 0.25 : 0.2;
        const paddingReduction = this.topSectionHeight * reductionRatio;
        const finalPadding = paddingTop - paddingReduction;
        
        // #ifdef H5
        const contentArea = document.querySelector('.content-area');
        if (contentArea) {
          contentArea.style.paddingTop = `${finalPadding}px`;
        }
        // #endif
        
        // #ifndef H5
        // 在小程序环境中使用uni.createSelectorQuery
        const query = uni.createSelectorQuery();
        query.select('.content-area').boundingClientRect(data => {
          if (data) {
            // 通过样式类或直接设置computed属性来更新
            this.contentPaddingTop = finalPadding;
          }
        }).exec();
        // #endif
      }
    },
    // 添加地址选择器显示方法
    showAddressSelector() {
      this.showAddressModal = true;
    },

    // 初始化用户数据（优先从服务器加载）
    async initializeUserData() {
      // 确保表单数据有默认值
      const defaultForm = {
        avatar: '/static/default-avatar.png',
        name: '徐敏',
        position: '销售专员',
        company: '德萨大数据股份有限公司',
        phone: '18596320120',
        email: 'www.1099458780.com',
        wechat: 'xumin_wx',
        address: '山东省庄市滕州市北辛西路2000号',
        description: '拥有5年销售经验，专注于客户关系维护和业务拓展。热爱学习新技术，善于沟通协调，致力于为客户提供最优质的服务体验。',
        customStyle: {
          backgroundType: 'solid',
          gradientIndex: 0,
          backgroundColor: '#ffffff',
          backgroundImage: '',
          textColor: '#333333',
          fontSize: 'medium',
          avatarStyle: 'rounded',
          borderRadius: 'small',
          borderRadiusIndex: 1,
          cardStyleIndex: 0
        }
      };

      // 先设置默认数据
      this.form = { ...defaultForm };

      // 如果用户已登录，优先从服务器加载数据
      if (userService.isLoggedIn()) {
        try {
          // 显示加载状态
          uni.showLoading({
            title: '加载数据中...',
            mask: true
          });

          const result = await userInfoService.getCurrentUserInfo();

          if (result.success && result.data) {
            const serverData = result.data;

            // 使用服务器数据更新表单
            this.form = {
              ...defaultForm,
              id: serverData.id,
              name: serverData.name || defaultForm.name,
              phone: serverData.phone || defaultForm.phone,
              email: serverData.email || defaultForm.email,
              wechat: serverData.wechat || defaultForm.wechat,
              position: serverData.position || defaultForm.position,
              company: serverData.company || defaultForm.company,
              address: serverData.address || defaultForm.address,
              description: serverData.description || defaultForm.description,
              avatar: serverData.avatar || defaultForm.avatar,
              // 重要：使用服务器的自定义样式数据
              customStyle: {
                ...defaultForm.customStyle,
                ...(serverData.customStyle || {})
              }
            };

            // 保存到本地存储
            uni.setStorageSync('userInfo', this.form);

          } else {
            this.loadFromLocalStorage(defaultForm);
          }

        } catch (error) {
          console.error('从服务器加载数据失败:', error);
          this.loadFromLocalStorage(defaultForm);
        } finally {
          uni.hideLoading();
        }

      } else {
        this.loadFromLocalStorage(defaultForm);
      }
    },

    // 从本地存储加载数据
    loadFromLocalStorage(defaultForm) {
      try {
        // 读取本地存储信息
        const saved = uni.getStorageSync('userInfo');

        // 检查saved是否为有效数据
        const isValidData = saved && typeof saved === 'object' &&
                           Object.keys(saved).length > 0;

        if (isValidData) {
          // 合并保存的数据和默认数据，确保所有字段都存在
          this.form = {
            ...defaultForm,
            ...saved
          };

          // 确保customStyle存在且完整
          if (saved.customStyle) {
            this.form.customStyle = {
              ...defaultForm.customStyle,
              ...saved.customStyle
            };
          } else {
            this.form.customStyle = { ...defaultForm.customStyle };
          }
        } else {
          // 如果没有保存的数据，使用默认值
          this.form = { ...defaultForm };

          // 将默认信息保存到本地存储，确保下次加载时有数据
          uni.setStorageSync('userInfo', this.form);
        }
      } catch (error) {
        // 出错时使用默认信息
        this.form = { ...defaultForm };
        // 尝试保存默认信息
        try {
          uni.setStorageSync('userInfo', this.form);
        } catch (e) {
          // 保存失败时不做处理
        }
      }
    },

    // 读取地址信息
    loadAddressInfo() {
      try {
        const savedAddress = uni.getStorageSync('userAddress');
        if (savedAddress) {
          this.regionValue = savedAddress.regionValue || ['', '', ''];
          this.regionText = savedAddress.regionText || '';
          this.addressDetail = savedAddress.addressDetail || '';
        }
      } catch (error) {
        // 加载地址信息出错时不做处理
      }
    },

    // 获取系统信息
    loadSystemInfo() {
      try {
        const windowInfo = uni.getWindowInfo();
        const statusBarHeight = windowInfo.statusBarHeight || 0;
        this.statusBarHeight = statusBarHeight;
        this.screenWidth = windowInfo.windowWidth || 375;
        this.screenHeight = windowInfo.windowHeight || 667;
        this.deviceRatio = windowInfo.pixelRatio || 1;
      } catch (error) {
        this.statusBarHeight = 44;
      }
    },

    // 强制更新视图
    forceUpdateView() {
      this.$nextTick(() => {
        this.$forceUpdate();

        // 延迟再次更新视图
        setTimeout(() => {
          this.$forceUpdate();
        }, 300);
      });
    }
  },
  mounted() {
    // #ifdef H5
    window.addEventListener('scroll', this.handleScroll);
    // #endif
    
    // 检查会员状态
    this.checkMemberStatus();
  },
  beforeDestroy() {
    // #ifdef H5
    window.removeEventListener('scroll', this.handleScroll);
    // #endif
  },
  onReady() {
    // 在页面渲染完成后立即测量顶部区域高度
    setTimeout(() => {
      const query = uni.createSelectorQuery();
      query.select('.top-section').boundingClientRect(data => {
        if (data) {
          this.topSectionHeight = data.height;
          // 立即更新内容区域的上边距
          this.$nextTick(() => {
            this.updateContentPadding();
          });
        }
      }).exec();
    }, 50); // 使用更短的延迟
  },
  async onLoad() {
    // 检查会员状态
    this.checkMemberStatus();

    // 加载系统信息
    this.loadSystemInfo();

    // 加载地址信息
    this.loadAddressInfo();

    // 优先尝试从服务器加载数据
    await this.initializeUserData();

    // 强制更新视图
    this.forceUpdateView();
  },

  onShow() {
    // 页面显示时同步会员状态
    this.syncMemberStatus();
  }
}
</script>

<style scoped>
.edit-container {
  background: #f7f8fa;
  min-height: 100vh;
  padding-bottom: 140rpx;
}
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  padding: 20rpx 32rpx 20rpx 32rpx;
  box-sizing: border-box;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.navbar-left {
  display: flex;
  align-items: center;
}
.back-btn {
  padding: 8rpx 16rpx;
  background: linear-gradient(to bottom, #ffffff, #f5f5f5);
  border-radius: 20rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 6rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.back-btn:active {
  transform: scale(0.96);
  background: linear-gradient(to bottom, #f5f5f5, #eeeeee);
}

.back-icon {
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 4rpx;
  color: #4f46e5;
}

.back-text {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}
.navbar-title {
  color: #111827;
  font-size: 36rpx;
  font-weight: 700;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  letter-spacing: 0.5rpx;
  background: linear-gradient(to right, #4f46e5, #6366f1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.top-section {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #f7f8fa;
}
.card-preview {
  padding: 0;
  background-color: #f7f8fa;
}
.fixed-tab-bar {
  padding: 0 20rpx;
  margin-bottom: 0; /* 减小底部间距 */
}
.tab-bar {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
  overflow: hidden;
}
.tab-item {
  flex: 1;
  text-align: center;
  font-size: 28rpx; /* 减小字体大小 */
  color: #888;
  padding: 20rpx 0; /* 减小内边距 */
  background: #fff;
  transition: color 0.2s, background 0.2s;
}
.tab-item.active {
  color: #2c5aa0;
  background: #f0f4ff;
  font-weight: bold;
}
.content-area {
  min-height: 100vh;
  padding-top: 0; /* 初始值为0，由JS动态设置 */
  transition: padding-top 0.2s ease-out; /* 添加平滑过渡效果 */
}
.form-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);
}
.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c5aa0;
  margin-right: 16rpx;
}
.section-line {
  flex: 1;
  height: 2rpx;
  background: linear-gradient(90deg, #4a6ef2, rgba(74,110,242,0.1));
}
.form-group {
  margin-bottom: 20rpx;
}
.form-divider {
  height: 1rpx;
  background: #f0f0f0;
  margin: 20rpx 0;
}
.modern-form-item {
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
}
.form-label {
  width: 120rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.input-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
}
.input-icon {
  width: 36rpx;
  height: 36rpx;
  position: absolute;
  left: 16rpx;
  opacity: 0.6;
}

.clear-btn {
  position: absolute;
  right: 16rpx;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e0e0e0;
  border-radius: 50%;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.clear-btn:active {
  transform: scale(0.9);
  background: #d0d0d0;
  box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.15);
}

.clear-icon {
  font-size: 24rpx;
  color: #666;
  font-weight: bold;
  line-height: 1;
}

.error-tip {
  font-size: 24rpx;
  color: #ff4757;
  margin-top: 8rpx;
  margin-left: 16rpx;
}
.form-input {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #e5e5e5;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background: #f8f8f8;
  transition: all 0.3s;
}
.form-input.with-icon {
  padding-left: 60rpx;
  padding-right: 60rpx;
}
.form-input:focus {
  border-color: #4a6ef2;
  background: #f0f4ff;
}
.form-textarea {
  flex: 1;
  min-height: 80rpx;
  border: 1rpx solid #e5e5e5;
  border-radius: 8rpx;
  padding: 8rpx 16rpx;
  font-size: 26rpx;
  background: #f8f8f8;
}
.form-btn {
  font-size: 26rpx;
  color: #4a6ef2;
  background: #f0f4ff;
  border-radius: 8rpx;
  padding: 0 24rpx;
  height: 56rpx;
  border: none;
}
.picker-view {
  flex: 1;
  height: 64rpx;
  display: flex;
  align-items: center;
  border: 1rpx solid #e5e5e5;
  border-radius: 8rpx;
  padding: 0 16rpx;
  font-size: 26rpx;
  background: #f8f8f8;
}
.diy-container {
  padding: 10rpx 20rpx; /* 减小上下内边距 */
  background-color: #f5f7fa;
}
.diy-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);
  margin-bottom: 16rpx; /* 减小底部外边距 */
  overflow: hidden;
}
.diy-card-header {
  padding: 16rpx; /* 减小内边距 */
  border-bottom: 1rpx solid #f0f0f0;
}
.diy-card-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}
.diy-card-content {
  padding: 16rpx; /* 减小内边距 */
}
.diy-option-group {
  margin-bottom: 24rpx;
}
.diy-option-group:last-child {
  margin-bottom: 0;
}
.diy-option-group-title {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
  display: block;
}
.diy-option-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  align-items: center;
  justify-content: flex-start;
  padding: 6rpx 0;
}
.diy-option-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}
.diy-option-preview {
  width: 60rpx;
  height: 40rpx;
  border-radius: 8rpx;
  margin-bottom: 8rpx;
  transition: all 0.2s;
}
.diy-option-label {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50rpx;
  line-height: 1.2;
  padding: 0 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-word;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -webkit-box;
}
.diy-color-block {
  width: 44rpx;
  height: 44rpx;
  border-radius: 8rpx;
  border: 2rpx solid transparent;
  transition: all 0.2s;
  margin: 6rpx;
  position: relative; /* 添加相对定位，用于白色边框 */
}
.diy-color-block.active {
  border-color: #4a6ef2;
  transform: scale(1.1);
  box-shadow: 0 2rpx 8rpx rgba(74,110,242,0.3);
  z-index: 1; /* 确保激活状态的颜色块显示在上层 */
}
.diy-color-block-border {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 1rpx solid #ddd;
  border-radius: 7rpx;
  pointer-events: none; /* 确保不影响点击事件 */
}
.diy-image-block {
  width: 140rpx;
  height: 110rpx;
  border-radius: 10rpx;
  overflow: hidden;
  border: 2rpx solid transparent;
  transition: all 0.2s;
  margin: 10rpx;
}
.diy-image-block.active {
  border-color: #4a6ef2;
  transform: scale(1.05);
  box-shadow: 0 4rpx 12rpx rgba(74,110,242,0.3);
}
.diy-image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.diy-upload-btn {
  width: 140rpx;
  height: 110rpx;
  border-radius: 10rpx;
  border: 2rpx dashed #999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  transition: all 0.2s;
  margin: 10rpx;
}
.diy-upload-btn:active {
  background-color: #f0f0f0;
}
.diy-upload-icon {
  font-size: 36rpx;
  color: #999;
  line-height: 1;
  margin-bottom: 6rpx;
}
.diy-upload-text {
  font-size: 28rpx;
  color: #999;
  line-height: 1;
  margin-top: 8rpx;
}
.diy-text-size-block {
  min-width: 64rpx;
  height: 48rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.2s;
}
.diy-text-size-block.active {
  border-color: #4a6ef2;
  background-color: #e6f0ff;
}
.diy-avatar-block {
  width: 48rpx;
  height: 48rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid transparent;
  transition: all 0.2s;
}
.diy-avatar-block.active {
  border-color: #4a6ef2;
  background-color: #e6f0ff;
}
.diy-avatar-preview {
  width: 36rpx;
  height: 36rpx;
  background-color: #999;
}
.diy-switch-btn {
  min-width: 64rpx;
  height: 48rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12rpx;
  border: 2rpx solid transparent;
  font-size: 26rpx;
  color: #666;
  transition: all 0.2s;
}
.diy-switch-btn.active {
  border-color: #4a6ef2;
  background-color: #e6f0ff;
  color: #4a6ef2;
}

/* 地址输入样式优化 */
.address-item {
  align-items: flex-start;
}

.address-input-container {
  flex: 1;
}

.address-preview {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  border: 1rpx solid #e5e5e5;
  border-radius: 12rpx;
  padding: 0 20rpx 0 60rpx;
  font-size: 28rpx;
  background: #f8f8f8;
}

.address-text {
  flex: 1;
  color: #333;
  word-break: break-all;
}

.address-icon {
  color: #999;
  font-size: 28rpx;
  margin-left: 10rpx;
}

/* 地址选择弹窗 */
.address-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: flex-end;
}

.address-modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.address-modal-content {
  position: relative;
  width: 100%;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 10000;
}

.address-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.address-modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.address-modal-close {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.address-tabs {
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}

.address-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.address-tab.active {
  color: #4a6ef2;
  font-weight: bold;
}

.address-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #4a6ef2;
  border-radius: 2rpx;
}

.address-content {
  padding: 30rpx 24rpx;
  min-height: 400rpx;
}

.address-manual {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.address-picker-group {
  border: 1rpx solid #e5e5e5;
  border-radius: 8rpx;
  overflow: hidden;
}

.region-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f8f8f8;
}

.picker-arrow {
  color: #999;
  font-size: 24rpx;
}

.address-detail-input {
  border: 1rpx solid #e5e5e5;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  background-color: #f8f8f8;
  min-height: 100rpx;
}

.map-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.map-placeholder {
  width: 100%;
  height: 300rpx;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.map-image {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.map-tip {
  font-size: 28rpx;
  color: #999;
}

.map-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #4A90E2, #357abd);
  color: #fff;
  font-size: 28rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.address-modal-footer {
  display: flex;
  padding: 24rpx;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.address-cancel-btn,
.address-confirm-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  border: none;
}

.address-cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.address-confirm-btn {
  background: linear-gradient(135deg, #4A90E2, #357abd);
  color: #fff;
}

/* 名片风格横向滚动样式 */
.style-scroll-view {
  width: 100%;
  white-space: nowrap;
  margin: 10rpx 0;
  height: 260rpx;
  padding-bottom: 50rpx;
}

.style-scroll-content {
  display: inline-flex;
  padding: 10rpx 0;
  height: 100%;
}

.style-option-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  margin-right: 24rpx;
  width: 160rpx;
  transition: all 0.2s;
  position: relative;
  padding-bottom: 50rpx;
  height: 200rpx; /* 固定高度 */
}

.style-option-preview {
  width: 160rpx;
  height: 200rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.12);
  padding: 16rpx;
  box-sizing: border-box;
  transition: all 0.2s;
  position: relative;
  overflow: hidden;
}

.style-preview-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.style-preview-avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: rgba(0,0,0,0.2);
  margin-bottom: 16rpx;
}

.style-preview-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.style-preview-name {
  height: 16rpx;
  width: 80rpx;
  background-color: rgba(0,0,0,0.2);
  border-radius: 4rpx;
}

.style-preview-position {
  height: 12rpx;
  width: 60rpx;
  background-color: rgba(0,0,0,0.15);
  border-radius: 4rpx;
}

.style-option-label {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50rpx;
  line-height: 1.2;
  padding: 0 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-word;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  background-color: rgba(255,255,255,0.7);
  border-radius: 0 0 12rpx 12rpx;
  margin-top: 4rpx;
}

.style-option-item.active .style-option-preview {
  transform: scale(1.05);
  box-shadow: 0 4rpx 16rpx rgba(74,110,242,0.5);
  border: 3rpx solid #4a6ef2;
}

.style-option-item.active .style-option-label {
  color: #4a6ef2;
  font-weight: bold;
}

/* 保存按钮样式 */
.save-btn-float {
  position: fixed;
  left: 5%;
  width: 90%;
  bottom: 40rpx;
  height: 88rpx;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #4A90E2, #357abd);
  color: #fff;
  font-size: 30rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
  border: none;
  opacity: 0;
  transform: translateY(100%);
  transition: all 0.3s cubic-bezier(.4,0,.2,1);
  z-index: 9999;
}

.save-btn-float.show {
  opacity: 1;
  transform: translateY(0);
}

/* 名片样式 */
.classic-card-v2 {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.12);
  padding: 32rpx 30rpx 28rpx 30rpx;
  position: relative;
  border: 1rpx solid #e0e0e0;
  overflow: hidden; /* 确保背景图片不会溢出 */
}

/* 背景图片样式 */
.classic-card-v2-bg-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

/* 背景图片覆盖层 */
.classic-card-v2-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

/* 内容容器 */
.classic-card-v2-content {
  position: relative;
  z-index: 2;
}

/* 为背景图片添加暗色遮罩，提高文字可读性 */
.classic-card-v2 {
  isolation: isolate; /* 创建新的层叠上下文 */
}

.classic-card-v2::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;
}

.classic-card-v2[style*="background-image"]::before {
  opacity: 1;
}

.classic-card-v2-header,
.classic-card-v2-divider,
.classic-card-v2-contact-list {
  position: relative;
  z-index: 2;
}

.classic-card-v2-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
}

.classic-card-v2-info {
  flex: 1;
  min-width: 0;
}

.classic-card-v2-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.classic-card-v2-name {
  font-size: 48rpx;
  font-weight: bold;
  color: #222;
  margin-right: 18rpx;
}

.classic-card-v2-position {
  font-size: 28rpx;
  border-radius: 24rpx;
  padding: 4rpx 20rpx;
  margin-left: 8rpx;
  transition: all 0.3s ease;
}

.classic-card-v2-company {
  font-size: 28rpx;
  color: #4a6ef2;
  margin-bottom: 0;
}

.classic-card-v2-avatar-box {
  position: relative;
  width: 120rpx;
  height: 120rpx;
}

.classic-card-v2-avatar-bg {
  background: #f7bfa3;
  border-radius: 50%;
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.classic-card-v2-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 2rpx solid rgba(255,255,255,0.8);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.classic-card-v2-cert {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #fff;
  border: 4rpx solid #fff;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
}

.classic-card-v2-divider {
  height: 1rpx;
  background: #f0f0f0;
  margin: 24rpx 0 18rpx 0;
}

.classic-card-v2-contact-list {
  margin-top: 0;
}

.classic-card-v2-contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 18rpx;
}

.classic-card-v2-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.classic-card-v2-label {
  font-size: 28rpx;
  color: #888;
  margin-right: 8rpx;
}

.classic-card-v2-value {
  font-size: 28rpx;
  color: #4a6ef2;
  word-break: break-all;
}

/* 小屏幕设备样式调整 - 进一步减小 */
@media screen and (max-height: 700px) {
  .classic-card-v2 {
    padding: 24rpx 24rpx 20rpx 24rpx;
  }
  
  .tab-item {
    padding: 16rpx 0;
    font-size: 26rpx;
  }
  
  .form-section {
    padding: 16rpx 14rpx 6rpx 14rpx;
  }
  
  .form-title {
    margin-bottom: 10rpx;
  }
  
  .form-item {
    margin-bottom: 10rpx;
  }
}

/* 确保名片预览中的文字颜色可以被动态修改 */
.classic-card-v2-name,
.classic-card-v2-company,
.classic-card-v2-value {
  transition: color 0.3s ease;
}

/* 头像选择器样式 */
.avatar-item {
  align-items: flex-start;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx dashed #e0e0e0;
}

.avatar-selector {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 30rpx;
}

.avatar-preview {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  border: 4rpx solid #f0f0f0;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
  margin-bottom: 20rpx;
}

.avatar-buttons {
  display: flex;
  flex-direction: row;
  gap: 20rpx;
  margin-top: 16rpx;
  align-items: center;
  justify-content: center;
}

.avatar-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(135deg, #1aad19, #2dc653);
  color: white;
  font-size: 24rpx;
  padding: 16rpx 20rpx;
  border-radius: 16rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(26, 173, 25, 0.3);
  min-width: 120rpx;
}

.avatar-btn::after {
  border: none;
}

.wechat-avatar-btn {
  background: linear-gradient(135deg, #1aad19, #2dc653);
}

.btn-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.btn-text {
  font-size: 26rpx;
  font-weight: 500;
  color: white;
}

.avatar-change-btn {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: white;
  padding: 12rpx 32rpx;
  border-radius: 50rpx;
  font-size: 26rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
  transition: all 0.3s ease;
  text-align: center;
  height: 60rpx;
  line-height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-change-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.4);
  background: linear-gradient(135deg, #389e0d, #52c41a);
}

/* 微信小程序button组件样式重置 */
.avatar-change-btn::after {
  border: none;
}

/* 悬停效果（支持的平台） */
.avatar-change-btn:hover {
  background: linear-gradient(135deg, #73d13d, #95de64);
  box-shadow: 0 6rpx 16rpx rgba(82, 196, 26, 0.4);
  transform: translateY(-2rpx);
}







/* 底部安全区域，防止保存按钮遮挡内容 */
.bottom-safe-area {
  height: 120rpx;
  width: 100%;
}

/* 为背景图片场景单独添加类 */
.classic-card-v2-with-bg-image::before {
  opacity: 1 !important;
}

/* 个人介绍样式 */
.profile-section {
	background: linear-gradient(145deg, #ffffff, #f8faff);
	border-radius: 20rpx;
	padding: 0;
	margin-top: 30rpx;
	box-shadow: 0 10rpx 30rpx rgba(59, 130, 246, 0.08);
	position: relative;
	overflow: hidden;
	border: 1rpx solid rgba(59, 130, 246, 0.1);
}

.profile-section::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 1rpx;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

.profile-header {
	padding: 28rpx 24rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1rpx solid rgba(59, 130, 246, 0.08);
	background: linear-gradient(90deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.02));
	position: relative;
}

.profile-header::after {
	content: "";
	position: absolute;
	bottom: 0;
	left: 24rpx;
	right: 24rpx;
	height: 1rpx;
	background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
}

.profile-header-left {
	display: flex;
	align-items: center;
}

.profile-icon-wrapper {
	width: 48rpx;
	height: 48rpx;
	border-radius: 12rpx;
	background: linear-gradient(135deg, #3b82f6, #60a5fa);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.2);
}

.profile-icon {
	width: 32rpx;
	height: 32rpx;
	filter: brightness(0) invert(1);
}

.profile-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #1e40af;
	position: relative;
	letter-spacing: 2rpx;
}

.profile-header-badge {
	background: linear-gradient(90deg, #3b82f6, #60a5fa);
	border-radius: 30rpx;
	padding: 8rpx 20rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.15);
}

.profile-badge-text {
	font-size: 22rpx;
	color: white;
	font-weight: 500;
	letter-spacing: 1rpx;
}

.badge-dot {
	width: 8rpx;
	height: 8rpx;
	border-radius: 50%;
	background: #ffffff;
	margin-left: 8rpx;
	box-shadow: 0 0 4rpx rgba(255, 255, 255, 0.8);
}

.profile-content {
	padding: 30rpx 24rpx;
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

.profile-desc-container {
	background: rgba(59, 130, 246, 0.02);
	border-radius: 16rpx;
	padding: 24rpx;
	border-left: 4rpx solid rgba(59, 130, 246, 0.2);
	position: relative;
}

.profile-desc-decoration-top {
	position: absolute;
	top: 12rpx;
	right: 12rpx;
	width: 20rpx;
	height: 20rpx;
	border-top: 3rpx solid rgba(59, 130, 246, 0.2);
	border-right: 3rpx solid rgba(59, 130, 246, 0.2);
}

.profile-desc-decoration-bottom {
	position: absolute;
	bottom: 12rpx;
	right: 12rpx;
	width: 20rpx;
	height: 20rpx;
	border-bottom: 3rpx solid rgba(59, 130, 246, 0.2);
	border-right: 3rpx solid rgba(59, 130, 246, 0.2);
}

.profile-desc {
	font-size: 28rpx;
	color: #4b5563;
	line-height: 1.8;
	text-align: justify;
	letter-spacing: 0.5rpx;
}

.profile-section-header {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.section-header-line {
	width: 8rpx;
	height: 28rpx;
	background: linear-gradient(to bottom, #3b82f6, #60a5fa);
	border-radius: 4rpx;
	margin-right: 12rpx;
}

.section-header-icon {
	font-size: 28rpx;
	margin-right: 10rpx;
}

.section-header-title {
	font-size: 30rpx;
	color: #1e40af;
	font-weight: 600;
	letter-spacing: 1rpx;
}

.profile-tags-container {
	margin-top: 10rpx;
}

.profile-tags-wrapper {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
	margin-top: 20rpx;
}

.profile-tag-item {
	display: flex;
	align-items: center;
	background: linear-gradient(145deg, #ffffff, #f8faff);
	padding: 12rpx 20rpx;
	border-radius: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.06);
	border: 1rpx solid rgba(59, 130, 246, 0.08);
	transition: all 0.3s ease;
}

.profile-tag-item:active {
	transform: translateY(-4rpx);
	box-shadow: 0 8rpx 20rpx rgba(59, 130, 246, 0.1);
}

.tag-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #3b82f6, #60a5fa);
	margin-right: 10rpx;
	box-shadow: 0 2rpx 6rpx rgba(59, 130, 246, 0.2);
}

.tag-text {
	font-size: 26rpx;
	color: #1e40af;
	font-weight: 500;
}

.profile-achievements-container {
	margin-top: 10rpx;
}

.profile-achievements-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	margin-top: 20rpx;
}

.profile-achievement-item {
	display: flex;
	align-items: flex-start;
	background: linear-gradient(145deg, #ffffff, #f8faff);
	padding: 20rpx;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.06);
	border: 1rpx solid rgba(59, 130, 246, 0.08);
	transition: all 0.3s ease;
}

.profile-achievement-item:active {
	transform: translateY(-4rpx);
	box-shadow: 0 8rpx 20rpx rgba(59, 130, 246, 0.1);
}

.achievement-icon {
	width: 44rpx;
	height: 44rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #3b82f6, #60a5fa);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
	flex-shrink: 0;
	box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.15);
}

.achievement-icon-text {
	font-size: 22rpx;
	color: white;
	font-weight: bold;
}

.achievement-content {
	flex: 1;
	position: relative;
}

.achievement-text {
	font-size: 28rpx;
	color: #1e40af;
	font-weight: 500;
	line-height: 1.6;
}

.achievement-decoration {
	height: 2rpx;
	width: 60rpx;
	background: linear-gradient(90deg, #3b82f6, rgba(59, 130, 246, 0.2));
	margin-top: 12rpx;
}

.profile-education-container {
	margin-top: 10rpx;
}

.profile-education-timeline {
	margin-top: 20rpx;
}

.profile-education-item {
	display: flex;
	margin-bottom: 24rpx;
}

.profile-education-item:last-child {
	margin-bottom: 0;
}

.education-timeline-left {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-right: 16rpx;
	padding-top: 8rpx;
}

.timeline-dot {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #3b82f6, #60a5fa);
	box-shadow: 0 0 0 6rpx rgba(59, 130, 246, 0.1);
}

.timeline-line {
	width: 2rpx;
	flex: 1;
	background: linear-gradient(to bottom, #3b82f6, rgba(59, 130, 246, 0.2));
	margin: 8rpx 0;
}

.education-content {
	background: linear-gradient(145deg, #ffffff, #f8faff);
	padding: 20rpx;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.06);
	border: 1rpx solid rgba(59, 130, 246, 0.08);
	flex: 1;
}

.education-school {
	font-size: 28rpx;
	color: #1e40af;
	font-weight: 600;
	margin-bottom: 8rpx;
	display: block;
}

.education-major {
	font-size: 26rpx;
	color: #4b5563;
	margin-bottom: 8rpx;
	display: block;
}

.education-time {
	font-size: 24rpx;
	color: #6b7280;
	display: block;
}

.profile-footer {
	padding: 16rpx 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-top: 1rpx solid rgba(59, 130, 246, 0.05);
}

.footer-line {
	width: 60rpx;
	height: 2rpx;
	background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
}

.footer-dot {
	width: 6rpx;
	height: 6rpx;
	border-radius: 50%;
	background: rgba(59, 130, 246, 0.3);
	margin: 0 4rpx;
}

/* 删除企业版/专业版限制遮罩样式 */

/* 添加禁用状态样式 */
.form-input:disabled {
  background-color: #f5f5f5;
  color: #999;
}

.avatar-upload-btn.disabled, .address-preview.disabled {
  opacity: 0.6;
  background: #f5f5f5;
  color: #999;
}

/* 删除会员提示条样式 */

/* CSS-based contact icons */
.contact-icon {
  width: 24px;
  height: 24px;
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Phone icon */
.phone-icon::before {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-radius: 2px;
  transform: rotate(45deg);
  top: 2px;
  left: 4px;
}

.phone-icon::after {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
  top: 8px;
  left: 8px;
}

/* Chat icon */
.chat-icon::before {
  content: '';
  position: absolute;
  width: 18px;
  height: 14px;
  border: 2px solid currentColor;
  border-radius: 4px;
  top: 4px;
  left: 3px;
}

.chat-icon::after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 6px solid currentColor;
  bottom: 2px;
  left: 7px;
}

/* Email icon */
.email-icon::before {
  content: '';
  position: absolute;
  width: 18px;
  height: 14px;
  border: 2px solid currentColor;
  border-radius: 2px;
  top: 4px;
  left: 3px;
}

.email-icon::after {
  content: '';
  position: absolute;
  width: 14px;
  height: 8px;
  border-left: 2px solid currentColor;
  border-right: 2px solid currentColor;
  border-top: 2px solid currentColor;
  border-radius: 2px 2px 0 0;
  transform: rotate(-45deg);
  top: 7px;
  left: 5px;
}

/* Location icon */
.location-icon::before {
  content: '';
  position: absolute;
  width: 14px;
  height: 14px;
  border: 2px solid currentColor;
  border-radius: 50% 50% 50% 0;
  transform: rotate(-45deg);
  top: 2px;
  left: 5px;
}

.location-icon::after {
  content: '';
  position: absolute;
  width: 4px;
  height: 4px;
  background-color: currentColor;
  border-radius: 50%;
  top: 7px;
  left: 10px;
}
</style>
