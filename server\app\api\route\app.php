<?php
// +----------------------------------------------------------------------
// | 电子名片 API 路由配置
// +----------------------------------------------------------------------

use think\facade\Route;

// API 路由组
Route::group('api', function () {
    
    // 用户相关接口
    Route::group('user', function () {
        // Route::get('test', 'User/test');                    // 测试接口
        Route::get('list', 'User/list');                   // 用户列表（验证用）
        Route::post('register', 'User/register');           // 用户注册
        Route::post('login', 'User/login');                 // 用户登录
        Route::post('wechat-login', 'User/wechatLogin');     // 微信登录
        Route::post('wechat-phone-login', 'User/wechatPhoneLogin'); // 微信手机号登录
        Route::get('info', 'User/info');                    // 获取用户信息
        Route::post('update', 'User/update');               // 更新用户信息

        Route::post('change-password', 'User/changePassword'); // 修改密码
        Route::post('refresh-token', 'User/refreshToken');  // 刷新Token
        // userinfo 接口（临时放在 user 组中）
        Route::get('read', 'User/read');                    // 读取用户详细信息
        Route::post('save', 'User/save');                   // 保存用户信息
    });

    // 名片相关接口
    Route::group('card', function () {
        Route::get('info/:user_id', 'Card/info');           // 获取名片信息
        Route::post('share/:user_id', 'Card/share');        // 分享名片
        Route::post('collect/:user_id', 'Card/collect');    // 收藏名片
        Route::get('stats', 'Card/stats');                  // 获取统计数据
        Route::get('visitors', 'Card/visitors');            // 获取访客记录
        Route::get('ai-analysis', 'Card/aiAnalysis');       // AI数据分析
    });
    
    // 会员相关接口
    Route::group('premium', function () {
        Route::get('plans', 'Premium/plans');               // 获取套餐列表
        Route::get('status', 'Premium/status');             // 获取会员状态
        Route::post('create-order', 'Premium/createOrder'); // 创建订单
        Route::post('verify-code', 'Premium/verifyCode');   // 验证激活码
        Route::post('activate', 'Premium/activate');        // 激活会员
        Route::get('features', 'Premium/features');         // 获取功能列表
        Route::get('orders', 'Premium/orders');             // 获取订单列表
    });

    // 支付相关接口
    Route::group('payment', function () {
        Route::post('create-order', 'Payment/createOrder'); // 创建支付订单
        Route::post('verify-code', 'Payment/verifyCode');   // 验证激活码
        Route::get('order-status', 'Payment/orderStatus');  // 查询订单状态
        Route::post('wechat/notify', 'Payment/wechatNotify'); // 微信支付回调
    });
    
    // 文件上传接口
    Route::group('upload', function () {
        Route::post('avatar', 'api/Upload/avatar');             // 上传头像
        Route::post('image', 'api/Upload/image');               // 上传图片
        Route::post('batch-images', 'api/Upload/batchImages'); // 批量上传图片
        Route::post('cleanup', 'api/Upload/cleanup');           // 清理旧文件
    });
    
}); // 使用 ThinkAdmin 内置的 CORS 功能
