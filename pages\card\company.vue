<template>
	<view class="company-page">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="navbarStyle">
			<view class="navbar-left">
				<view class="back-btn" @click="goBack">
					<text class="back-icon">←</text>
					<text class="back-text">返回</text>
				</view>
			</view>
			<view class="navbar-title">企业展示</view>
		</view>

		<!-- 主要内容 -->
		<view class="main-content" :style="mainStyle">
			<!-- 企业版提示 -->
			<view class="premium-card" v-if="showPremiumNotice">
				<view class="premium-icon">👑</view>
				<view class="premium-info">
					<text class="premium-title">1企业版专属</text>
					<text class="premium-desc">解锁完整企业展示功能</text>
				</view>
				<view class="upgrade-btn">
					<text class="upgrade-text">升级</text>
				</view>
			</view>

			<!-- 主开关 -->
			<view class="switch-card">
				<view class="card-header">
					<view class="header-info">
						<text class="card-title">2企业展示</text>
						<text class="card-desc">控制整个企业展示模块</text>
					</view>
					<switch
						:checked="companyData.showSection"
						@change="onSwitchChange"
						color="#111827"
					/>
				</view>
			</view>

			<!-- 企业信息卡片 -->
			<view class="info-card" :class="{'disabled': !companyData.showSection}">
				<view class="card-header" @click="toggleExpand('info')">
					<view class="header-info">
						<text class="card-title">企业信息</text>
						<text class="card-desc">基本信息与介绍</text>
					</view>
					<view class="expand-btn" :class="{'expanded': expandStatus.info}">
						<text class="expand-icon">›</text>
					</view>
				</view>

				<view class="card-content" v-if="expandStatus.info">
					<!-- 企业名称 -->
					<view class="input-group">
						<view class="input-header">
							<text class="input-label">企业名称</text>
							<switch
								:checked="companyData.showName"
								@change="(e) => companyData.showName = e.detail.value"
								color="#111827"
							/>
						</view>
						<input
							class="modern-input"
							v-model="companyData.name"
							placeholder="请输入企业名称"
							maxlength="50"
						/>
					</view>

					<!-- 企业简介 -->
					<view class="input-group">
						<view class="input-header">
							<text class="input-label">企业简介</text>
							<switch
								:checked="companyData.showDesc"
								@change="(e) => companyData.showDesc = e.detail.value"
								color="#111827"
							/>
						</view>
						<textarea
							class="modern-textarea"
							v-model="companyData.desc"
							placeholder="介绍企业的主营业务和特色"
							maxlength="200"
						/>
					</view>

					<!-- 企业口号 -->
					<view class="input-group">
						<view class="input-header">
							<text class="input-label">企业口号</text>
							<switch
								:checked="companyData.showSlogan"
								@change="(e) => companyData.showSlogan = e.detail.value"
								color="#111827"
							/>
						</view>
						<input
							class="modern-input"
							v-model="companyData.slogan"
							placeholder="请输入企业口号"
							maxlength="50"
						/>
					</view>

					<!-- 企业地址 -->
					<view class="input-group">
						<view class="input-header">
							<text class="input-label">企业地址</text>
							<switch
								:checked="companyData.showAddress"
								@change="(e) => companyData.showAddress = e.detail.value"
								color="#111827"
							/>
						</view>
						<input
							class="modern-input"
							v-model="companyData.address"
							placeholder="请输入企业地址"
							maxlength="100"
						/>
					</view>
				</view>
			</view>

			<!-- 企业图片卡片 -->
			<view class="media-card" :class="{'disabled': !companyData.showSection}">
				<view class="card-header" @click="toggleExpand('images')">
					<view class="header-info">
						<text class="card-title">企业图片</text>
						<text class="card-desc">展示企业环境和产品</text>
					</view>
					<view class="expand-btn" :class="{'expanded': expandStatus.images}">
						<text class="expand-icon">›</text>
					</view>
				</view>

				<view class="card-content" v-if="expandStatus.images">
					<view class="media-grid">
						<!-- 已上传的图片 -->
						<view
							class="media-item"
							v-for="(image, index) in companyData.images"
							:key="index"
						>
							<image
								:src="image"
								class="media-preview"
								mode="aspectFill"
							/>
							<view class="delete-btn" @click="deleteImage(index)">
								<text class="delete-icon">×</text>
							</view>
						</view>

						<!-- 上传按钮 -->
						<view
							class="upload-item"
							@click="uploadImage"
							v-if="companyData.images.length < 9"
						>
							<text class="upload-icon">+</text>
							<text class="upload-text">添加图片</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 企业荣誉卡片 -->
			<view class="honor-card" :class="{'disabled': !companyData.showSection}">
				<view class="card-header" @click="toggleExpand('honors')">
					<view class="header-info">
						<text class="card-title">企业荣誉</text>
						<text class="card-desc">展示获得的奖项和认证</text>
					</view>
					<view class="expand-btn" :class="{'expanded': expandStatus.honors}">
						<text class="expand-icon">›</text>
					</view>
				</view>

				<view class="card-content" v-if="expandStatus.honors">
					<view class="honor-grid">
						<!-- 已添加的荣誉 -->
						<view
							class="honor-item"
							v-for="(honor, index) in companyData.honors"
							:key="index"
						>
							<image
								:src="honor.image"
								class="honor-image"
								mode="aspectFill"
							/>
							<view class="honor-info">
								<text class="honor-title">{{ honor.title }}</text>
							</view>
							<view class="delete-btn" @click="deleteHonor(index)">
								<text class="delete-icon">×</text>
							</view>
						</view>

						<!-- 添加荣誉按钮 -->
						<view
							class="add-honor-item"
							@click="addHonor"
							v-if="companyData.honors.length < 6"
						>
							<text class="add-icon">+</text>
							<text class="add-text">添加荣誉</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部保存按钮 -->
		<view class="bottom-actions">
			<view class="save-btn" @click="saveData">
				<text class="save-text">保存设置</text>
			</view>
		</view>

		<!-- 荣誉编辑弹窗 -->
		<view class="honor-modal" v-if="showHonorModal" @click="closeHonorModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">添加荣誉</text>
					<view class="close-btn" @click="closeHonorModal">
						<text class="close-icon">×</text>
					</view>
				</view>
				<view class="modal-body">
					<view class="input-group">
						<text class="input-label">荣誉标题</text>
						<input
							class="modern-input"
							v-model="currentHonor.title"
							placeholder="请输入荣誉标题"
							maxlength="30"
						/>
					</view>
					<view class="input-group">
						<text class="input-label">荣誉图片</text>
						<view class="image-upload" @click="selectHonorImage">
							<image
								v-if="currentHonor.image"
								:src="currentHonor.image"
								class="preview-image"
								mode="aspectFill"
							/>
							<view v-else class="upload-placeholder">
								<text class="upload-icon">+</text>
								<text class="upload-text">选择图片</text>
							</view>
						</view>
					</view>
				</view>
				<view class="modal-footer">
					<view class="cancel-btn" @click="closeHonorModal">
						<text class="cancel-text">取消</text>
					</view>
					<view class="confirm-btn" @click="confirmHonor">
						<text class="confirm-text">确定</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			// 状态栏高度
			statusBarHeight: 0,

			// 企业版提示
			showPremiumNotice: false,

			// 展开状态
			expandStatus: {
				info: true,
				images: false,
				honors: false
			},

			// 企业数据
			companyData: {
				showSection: true,
				// 基本信息
				name: '',
				showName: true,
				desc: '',
				showDesc: true,
				slogan: '',
				showSlogan: true,
				address: '',
				showAddress: true,
				// 图片
				images: [],
				// 荣誉
				honors: []
			},

			// 荣誉弹窗
			showHonorModal: false,
			currentHonor: {
				title: '',
				image: ''
			}
		}
	},

	computed: {
		navbarStyle() {
			return {
				height: `${this.statusBarHeight + 45}px`
			}
		},
		mainStyle() {
			return {
				marginTop: `${this.statusBarHeight + 55}px`
			}
		}
	},

	onLoad() {
		this.getSystemInfo();
		this.loadData();
	},

	methods: {
		// 获取系统信息
		getSystemInfo() {
			const systemInfo = uni.getSystemInfoSync();
			this.statusBarHeight = systemInfo.statusBarHeight || 0;
		},

		// 返回
		goBack() {
			uni.navigateBack();
		},

		// 加载数据
		loadData() {
			// 这里可以从API加载数据
			console.log('加载企业数据');
		},

		// 保存数据
		saveData() {
			uni.showLoading({
				title: '保存中...'
			});

			// 这里调用API保存数据
			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '保存成功',
					icon: 'success'
				});
			}, 1000);
		},

		// 主开关变化
		onSwitchChange(e) {
			this.companyData.showSection = e.detail.value;
		},

		// 切换展开状态
		toggleExpand(section) {
			this.expandStatus[section] = !this.expandStatus[section];
		},

		// 上传图片
		uploadImage() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					const tempFilePath = res.tempFilePaths[0];
					// 这里可以上传到服务器
					this.companyData.images.push(tempFilePath);
				}
			});
		},

		// 删除图片
		deleteImage(index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这张图片吗？',
				success: (res) => {
					if (res.confirm) {
						this.companyData.images.splice(index, 1);
					}
				}
			});
		},

		// 添加荣誉
		addHonor() {
			this.currentHonor = {
				title: '',
				image: ''
			};
			this.showHonorModal = true;
		},

		// 选择荣誉图片
		selectHonorImage() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					this.currentHonor.image = res.tempFilePaths[0];
				}
			});
		},

		// 确认添加荣誉
		confirmHonor() {
			if (!this.currentHonor.title.trim()) {
				uni.showToast({
					title: '请输入荣誉标题',
					icon: 'none'
				});
				return;
			}

			if (!this.currentHonor.image) {
				uni.showToast({
					title: '请选择荣誉图片',
					icon: 'none'
				});
				return;
			}

			this.companyData.honors.push({
				title: this.currentHonor.title,
				image: this.currentHonor.image
			});

			this.closeHonorModal();
		},

		// 关闭荣誉弹窗
		closeHonorModal() {
			this.showHonorModal = false;
		},

		// 删除荣誉
		deleteHonor(index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这个荣誉吗？',
				success: (res) => {
					if (res.confirm) {
						this.companyData.honors.splice(index, 1);
					}
				}
			});
		}
	}
}
</script>

<style scoped>
/* 页面容器 */
.company-page {
	min-height: 100vh;
	background: #fafbfc;
	position: relative;
}

/* 自定义导航栏 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	background: rgba(255, 255, 255, 0.98);
	backdrop-filter: blur(20rpx);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
	padding: 20rpx 40rpx 24rpx 40rpx;
	box-sizing: border-box;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);
}

.navbar-left {
	display: flex;
	align-items: center;
}

.back-btn {
	padding: 12rpx 20rpx;
	background: transparent;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
	transition: all 0.2s ease;
}

.back-btn:active {
	background: rgba(0, 0, 0, 0.04);
}

.back-icon {
	font-size: 32rpx;
	color: #1f2937;
	font-weight: 500;
}

.back-text {
	font-size: 28rpx;
	color: #1f2937;
	font-weight: 500;
}

.navbar-title {
	color: #111827;
	font-size: 34rpx;
	font-weight: 600;
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	letter-spacing: -0.5rpx;
}

/* 主要内容 */
.main-content {
	padding: 40rpx 32rpx;
	padding-bottom: calc(160rpx + env(safe-area-inset-bottom));
	position: relative;
	z-index: 1;
}

/* 企业版提示卡片 */
.premium-card {
	background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
	border-radius: 20rpx;
	padding: 40rpx;
	display: flex;
	align-items: center;
	gap: 32rpx;
	margin-bottom: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.1);
}

.premium-icon {
	width: 88rpx;
	height: 88rpx;
	background: rgba(255, 255, 255, 0.15);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 44rpx;
}

.premium-info {
	flex: 1;
}

.premium-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
	margin-bottom: 8rpx;
}

.premium-desc {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.7);
	line-height: 1.4;
}

.upgrade-btn {
	padding: 20rpx 32rpx;
	background: rgba(255, 255, 255, 0.15);
	border-radius: 12rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	transition: all 0.2s ease;
}

.upgrade-btn:active {
	background: rgba(255, 255, 255, 0.25);
}

.upgrade-text {
	color: #ffffff;
	font-size: 28rpx;
	font-weight: 500;
}

/* 卡片样式 */
.switch-card, .info-card, .media-card, .honor-card {
	background: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	border: 1rpx solid rgba(0, 0, 0, 0.06);
	overflow: hidden;
	transition: all 0.2s ease;
}

.switch-card:active, .info-card:active, .media-card:active, .honor-card:active {
	transform: translateY(-1rpx);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.disabled {
	opacity: 0.4;
	pointer-events: none;
}

.card-header {
	padding: 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);
}

.header-info {
	flex: 1;
}

.card-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #111827;
	margin-bottom: 8rpx;
	letter-spacing: -0.3rpx;
}

.card-desc {
	font-size: 26rpx;
	color: #6b7280;
	line-height: 1.4;
	font-weight: 400;
}

.expand-btn {
	width: 56rpx;
	height: 56rpx;
	background: rgba(0, 0, 0, 0.04);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s ease;
}

.expand-btn.expanded {
	transform: rotate(90deg);
	background: rgba(0, 0, 0, 0.08);
}

.expand-icon {
	font-size: 24rpx;
	color: #6b7280;
	font-weight: 600;
}

.card-content {
	padding: 40rpx;
	padding-top: 0;
}

/* 表单样式 */
.input-group {
	margin-bottom: 40rpx;
}

.input-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.input-label {
	font-size: 28rpx;
	font-weight: 500;
	color: #111827;
	letter-spacing: -0.3rpx;
}

.modern-input, .modern-textarea {
	width: 100%;
	height: 96rpx;
	padding: 0 28rpx;
	background: #fafbfc;
	border: 1rpx solid #e5e7eb;
	border-radius: 12rpx;
	font-size: 28rpx;
	line-height: 96rpx;
	color: #111827;
	transition: all 0.2s ease;
	box-sizing: border-box;
}

.modern-input:focus, .modern-textarea:focus {
	outline: none;
	border-color: #111827;
	background: #ffffff;
	box-shadow: 0 0 0 4rpx rgba(17, 24, 39, 0.04);
}

.modern-input::placeholder, .modern-textarea::placeholder {
	color: #9ca3af;
	font-size: 28rpx;
}

.modern-textarea {
	height: auto;
	min-height: 160rpx;
	line-height: 1.6;
	padding: 28rpx;
	resize: none;
}

/* 媒体网格 */
.media-grid, .honor-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.media-item, .upload-item {
	width: calc((100% - 40rpx) / 3);
	aspect-ratio: 1;
	border-radius: 16rpx;
	position: relative;
	overflow: hidden;
}

.media-preview {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.upload-item {
	background: rgba(249, 250, 251, 0.8);
	border: 3rpx dashed rgba(156, 163, 175, 0.6);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	transition: all 0.3s ease;
}

.upload-item:active {
	border-color: #6366f1;
	background: rgba(99, 102, 241, 0.05);
	transform: scale(0.98);
}

.upload-icon {
	font-size: 48rpx;
	color: #9ca3af;
	font-weight: 300;
}

.upload-text {
	font-size: 24rpx;
	color: #6b7280;
	font-weight: 500;
}

.delete-btn {
	position: absolute;
	top: 8rpx;
	right: 8rpx;
	width: 48rpx;
	height: 48rpx;
	background: rgba(239, 68, 68, 0.9);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.delete-btn:active {
	transform: scale(0.9);
}

.delete-icon {
	color: #ffffff;
	font-size: 28rpx;
	font-weight: bold;
}

/* 荣誉样式 */
.honor-item, .add-honor-item {
	width: calc((100% - 20rpx) / 2);
	aspect-ratio: 1;
	border-radius: 16rpx;
	position: relative;
	overflow: hidden;
}

.honor-image {
	width: 100%;
	height: 70%;
	object-fit: cover;
}

.honor-info {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
	padding: 16rpx;
	padding-top: 32rpx;
}

.honor-title {
	color: #ffffff;
	font-size: 24rpx;
	font-weight: 600;
	line-height: 1.3;
}

.add-honor-item {
	background: rgba(249, 250, 251, 0.8);
	border: 3rpx dashed rgba(156, 163, 175, 0.6);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	transition: all 0.3s ease;
}

.add-honor-item:active {
	border-color: #6366f1;
	background: rgba(99, 102, 241, 0.05);
	transform: scale(0.98);
}

.add-icon {
	font-size: 60rpx;
	color: #9ca3af;
	font-weight: 300;
}

.add-text {
	font-size: 26rpx;
	color: #6b7280;
	font-weight: 500;
}

/* 弹窗样式 */
.honor-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 2000;
	padding: 40rpx;
}

.modal-content {
	background: #ffffff;
	border-radius: 24rpx;
	width: 100%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
	padding: 32rpx;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.modal-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #1f2937;
}

.close-btn {
	width: 60rpx;
	height: 60rpx;
	background: rgba(239, 68, 68, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.close-icon {
	color: #ef4444;
	font-size: 32rpx;
	font-weight: bold;
}

.modal-body {
	padding: 32rpx;
}

.image-upload {
	width: 100%;
	height: 200rpx;
	border-radius: 16rpx;
	overflow: hidden;
	position: relative;
}

.preview-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.upload-placeholder {
	width: 100%;
	height: 100%;
	background: rgba(249, 250, 251, 0.8);
	border: 3rpx dashed rgba(156, 163, 175, 0.6);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
}

.modal-footer {
	padding: 32rpx;
	border-top: 1rpx solid rgba(0, 0, 0, 0.05);
	display: flex;
	gap: 20rpx;
}

.cancel-btn, .confirm-btn {
	flex: 1;
	padding: 24rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.cancel-btn {
	background: rgba(107, 114, 128, 0.1);
}

.cancel-btn:active {
	background: rgba(107, 114, 128, 0.2);
}

.cancel-text {
	color: #6b7280;
	font-size: 28rpx;
	font-weight: 600;
}

.confirm-btn {
	background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
}

.confirm-btn:active {
	transform: translateY(2rpx);
}

.confirm-text {
	color: #ffffff;
	font-size: 28rpx;
	font-weight: 600;
}

/* 底部操作栏 */
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	padding: 32rpx;
	padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
	border-top: 1rpx solid rgba(0, 0, 0, 0.06);
	z-index: 1000;
}

.save-btn {
	width: 100%;
	padding: 36rpx;
	background: #111827;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	transition: all 0.2s ease;
}

.save-btn:active {
	transform: translateY(1rpx);
	box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.15);
	background: #000000;
}

.save-text {
	color: #ffffff;
	font-size: 32rpx;
	font-weight: 600;
	letter-spacing: -0.5rpx;
}
</style>