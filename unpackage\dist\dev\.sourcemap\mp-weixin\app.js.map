{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script>\n\timport userService from './services/userService.js';\n\n\texport default {\n\t\tasync onLaunch() {\n\t\t\t// 初始化用户服务\n\t\t\ttry {\n\t\t\t\tawait userService.init();\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('用户服务初始化失败:', error);\n\t\t\t}\n\n\t\t\t// 初始化用户信息（不检查登录状态）\n\t\t\tthis.initUserInfo();\n\n\t\t\t// 同步会员状态\n\t\t\tthis.syncMemberStatus();\n\t\t},\n\t\tonShow: function() {\n\t\t\t// 应用从后台切换到前台时，同步会员状态\n\t\t\tthis.syncMemberStatus();\n\t\t},\n\t\tonHide: function() {\n\t\t},\n\t\tmethods: {\n\t\t\t// 同步会员状态\n\t\t\tasync syncMemberStatus() {\n\t\t\t\ttry {\n\t\t\t\t\t// 静默同步，不显示loading\n\t\t\t\t\tawait userService.syncMemberStatus();\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('会员状态同步失败:', error);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tinitUserInfo() {\n\t\t\t\t// 检查是否有保存的用户信息\n\t\t\t\tlet userInfo = uni.getStorageSync('userInfo');\n\t\t\t\tif (!userInfo) {\n\t\t\t\t\t// 设置默认用户信息\n\t\t\t\t\tconst defaultUserInfo = {\n\t\t\t\t\t\tavatar: '/static/default-avatar.png',\n\t\t\t\t\t\tname: '请设置姓名',\n\t\t\t\t\t\tposition: '请设置职位',\n\t\t\t\t\t\tcompany: '请设置公司',\n\t\t\t\t\t\tphone: '',\n\t\t\t\t\t\temail: '',\n\t\t\t\t\t\twechat: '',\n\t\t\t\t\t\taddress: '',\n\t\t\t\t\t\tdescription: ''\n\t\t\t\t\t};\n\t\t\t\t\tuni.setStorageSync('userInfo', defaultUserInfo);\n\t\t\t\t\tuserInfo = defaultUserInfo;\n\t\t\t\t}\n\n\t\t\t\t// 确保用户信息完整性\n\t\t\t\tconst requiredFields = ['avatar', 'name', 'position', 'company', 'phone', 'email', 'wechat', 'address', 'description'];\n\t\t\t\tlet needUpdate = false;\n\n\t\t\t\trequiredFields.forEach(field => {\n\t\t\t\t\tif (userInfo[field] === undefined) {\n\t\t\t\t\t\tuserInfo[field] = field === 'avatar' ? '/static/default-avatar.png' :\n\t\t\t\t\t\t\t\t\t\t field === 'name' ? '请设置姓名' :\n\t\t\t\t\t\t\t\t\t\t field === 'position' ? '请设置职位' :\n\t\t\t\t\t\t\t\t\t\t field === 'company' ? '请设置公司' : '';\n\t\t\t\t\t\tneedUpdate = true;\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\tif (needUpdate) {\n\t\t\t\t\tuni.setStorageSync('userInfo', userInfo);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t/* 全局样式 */\n\tpage {\n\t\tbackground-color: #f5f5f5;\n\t\tfont-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;\n\t}\n\n\t/* 通用按钮样式 */\n\t.btn {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder: none;\n\t\tborder-radius: 8rpx;\n\t\tfont-size: 28rpx;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.btn:active {\n\t\ttransform: scale(0.98);\n\t\topacity: 0.8;\n\t}\n\n\t/* 主色调按钮 */\n\t.btn-primary {\n\t\tbackground-color: #4A90E2;\n\t\tcolor: white;\n\t}\n\n\t/* 次要按钮 */\n\t.btn-secondary {\n\t\tbackground-color: #f8f9fa;\n\t\tcolor: #333;\n\t\tborder: 1rpx solid #e0e0e0;\n\t}\n\n\t/* 卡片样式 */\n\t.card {\n\t\tbackground: white;\n\t\tborder-radius: 15rpx;\n\t\tpadding: 30rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t/* 文本省略 */\n\t.text-ellipsis {\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t}\n\n\t.text-ellipsis-2 {\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\tdisplay: -webkit-box;\n\t\t-webkit-line-clamp: 2;\n\t\t-webkit-box-orient: vertical;\n\t}\n\n\t/* 居中布局 */\n\t.flex-center {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.flex-between {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t}\n\n\t/* 间距工具类 */\n\t.mt-10 { margin-top: 10rpx; }\n\t.mt-20 { margin-top: 20rpx; }\n\t.mt-30 { margin-top: 30rpx; }\n\t.mb-10 { margin-bottom: 10rpx; }\n\t.mb-20 { margin-bottom: 20rpx; }\n\t.mb-30 { margin-bottom: 30rpx; }\n\t.ml-10 { margin-left: 10rpx; }\n\t.ml-20 { margin-left: 20rpx; }\n\t.mr-10 { margin-right: 10rpx; }\n\t.mr-20 { margin-right: 20rpx; }\n\n\t.p-10 { padding: 10rpx; }\n\t.p-20 { padding: 20rpx; }\n\t.p-30 { padding: 30rpx; }\n\n\t/* 字体大小 */\n\t.text-xs { font-size: 20rpx; }\n\t.text-sm { font-size: 24rpx; }\n\t.text-base { font-size: 28rpx; }\n\t.text-lg { font-size: 32rpx; }\n\t.text-xl { font-size: 36rpx; }\n\t.text-2xl { font-size: 40rpx; }\n\n\t/* 字体颜色 */\n\t.text-primary { color: #4A90E2; }\n\t.text-secondary { color: #666; }\n\t.text-muted { color: #999; }\n\t.text-danger { color: #e74c3c; }\n\t.text-success { color: #27ae60; }\n\n\t/* 字体粗细 */\n\t.font-bold { font-weight: bold; }\n\t.font-normal { font-weight: normal; }\n</style>\n", "import App from './App'\nimport appService from './services/appService'\nimport NavBar from './components/NavBar.vue'\nimport SwitchItem from './components/SwitchItem.vue'\nimport http from './utils/request'\nimport { API_BASE_URL } from './utils/config'\n\n// API可用性检查函数\nfunction checkApiAvailability() {\n  console.log('正在检查API可用性...');\n  console.log('API基础URL:', API_BASE_URL);\n  \n  // 测试API连接\n  uni.request({\n    url: API_BASE_URL + '/test/index',\n    method: 'GET',\n    timeout: 5000,\n    success: (res) => {\n      if (res.statusCode === 200) {\n        console.log('API连接测试成功!');\n      } else {\n        console.warn('API连接测试失败! 状态码:', res.statusCode);\n      }\n    },\n    fail: (err) => {\n      console.error('API连接测试失败!', err);\n    }\n  });\n}\n\n// #ifndef VUE3\nimport Vue from 'vue'\nimport './uni.promisify.adaptor'\n\n// 注册全局组件\nVue.component('NavBar', NavBar)\nVue.component('SwitchItem', SwitchItem)\n\n// 添加全局属性\nVue.prototype.$app = appService\nVue.prototype.$user = appService.getUserService()\nVue.prototype.$company = appService.getCompanyService()\n\n// 添加全局错误处理\nVue.config.errorHandler = function(err, vm, info) {\n  // 检查是否是注册相关的错误\n  if (err && err.message && (\n    err.message.includes('注册请求') || \n    err.message.includes('系统发生错误') ||\n    err.message.includes('服务器内部错误')\n  )) {\n    // 对于注册相关错误，只输出简短信息，不输出完整堆栈\n    console.log('注册流程中的预期错误:', err.message);\n  } else {\n    // 其他错误正常输出\n    console.error('应用错误:', err);\n    console.error('错误信息:', info);\n  }\n};\n\n// 拦截控制台错误\nconst originalConsoleError = console.error;\nconsole.error = function(...args) {\n  // 检查是否包含注册相关的错误信息\n  const errorString = args.join(' ');\n  if (errorString.includes('/user/register') || \n      errorString.includes('系统发生错误') || \n      errorString.includes('服务器内部错误')) {\n    // 对于注册相关错误，改为使用console.log\n    console.log('已拦截的错误日志:', ...args.map(arg => \n      typeof arg === 'string' ? arg.replace('错误', '信息') : arg\n    ));\n  } else {\n    // 其他错误正常输出\n    originalConsoleError.apply(console, args);\n  }\n};\n\nVue.config.productionTip = false\nApp.mpType = 'app'\n\n// 检查API可用性\ncheckApiAvailability();\n\n// 初始化应用\nappService.init().then(() => {\n  console.log('应用初始化成功，开始挂载Vue实例')\n  const app = new Vue({\n    ...App\n  })\n  app.$mount()\n}).catch(error => {\n  console.error('应用初始化失败:', error)\n  // 即使初始化失败，也尝试挂载应用\nconst app = new Vue({\n  ...App\n})\napp.$mount()\n})\n// #endif\n\n// #ifdef VUE3\nimport { createSSRApp } from 'vue'\n\nexport function createApp() {\n  const app = createSSRApp(App)\n  \n  // 注册全局组件\n  app.component('NavBar', NavBar)\n  app.component('SwitchItem', SwitchItem)\n  \n  // 添加全局属性\n  app.config.globalProperties.$app = appService\n  app.config.globalProperties.$user = appService.getUserService()\n  app.config.globalProperties.$company = appService.getCompanyService()\n  \n  // 添加全局错误处理\n  app.config.errorHandler = function(err, vm, info) {\n    // 检查是否是注册相关的错误\n    if (err && err.message && (\n      err.message.includes('注册请求') || \n      err.message.includes('系统发生错误') ||\n      err.message.includes('服务器内部错误')\n    )) {\n      // 对于注册相关错误，只输出简短信息，不输出完整堆栈\n      console.log('注册流程中的预期错误:', err.message);\n    } else {\n      // 其他错误正常输出\n      console.error('应用错误:', err);\n      console.error('错误信息:', info);\n    }\n  };\n  \n  // 检查API可用性\n  checkApiAvailability();\n  \n  // 初始化应用（在实际挂载前）\n  appService.init().catch(error => {\n    console.error('应用初始化失败:', error)\n  })\n  \n  return {\n    app\n  }\n}\n// #endif"], "names": ["userService", "uni", "API_BASE_URL", "createSSRApp", "App", "appService"], "mappings": ";;;;;;;;;;;;;;;;;;;AAGC,MAAK,YAAU;AAAA,EACd,MAAM,WAAW;AAEhB,QAAI;AACH,YAAMA,qBAAAA,YAAY;IACjB,SAAO,OAAO;AACfC,oBAAc,MAAA,MAAA,SAAA,iBAAA,cAAc,KAAK;AAAA,IAClC;AAGA,SAAK,aAAY;AAGjB,SAAK,iBAAgB;AAAA,EACrB;AAAA,EACD,QAAQ,WAAW;AAElB,SAAK,iBAAgB;AAAA,EACrB;AAAA,EACD,QAAQ,WAAW;AAAA,EAClB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,MAAM,mBAAmB;AACxB,UAAI;AAEH,cAAMD,qBAAAA,YAAY;MACjB,SAAO,OAAO;AACfC,sBAAA,MAAA,MAAA,SAAA,iBAAc,aAAa,KAAK;AAAA,MACjC;AAAA,IACA;AAAA,IAED,eAAe;AAEd,UAAI,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC5C,UAAI,CAAC,UAAU;AAEd,cAAM,kBAAkB;AAAA,UACvB,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS;AAAA,UACT,OAAO;AAAA,UACP,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,aAAa;AAAA;AAEdA,sBAAAA,MAAI,eAAe,YAAY,eAAe;AAC9C,mBAAW;AAAA,MACZ;AAGA,YAAM,iBAAiB,CAAC,UAAU,QAAQ,YAAY,WAAW,SAAS,SAAS,UAAU,WAAW,aAAa;AACrH,UAAI,aAAa;AAEjB,qBAAe,QAAQ,WAAS;AAC/B,YAAI,SAAS,KAAK,MAAM,QAAW;AAClC,mBAAS,KAAK,IAAI,UAAU,WAAW,+BAClC,UAAU,SAAS,UACnB,UAAU,aAAa,UACvB,UAAU,YAAY,UAAU;AACrC,uBAAa;AAAA,QACd;AAAA,MACD,CAAC;AAED,UAAI,YAAY;AACfA,sBAAAA,MAAI,eAAe,YAAY,QAAQ;AAAA,MACxC;AAAA,IACD;AAAA,EACD;AACD;ACxED,MAAM,SAAS,MAAW;AAC1B,MAAM,aAAa,MAAW;AAK9B,SAAS,uBAAuB;AAC9BA,gBAAAA,MAAY,MAAA,OAAA,iBAAA,eAAe;AAC3BA,gBAAA,MAAA,MAAA,OAAA,iBAAY,aAAaC,aAAY,YAAA;AAGrCD,gBAAAA,MAAI,QAAQ;AAAA,IACV,KAAKC,aAAY,eAAG;AAAA,IACpB,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,SAAS,CAAC,QAAQ;AAChB,UAAI,IAAI,eAAe,KAAK;AAC1BD,sBAAAA,oCAAY,YAAY;AAAA,MAChC,OAAa;AACLA,sBAAa,MAAA,MAAA,QAAA,iBAAA,mBAAmB,IAAI,UAAU;AAAA,MAC/C;AAAA,IACF;AAAA,IACD,MAAM,CAAC,QAAQ;AACbA,0DAAc,cAAc,GAAG;AAAA,IAChC;AAAA,EACL,CAAG;AACH;AA4EO,SAAS,YAAY;AAC1B,QAAM,MAAME,cAAY,aAACC,SAAG;AAG5B,MAAI,UAAU,UAAU,MAAM;AAC9B,MAAI,UAAU,cAAc,UAAU;AAGtC,MAAI,OAAO,iBAAiB,OAAOC,oBAAU;AAC7C,MAAI,OAAO,iBAAiB,QAAQA,oBAAAA,WAAW,eAAgB;AAC/D,MAAI,OAAO,iBAAiB,WAAWA,oBAAAA,WAAW,kBAAmB;AAGrE,MAAI,OAAO,eAAe,SAAS,KAAK,IAAI,MAAM;AAEhD,QAAI,OAAO,IAAI,YACb,IAAI,QAAQ,SAAS,MAAM,KAC3B,IAAI,QAAQ,SAAS,QAAQ,KAC7B,IAAI,QAAQ,SAAS,SAAS,IAC7B;AAEDJ,oBAAY,MAAA,MAAA,OAAA,kBAAA,eAAe,IAAI,OAAO;AAAA,IAC5C,OAAW;AAELA,oBAAA,MAAA,MAAA,SAAA,kBAAc,SAAS,GAAG;AAC1BA,oBAAc,MAAA,MAAA,SAAA,kBAAA,SAAS,IAAI;AAAA,IAC5B;AAAA,EACL;AAGE;AAGAI,sBAAAA,WAAW,KAAI,EAAG,MAAM,WAAS;AAC/BJ,kBAAAA,MAAA,MAAA,SAAA,kBAAc,YAAY,KAAK;AAAA,EACnC,CAAG;AAED,SAAO;AAAA,IACL;AAAA,EACD;AACH;;;"}