# 前后端状态管理文档

## 概述

本文档详细记录了电子名片小程序的前后端状态管理、Token认证机制和数据同步实现方案。

## 1. Token认证机制

### 1.1 数据库设计

Token信息直接存储在 `bc_users` 表中，避免了复杂的关联查询：

```sql
-- Token相关字段
ALTER TABLE `bc_users`
ADD COLUMN `access_token` varchar(64) DEFAULT NULL COMMENT '访问令牌',
ADD COLUMN `token_expires_at` datetime DEFAULT NULL COMMENT '令牌过期时间',
ADD COLUMN `last_login_at` datetime DEFAULT NULL COMMENT '最后登录时间',
ADD COLUMN `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP';

-- 个性定制相关字段（使用JSON存储）
ALTER TABLE `bc_users`
ADD COLUMN `custom_style` json DEFAULT NULL COMMENT '个性定制样式JSON数据';

-- JSON结构示例：
{
  "backgroundType": "image",
  "backgroundColor": "#ffffff",
  "backgroundImage": "https://example.com/bg.jpg",
  "gradientIndex": 0,
  "textColor": "#333333",
  "borderRadiusIndex": 1,
  "cardStyleIndex": 0
}
```

### 1.2 Token生成策略

**后端实现** (`server/app/common/model/BusinessUser.php`)：

```php
public function generateAccessToken($ipAddress = null): string
{
    // 生成64位随机令牌
    $token = bin2hex(random_bytes(32));

    // 设置7天过期时间
    $expiresAt = date('Y-m-d H:i:s', time() + 7 * 24 * 3600);

    // 更新用户记录
    $this->save([
        'access_token' => $token,
        'token_expires_at' => $expiresAt,
        'last_login_at' => date('Y-m-d H:i:s'),
        'last_login_ip' => $ipAddress
    ]);

    return $token;
}
```

### 1.3 Token验证机制

**验证流程**：
1. 从请求头获取 `Authorization: Bearer {token}`
2. 查询数据库验证token有效性
3. 检查过期时间
4. 更新最后登录时间

```php
public static function validateAccessToken(string $token): ?self
{
    $user = self::where('access_token', $token)
                ->where('token_expires_at', '>', date('Y-m-d H:i:s'))
                ->find();

    if ($user) {
        // 更新最后登录时间
        $user->save(['last_login_at' => date('Y-m-d H:i:s')]);
    }

    return $user;
}
```

## 2. 前端状态管理

### 2.1 存储架构

**本地存储键值**：
- `business_card_token`: 用户认证令牌
- `userInfo`: 用户基本信息和名片数据（包含个性定制数据）
- `isLoggedIn`: 登录状态标识

**userInfo数据结构**：
```javascript
{
  // 基础信息
  id, name, phone, email, wechat, address, company, position, description, avatar,

  // 会员信息
  member_level, member_expire_time,

  // 个性定制数据
  customStyle: {
    backgroundType: 'color|gradient|image',
    backgroundColor: '#ffffff',
    backgroundImage: '',
    gradientIndex: 0,
    textColor: '#333333',
    borderRadiusIndex: 1,
    cardStyleIndex: 0
  }
}

### 2.2 服务层设计

**userService.js** - 用户认证服务：
```javascript
class UserService {
    async login(phone, password) {
        // 登录逻辑
        // 存储token和用户状态
    }

    isLoggedIn() {
        // 检查登录状态
    }

    logout() {
        // 清除本地状态
    }
}
```

**userInfoService.js** - 用户信息同步服务：
```javascript
class UserInfoService {
    async getUserInfo() {
        // 从服务器获取用户信息
    }

    async updateUserInfo(userInfo) {
        // 同步用户信息到服务器
    }
}
```

## 3. 数据同步机制

### 3.1 同步策略

**双向同步原则**：
- 前端优先：用户编辑时立即更新本地存储
- 后端同步：联系信息变更时自动同步到服务器
- 冲突处理：服务器数据为准，本地样式设置保留

### 3.2 同步时机

1. **应用启动时**：
   - 检查登录状态
   - 如已登录，从服务器拉取最新数据
   - 合并本地样式设置

2. **编辑保存时**：
   - 立即保存到本地存储
   - 如果是联系信息，同步到服务器
   - 样式设置仅保存本地

3. **登录成功后**：
   - 从服务器获取用户完整信息
   - 更新本地存储

### 3.3 数据合并策略

```javascript
// 保留本地样式，更新服务器数据
const mergedData = {
    // 服务器数据（联系信息）
    name: serverData.name,
    phone: serverData.phone,
    email: serverData.email,
    company: serverData.company,
    position: serverData.position,
    wechat: serverData.wechat,
    address: serverData.address,

    // 保留本地样式设置
    customStyle: this.form.customStyle,
    avatar: this.form.avatar
};
```

## 4. API接口设计

### 4.1 认证接口

**POST /api/user/login**
```json
{
    "phone": "手机号",
    "password": "密码"
}
```

**响应**：
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "token": "访问令牌",
        "user": {用户信息}
    }
}
```

### 4.2 用户信息接口

**GET /api/user/read**
- Headers: `Authorization: Bearer {token}`
- 返回用户完整信息

**POST /api/user/save**
- Headers: `Authorization: Bearer {token}`
- Body: 用户信息更新数据（包含个性定制数据）

**请求示例**：
```json
{
  "name": "张三",
  "phone": "13800138000",
  "email": "<EMAIL>",
  "wechat": "zhangsan_wx",
  "address": "北京市朝阳区",
  "company": "科技公司",
  "position": "产品经理",
  "description": "专注产品设计与用户体验",
  "customStyle": {
    "backgroundType": "gradient",
    "backgroundColor": "#ff6b6b",
    "backgroundImage": "",
    "gradientIndex": 2,
    "textColor": "#ffffff",
    "borderRadiusIndex": 2,
    "cardStyleIndex": 5
  }
}
```

**字段映射**：
前端 `customStyle` 对象字段会自动映射到数据库字段：
- `backgroundType` → `background_type`
- `backgroundColor` → `background_color`
- `backgroundImage` → `background_image`
- `gradientIndex` → `gradient_index`
- `textColor` → `text_color`
- `borderRadiusIndex` → `border_radius_index`
- `cardStyleIndex` → `card_style_index`

## 5. 后端管理功能

### 5.1 Token管理

管理员可以在后台查看和管理用户Token：

**功能特性**：
- 查看Token状态（有效/过期）
- 显示Token过期时间
- 查看最后登录时间和IP
- 一键撤销用户Token

**实现位置**：
- 控制器：`server/app/admin/controller/BusinessUser.php`
- 模板：`server/app/admin/view/business_user/form.html`

### 5.2 用户数据管理

**表单字段**：
- 基本信息：姓名、手机、邮箱
- 职业信息：公司、职位
- 联系方式：微信号、地址
- 会员信息：等级、过期时间
- Token信息：状态、过期时间、登录记录
- 个性定制：名片风格、背景类型、颜色配置、圆角设置等（只读显示）

## 6. 安全考虑

### 6.1 Token安全

1. **随机性**：使用 `random_bytes(32)` 生成64位随机token
2. **过期机制**：7天自动过期
3. **单设备登录**：新登录会覆盖旧token
4. **IP记录**：记录登录IP便于安全审计

### 6.2 数据传输安全

1. **HTTPS传输**：所有API请求使用HTTPS
2. **请求头认证**：Token通过Authorization头传输
3. **数据验证**：服务端严格验证所有输入数据

## 7. 错误处理

### 7.1 Token失效处理

```javascript
// 前端自动处理token失效
if (response.statusCode === 401) {
    // 清除本地状态
    userService.logout();
    // 跳转到登录页
    uni.navigateTo({url: '/pages/auth/login'});
}
```

### 7.2 网络异常处理

```javascript
// 网络请求失败时的降级策略
try {
    const result = await userInfoService.updateUserInfo(data);
    if (!result.success) {
        // 显示错误提示，但不影响本地使用
        uni.showToast({title: '同步失败，数据已保存到本地'});
    }
} catch (error) {
    // 静默处理，确保用户体验
    console.error('同步异常:', error);
}
```

## 8. 性能优化

### 8.1 缓存策略

1. **本地缓存**：用户信息本地存储，减少网络请求
2. **增量更新**：只同步变更的字段
3. **延迟同步**：非关键数据延迟同步

### 8.2 数据库优化

1. **索引优化**：为access_token字段添加索引
2. **定期清理**：定期清理过期token记录
3. **查询优化**：避免不必要的关联查询

## 9. 部署注意事项

### 9.1 数据库迁移

执行SQL脚本添加token相关字段：
```bash
# 在数据库中执行
source /path/to/database/migrations/database.sql
```

### 9.2 缓存清理

部署后清理ThinkAdmin缓存：
```bash
# 删除缓存目录
rm -rf server/runtime/cache/*
```

## 10. 监控和维护

### 10.1 日志监控

- 登录失败次数监控
- Token使用频率统计
- 异常请求记录

### 10.2 定期维护

- 清理过期token记录
- 用户活跃度统计
- 系统性能监控

## 11. 实际应用场景

### 11.1 用户登录流程

1. 用户在小程序输入手机号和密码
2. 前端调用 `/api/user/login` 接口
3. 后端验证用户信息，生成token
4. 前端存储token和用户信息到本地
5. 设置登录状态为true

### 11.2 数据编辑同步流程

**联系信息和个性定制统一同步**：
1. 用户在编辑页面修改联系信息或个性定制
2. 点击保存时立即更新本地存储
3. 检查用户登录状态
4. 如果已登录，调用 `/api/user/save` 接口同步数据
5. 后端自动处理字段映射并更新数据库
6. 返回同步结果给前端
7. 成功后重新从服务器加载最新数据确保一致性

### 11.3 应用启动数据加载

1. App.vue onLaunch 检查登录状态
2. 如果已登录，从服务器获取最新数据
3. 合并服务器数据和本地样式设置
4. 更新本地存储和页面显示

## 12. 故障排查指南

### 12.1 Token相关问题

**问题**：用户提示未登录
**排查步骤**：
1. 检查本地存储中的token是否存在
2. 验证token格式是否正确
3. 检查token是否过期
4. 查看服务器日志确认验证过程

**问题**：数据同步失败
**排查步骤**：
1. 检查网络连接状态
2. 验证API接口是否正常
3. 查看请求头中的Authorization字段
4. 检查服务器端的错误日志

### 12.2 数据一致性问题

**问题**：本地数据与服务器不一致
**解决方案**：
1. 强制从服务器重新拉取数据
2. 清除本地缓存重新登录
3. 检查数据合并逻辑是否正确

## 13. 个性定制功能部署指南

### 13.1 数据库迁移

**方法一：使用迁移脚本（推荐）**
1. 访问 `https://你的域名/migrate_custom_style.php`
2. 脚本会自动检查并添加个性定制字段
3. 显示迁移结果和字段验证

**方法二：手动执行SQL**
```sql
-- 在phpMyAdmin或数据库管理工具中执行
ALTER TABLE `bc_users`
ADD COLUMN `background_type` varchar(20) DEFAULT 'color' COMMENT '背景类型：color, gradient, image',
ADD COLUMN `background_color` varchar(20) DEFAULT '#ffffff' COMMENT '背景颜色',
ADD COLUMN `background_image` varchar(255) DEFAULT '' COMMENT '背景图片URL',
ADD COLUMN `gradient_index` int(11) DEFAULT 0 COMMENT '渐变索引',
ADD COLUMN `text_color` varchar(20) DEFAULT '#333333' COMMENT '文字颜色',
ADD COLUMN `border_radius_index` int(11) DEFAULT 1 COMMENT '圆角索引：0=小，1=中，2=大',
ADD COLUMN `card_style_index` int(11) DEFAULT 0 COMMENT '名片风格索引';
```

### 13.2 后端缓存清理

迁移完成后必须清理后端缓存：
```bash
# 删除缓存目录
rm -rf server/runtime/cache/*
# 或在宝塔面板中删除 runtime/cache 目录
```

### 13.3 功能测试

1. **前端测试**：
   - 进入小程序「工作台」→「个性定制测试」
   - 测试数据加载、保存和同步功能
   - 验证登录/未登录状态下的不同行为

2. **后端验证**：
   - 登录后台管理系统
   - 进入「用户管理」→「用户列表」
   - 编辑用户查看个性定制信息显示

3. **数据一致性检查**：
   - 在小程序中修改名片样式
   - 检查后台用户信息是否同步更新
   - 验证字段映射是否正确

### 13.4 清理测试文件

部署完成后可删除测试相关文件：
```bash
# 删除测试页面
rm -f pages/test/custom-style-test.vue
# 删除迁移脚本（可选）
rm -f server/public/migrate_custom_style.php
```

## 14. 文件上传状态管理

### 14.1 头像上传机制

**前端实现** (`pages/card/edit.vue`)：
- **微信头像选择**：使用 `onChooseAvatar` 事件，静默上传
- **相册选择**：使用 `uploadAvatarSilently` 方法，避免重复loading
- **数据同步**：上传成功后立即更新表单和本地存储

**后端实现** (`server/app/api/controller/Upload.php`)：
```php
// 头像上传逻辑
public function avatar(Request $request)
{
    // 1. 验证文件类型和大小
    // 2. 删除旧头像文件
    // 3. 使用时间戳确保文件唯一性
    $timestamp = time();
    $saveFileName = "avatars/{$date}/user_{$userId}_{$timestamp}.{$ext}";
    // 4. 保存到七牛云存储
    // 5. 更新数据库
}
```

### 14.2 背景图片上传机制

**文件命名规则**：
```
backgrounds/YYYY/MM/DD/user_{用户ID}_{时间戳}.{扩展名}
```

**上传流程**：
1. 用户选择图片
2. 删除旧背景图片文件
3. 上传新文件到七牛云
4. 更新 `custom_style` JSON字段
5. 前端同步更新预览

### 14.3 跨设备数据同步

**数据加载优先级**：
1. **优先从服务器加载**：登录用户首次打开页面
2. **本地缓存备用**：服务器加载失败时使用
3. **自动同步**：应用启动、页面显示时触发

**实现机制** (`pages/card/edit.vue`)：
```javascript
async initializeUserData() {
  if (userService.isLoggedIn()) {
    try {
      // 优先从服务器加载完整数据
      const result = await userInfoService.getCurrentUserInfo();
      if (result.success) {
        // 包含 customStyle 的完整数据
        this.form = { ...defaultForm, ...serverData };
      }
    } catch (error) {
      // 失败时使用本地缓存
      this.loadFromLocalStorage(defaultForm);
    }
  }
}
```

## 15. 会员状态自动同步

### 15.1 同步时机

**应用级别**：
- `App.vue` 的 `onLaunch` 和 `onShow`
- 应用启动和从后台切换到前台时

**页面级别**：
- 主要页面的 `onShow` 事件
- 确保页面显示时状态最新

### 15.2 同步实现

**服务层** (`services/userService.js`)：
```javascript
async syncMemberStatus() {
  if (!this.isLoggedIn()) return false;

  try {
    // 静默同步，不显示loading
    await this.fetchUserInfo();
    return true;
  } catch (error) {
    console.error('会员状态同步失败:', error);
    return false;
  }
}
```

**页面调用**：
```javascript
onShow() {
  // 页面显示时同步会员状态
  this.syncMemberStatus();
}
```

## 16. 错误处理和容错机制

### 16.1 网络错误处理

- **超时重试**：关键接口自动重试机制
- **降级策略**：服务器不可用时使用本地缓存
- **用户提示**：友好的错误信息展示

### 16.2 数据一致性保证

- **乐观更新**：前端先更新UI，后同步服务器
- **冲突解决**：服务器数据优先原则
- **状态回滚**：操作失败时恢复原状态

## 17. 性能优化策略

### 17.1 数据缓存

- **本地存储**：用户信息、会员状态缓存
- **图片缓存**：头像和背景图片本地缓存
- **接口缓存**：非实时数据的短期缓存

### 17.2 加载优化

- **懒加载**：非关键数据延迟加载
- **预加载**：预测用户行为，提前加载数据
- **静默同步**：后台静默更新数据

---

**文档版本**: v1.0
**最后更新**: 2025-01-26
**维护人员**: 开发团队