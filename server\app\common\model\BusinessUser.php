<?php
declare(strict_types=1);

namespace app\common\model;

use think\admin\Model;

/**
 * 电子名片用户模型
 */
class BusinessUser extends Model
{
    protected $name = 'bc_users';
    
    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'phone'       => 'string',
        'password'    => 'string',
        'openid'      => 'string',
        'unionid'     => 'string',
        'session_key' => 'string',
        'wechat_nickname' => 'string',
        'gender'      => 'int',
        'city'        => 'string',
        'province'    => 'string',
        'country'     => 'string',
        'name'        => 'string',
        'position'    => 'string',
        'company'     => 'string',
        'email'       => 'string',
        'wechat'      => 'string',
        'address'     => 'string',
        'avatar'      => 'string',
        'description' => 'text',
        'tags'        => 'text',
        'achievements'=> 'text',
        'education'   => 'string',
        // 个人介绍显示控制开关
        'showProfileSection' => 'int',
        'showDescription' => 'int',
        'showTags'    => 'int',
        'showAchievements' => 'int',
        'showEducation' => 'int',
        'member_level'=> 'int',
        'member_expire_time' => 'datetime',
        'access_token' => 'string',
        'token_expires_at' => 'datetime',
        'last_login_at' => 'datetime',
        'last_login_ip' => 'string',
        'background_type' => 'string',
        'background_color' => 'string',
        'background_image' => 'string',
        'gradient_index' => 'int',
        'text_color' => 'string',
        'border_radius_index' => 'int',
        'card_style_index' => 'int',
        'status'      => 'int',
        'create_time' => 'datetime',
        'update_time' => 'datetime',
        'deleted_at'  => 'datetime',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $deleteTime = 'deleted_at';

    // 字段类型转换
    protected $type = [
        'gender' => 'integer',
        'member_level' => 'integer',
        'status' => 'integer',
        'create_time' => 'datetime',
        'update_time' => 'datetime',
        'member_expire_time' => 'datetime',
        // 显示控制开关字段
        'showProfileSection' => 'integer',
        'showDescription' => 'integer',
        'showTags' => 'integer',
        'showAchievements' => 'integer',
        'showEducation' => 'integer',
    ];

    // 隐藏字段
    protected $hidden = ['password', 'session_key', 'deleted_at'];

    // 会员等级
    const MEMBER_BASIC = 0;      // 基础版
    const MEMBER_PROFESSIONAL = 1; // 专业版
    const MEMBER_ENTERPRISE = 2;   // 企业版

    // 状态
    const STATUS_DISABLED = 0;   // 禁用
    const STATUS_ENABLED = 1;    // 启用

    /**
     * 获取会员等级文本
     */
    public function getMemberLevelTextAttr($value, $data)
    {
        $levels = [
            self::MEMBER_BASIC => '基础版',
            self::MEMBER_PROFESSIONAL => '专业版',
            self::MEMBER_ENTERPRISE => '企业版',
        ];
        return $levels[$data['member_level']] ?? '未知';
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        return $data['status'] == self::STATUS_ENABLED ? '正常' : '禁用';
    }

    /**
     * 密码加密
     */
    public function setPasswordAttr($value)
    {
        return password_hash($value, PASSWORD_DEFAULT);
    }

    /**
     * 关联统计数据
     */
    public function stats()
    {
        return $this->hasOne(CardStats::class, 'user_id');
    }

    /**
     * 关联订单
     */
    public function orders()
    {
        return $this->hasMany(PremiumOrder::class, 'user_id');
    }

    /**
     * 检查是否为会员
     */
    public function isMember()
    {
        return $this->member_level > 0 &&
               $this->member_expire_time &&
               $this->member_expire_time > date('Y-m-d H:i:s');
    }

    /**
     * 验证密码（支持新旧密码格式）
     */
    public function checkPassword($password)
    {
        return password_verify($password, $this->password) || md5($password) === $this->password;
    }

    /**
     * 根据OpenID查找用户
     */
    public static function findByOpenid($openid)
    {
        return self::where('openid', $openid)->find();
    }

    /**
     * 获取性别文本
     */
    public function getGenderTextAttr($value, $data)
    {
        $genders = [
            0 => '未知',
            1 => '男',
            2 => '女',
        ];
        return $genders[$data['gender']] ?? '未知';
    }

    /**
     * 是否为微信用户
     */
    public function isWechatUser()
    {
        return !empty($this->openid);
    }

    /**
     * 搜索器：手机号
     */
    public function searchPhoneAttr($query, $value)
    {
        $query->where('phone', 'like', '%' . $value . '%');
    }

    /**
     * 搜索器：姓名
     */
    public function searchNameAttr($query, $value)
    {
        $query->where('name', 'like', '%' . $value . '%');
    }

    /**
     * 搜索器：公司
     */
    public function searchCompanyAttr($query, $value)
    {
        $query->where('company', 'like', '%' . $value . '%');
    }

    /**
     * 搜索器：会员等级
     */
    public function searchMemberLevelAttr($query, $value)
    {
        $query->where('member_level', $value);
    }

    /**
     * 搜索器：状态
     */
    public function searchStatusAttr($query, $value)
    {
        $query->where('status', $value);
    }

    /**
     * 生成访问令牌
     */
    public function generateAccessToken($ipAddress = null): string
    {
        $token = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', time() + 7 * 24 * 3600); // 7天过期

        $this->save([
            'access_token' => $token,
            'token_expires_at' => $expiresAt,
            'last_login_at' => date('Y-m-d H:i:s'),
            'last_login_ip' => $ipAddress
        ]);

        return $token;
    }

    /**
     * 验证访问令牌
     */
    public static function validateAccessToken(string $token): ?self
    {
        $user = self::where('access_token', $token)
            ->where('token_expires_at', '>', date('Y-m-d H:i:s'))
            ->where('status', 1)
            ->find();

        if ($user) {
            // 更新最后登录时间
            $user->save(['last_login_at' => date('Y-m-d H:i:s')]);
        }

        return $user;
    }

    /**
     * 撤销访问令牌
     */
    public function revokeAccessToken(): bool
    {
        return $this->save([
            'access_token' => null,
            'token_expires_at' => null
        ]);
    }

    /**
     * 检查令牌是否有效
     */
    public function isTokenValid(): bool
    {
        return !empty($this->access_token) &&
               !empty($this->token_expires_at) &&
               strtotime($this->token_expires_at) > time();
    }
}
