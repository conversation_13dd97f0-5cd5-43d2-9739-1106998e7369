{"version": 3, "file": "profile.js", "sources": ["pages/card/profile.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.45.2025010502 (1)/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY2FyZC9wcm9maWxlLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"profile-container\">\r\n\t\t<!-- 自定义导航栏 -->\r\n\t\t<view class=\"custom-navbar\" :style=\"navbarStyle\">\r\n\t\t\t<view class=\"navbar-left\">\r\n\t\t\t\t<view class=\"back-btn\" @click=\"goBack\">\r\n\t\t\t\t\t<text class=\"back-icon\">←</text>\r\n\t\t\t\t\t<text class=\"back-text\">返回</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"navbar-title\">{{ pageType === 'company' ? '企业介绍' : '个人介绍' }}</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 主要内容 -->\r\n\t\t<view class=\"profile-content\" :style=\"mainStyle\">\r\n\t\t\t<!-- 个人介绍部分 -->\r\n\t\t\t<view v-if=\"pageType === 'personal'\">\r\n\t\t\t\t<!-- 总开关 -->\r\n\t\t\t\t<view class=\"section-block master-switch-block\">\r\n\t\t\t\t\t<view class=\"master-switch-row\">\r\n\t\t\t\t\t\t<view class=\"master-switch-left\">\r\n\t\t\t\t\t\t\t<text class=\"master-switch-title\">显示个人介绍板块</text>\r\n\t\t\t\t\t\t\t<text class=\"master-switch-desc\">关闭后整个个人介绍部分将不会显示</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<switch \r\n\t\t\t\t\t\t\t:checked=\"userInfo.showProfileSection\" \r\n\t\t\t\t\t\t\t@change=\"(e) => userInfo.showProfileSection = e.detail.value\"\r\n\t\t\t\t\t\t\tcolor=\"#4f46e5\"\r\n\t\t\t\t\t\t\tstyle=\"transform:scale(0.8)\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 个人介绍 -->\r\n\t\t\t\t<view class=\"section-block\" :class=\"{'section-disabled-all': !userInfo.showProfileSection}\">\r\n\t\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t\t<view class=\"header-left\">\r\n\t\t\t\t\t\t\t<text class=\"section-title\">个人介绍</text>\r\n\t\t\t\t\t\t\t<text class=\"section-desc\">介绍自己的专业背景、工作经验等</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"switch-container\">\r\n\t\t\t\t\t\t\t<text class=\"switch-label\">{{userInfo.showDescription ? '显示' : '隐藏'}}</text>\r\n\t\t\t\t\t\t\t<switch \r\n\t\t\t\t\t\t\t\t:checked=\"userInfo.showDescription\" \r\n\t\t\t\t\t\t\t\t@change=\"(e) => userInfo.showDescription = e.detail.value\"\r\n\t\t\t\t\t\t\t\tcolor=\"#4f46e5\"\r\n\t\t\t\t\t\t\t\tstyle=\"transform:scale(0.7)\"\r\n\t\t\t\t\t\t\t\t:disabled=\"!userInfo.showProfileSection\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"textarea-wrapper\" v-if=\"userInfo.showDescription && userInfo.showProfileSection\">\r\n\t\t\t\t\t\t<textarea\r\n\t\t\t\t\t\t\tclass=\"profile-textarea\"\r\n\t\t\t\t\t\t\tv-model=\"userInfo.description\"\r\n\t\t\t\t\t\t\tplaceholder=\"请输入您的个人介绍，建议200字以内\"\r\n\t\t\t\t\t\t\tmaxlength=\"300\"\r\n\t\t\t\t\t\t\tauto-height\r\n\t\t\t\t\t\t\t:disabled=\"!userInfo.showProfileSection\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t<text class=\"word-count\">{{userInfo.description ? userInfo.description.length : 0}}/300</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"section-disabled\" v-else-if=\"!userInfo.showDescription && userInfo.showProfileSection\">\r\n\t\t\t\t\t\t<text class=\"disabled-text\">此内容已隐藏，不会在名片上显示</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"section-disabled\" v-else>\r\n\t\t\t\t\t\t<text class=\"disabled-text\">总开关已关闭，整个个人介绍板块将不会显示</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 个人标签 -->\r\n\t\t\t\t<view class=\"section-block\" :class=\"{'section-disabled-all': !userInfo.showProfileSection}\">\r\n\t\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t\t<view class=\"header-left\">\r\n\t\t\t\t\t\t\t<text class=\"section-title\">个人标签</text>\r\n\t\t\t\t\t\t\t<text class=\"section-desc\">添加能够代表您的关键词标签</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"switch-container\">\r\n\t\t\t\t\t\t\t<text class=\"switch-label\">{{userInfo.showTags ? '显示' : '隐藏'}}</text>\r\n\t\t\t\t\t\t\t<switch \r\n\t\t\t\t\t\t\t\t:checked=\"userInfo.showTags\" \r\n\t\t\t\t\t\t\t\t@change=\"(e) => userInfo.showTags = e.detail.value\"\r\n\t\t\t\t\t\t\t\tcolor=\"#4f46e5\"\r\n\t\t\t\t\t\t\t\tstyle=\"transform:scale(0.7)\"\r\n\t\t\t\t\t\t\t\t:disabled=\"!userInfo.showProfileSection\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"userInfo.showTags && userInfo.showProfileSection\">\r\n\t\t\t\t\t\t<view class=\"tags-container\">\r\n\t\t\t\t\t\t\t<view \r\n\t\t\t\t\t\t\t\tv-for=\"(tag, index) in userInfo.tags\" \r\n\t\t\t\t\t\t\t\t:key=\"index\" \r\n\t\t\t\t\t\t\t\tclass=\"tag-item\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<text class=\"tag-text\">{{tag}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"tag-delete\" @click=\"deleteTag(index)\">×</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"tag-add\" @click=\"showAddTagModal\" v-if=\"userInfo.tags.length < 6\">\r\n\t\t\t\t\t\t\t\t<text class=\"tag-add-icon\">+</text>\r\n\t\t\t\t\t\t\t\t<text class=\"tag-add-text\">添加标签</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"section-disabled\" v-else-if=\"!userInfo.showTags && userInfo.showProfileSection\">\r\n\t\t\t\t\t\t<text class=\"disabled-text\">此内容已隐藏，不会在名片上显示</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"section-disabled\" v-else>\r\n\t\t\t\t\t\t<text class=\"disabled-text\">总开关已关闭，整个个人介绍板块将不会显示</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 个人成就 -->\r\n\t\t\t\t<view class=\"section-block\" :class=\"{'section-disabled-all': !userInfo.showProfileSection}\">\r\n\t\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t\t<view class=\"header-left\">\r\n\t\t\t\t\t\t\t<text class=\"section-title\">个人成就</text>\r\n\t\t\t\t\t\t\t<text class=\"section-desc\">展示您的专业资质、奖项等</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"switch-container\">\r\n\t\t\t\t\t\t\t<text class=\"switch-label\">{{userInfo.showAchievements ? '显示' : '隐藏'}}</text>\r\n\t\t\t\t\t\t\t<switch \r\n\t\t\t\t\t\t\t\t:checked=\"userInfo.showAchievements\" \r\n\t\t\t\t\t\t\t\t@change=\"(e) => userInfo.showAchievements = e.detail.value\"\r\n\t\t\t\t\t\t\t\tcolor=\"#4f46e5\"\r\n\t\t\t\t\t\t\t\tstyle=\"transform:scale(0.7)\"\r\n\t\t\t\t\t\t\t\t:disabled=\"!userInfo.showProfileSection\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"userInfo.showAchievements && userInfo.showProfileSection\">\r\n\t\t\t\t\t\t<view class=\"achievements-container\">\r\n\t\t\t\t\t\t\t<view \r\n\t\t\t\t\t\t\t\tv-for=\"(achievement, index) in userInfo.achievements\" \r\n\t\t\t\t\t\t\t\t:key=\"index\" \r\n\t\t\t\t\t\t\t\tclass=\"achievement-item\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<text class=\"achievement-text\">{{achievement}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"achievement-delete\" @click=\"deleteAchievement(index)\">×</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"achievement-add\" @click=\"showAddAchievementModal\" v-if=\"userInfo.achievements.length < 3\">\r\n\t\t\t\t\t\t\t\t<text class=\"achievement-add-icon\">+</text>\r\n\t\t\t\t\t\t\t\t<text class=\"achievement-add-text\">添加成就</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"section-disabled\" v-else-if=\"!userInfo.showAchievements && userInfo.showProfileSection\">\r\n\t\t\t\t\t\t<text class=\"disabled-text\">此内容已隐藏，不会在名片上显示</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"section-disabled\" v-else>\r\n\t\t\t\t\t\t<text class=\"disabled-text\">总开关已关闭，整个个人介绍板块将不会显示</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 教育背景 -->\r\n\t\t\t\t<view class=\"section-block\" :class=\"{'section-disabled-all': !userInfo.showProfileSection}\">\r\n\t\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t\t<view class=\"header-left\">\r\n\t\t\t\t\t\t\t<text class=\"section-title\">教育背景</text>\r\n\t\t\t\t\t\t\t<text class=\"section-desc\">填写您的学历信息</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"switch-container\">\r\n\t\t\t\t\t\t\t<text class=\"switch-label\">{{userInfo.showEducation ? '显示' : '隐藏'}}</text>\r\n\t\t\t\t\t\t\t<switch \r\n\t\t\t\t\t\t\t\t:checked=\"userInfo.showEducation\" \r\n\t\t\t\t\t\t\t\t@change=\"(e) => userInfo.showEducation = e.detail.value\"\r\n\t\t\t\t\t\t\t\tcolor=\"#4f46e5\"\r\n\t\t\t\t\t\t\t\tstyle=\"transform:scale(0.7)\"\r\n\t\t\t\t\t\t\t\t:disabled=\"!userInfo.showProfileSection\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"input-wrapper\" v-if=\"userInfo.showEducation && userInfo.showProfileSection\">\r\n\t\t\t\t\t\t<input \r\n\t\t\t\t\t\t\tclass=\"profile-input\" \r\n\t\t\t\t\t\t\tv-model=\"userInfo.education\" \r\n\t\t\t\t\t\t\tplaceholder=\"例如：清华大学 计算机科学 硕士\"\r\n\t\t\t\t\t\t\t:disabled=\"!userInfo.showProfileSection\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"section-disabled\" v-else-if=\"!userInfo.showEducation && userInfo.showProfileSection\">\r\n\t\t\t\t\t\t<text class=\"disabled-text\">此内容已隐藏，不会在名片上显示</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"section-disabled\" v-else>\r\n\t\t\t\t\t\t<text class=\"disabled-text\">总开关已关闭，整个个人介绍板块将不会显示</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 企业介绍部分 -->\r\n\t\t\t<view v-else>\r\n\t\t\t\t<!-- 总开关 -->\r\n\t\t\t\t<view class=\"section-block master-switch-block company-block\">\r\n\t\t\t\t\t<view class=\"master-switch-row\">\r\n\t\t\t\t\t\t<view class=\"master-switch-left\">\r\n\t\t\t\t\t\t\t<text class=\"master-switch-title\">显示企业介绍板块</text>\r\n\t\t\t\t\t\t\t<text class=\"master-switch-desc\">关闭后整个企业介绍部分将不会显示</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<switch \r\n\t\t\t\t\t\t\t:checked=\"companyInfo.showCompanySection\" \r\n\t\t\t\t\t\t\t@change=\"(e) => companyInfo.showCompanySection = e.detail.value\"\r\n\t\t\t\t\t\t\tcolor=\"#ec4aa9\"\r\n\t\t\t\t\t\t\tstyle=\"transform:scale(0.8)\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 公司名称 -->\r\n\t\t\t\t<view class=\"section-block company-block\" :class=\"{'section-disabled-all': !companyInfo.showCompanySection}\">\r\n\t\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t\t<view class=\"header-left\">\r\n\t\t\t\t\t\t\t<text class=\"section-title\">公司名称</text>\r\n\t\t\t\t\t\t\t<text class=\"section-desc\">填写您的公司全称</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"switch-container\">\r\n\t\t\t\t\t\t\t<text class=\"switch-label\">{{companyInfo.showCompanyName ? '显示' : '隐藏'}}</text>\r\n\t\t\t\t\t\t\t<switch \r\n\t\t\t\t\t\t\t\t:checked=\"companyInfo.showCompanyName\" \r\n\t\t\t\t\t\t\t\t@change=\"(e) => companyInfo.showCompanyName = e.detail.value\"\r\n\t\t\t\t\t\t\t\tcolor=\"#ec4aa9\"\r\n\t\t\t\t\t\t\t\tstyle=\"transform:scale(0.7)\"\r\n\t\t\t\t\t\t\t\t:disabled=\"!companyInfo.showCompanySection\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"input-wrapper\" v-if=\"companyInfo.showCompanyName && companyInfo.showCompanySection\">\r\n\t\t\t\t\t\t<input \r\n\t\t\t\t\t\t\tclass=\"company-input\" \r\n\t\t\t\t\t\t\tv-model=\"companyInfo.companyName\" \r\n\t\t\t\t\t\t\tplaceholder=\"请输入公司全称\"\r\n\t\t\t\t\t\t\t:disabled=\"!companyInfo.showCompanySection\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"section-disabled\" v-else-if=\"!companyInfo.showCompanyName && companyInfo.showCompanySection\">\r\n\t\t\t\t\t\t<text class=\"disabled-text\">此内容已隐藏，不会在名片上显示</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"section-disabled\" v-else>\r\n\t\t\t\t\t\t<text class=\"disabled-text\">总开关已关闭，整个企业介绍板块将不会显示</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 公司简介 -->\r\n\t\t\t\t<view class=\"section-block company-block\" :class=\"{'section-disabled-all': !companyInfo.showCompanySection}\">\r\n\t\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t\t<view class=\"header-left\">\r\n\t\t\t\t\t\t\t<text class=\"section-title\">公司简介</text>\r\n\t\t\t\t\t\t\t<text class=\"section-desc\">介绍公司业务范围、发展历程等</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"switch-container\">\r\n\t\t\t\t\t\t\t<text class=\"switch-label\">{{companyInfo.showCompanyDesc ? '显示' : '隐藏'}}</text>\r\n\t\t\t\t\t\t\t<switch \r\n\t\t\t\t\t\t\t\t:checked=\"companyInfo.showCompanyDesc\" \r\n\t\t\t\t\t\t\t\t@change=\"(e) => companyInfo.showCompanyDesc = e.detail.value\"\r\n\t\t\t\t\t\t\t\tcolor=\"#ec4aa9\"\r\n\t\t\t\t\t\t\t\tstyle=\"transform:scale(0.7)\"\r\n\t\t\t\t\t\t\t\t:disabled=\"!companyInfo.showCompanySection\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"textarea-wrapper\" v-if=\"companyInfo.showCompanyDesc && companyInfo.showCompanySection\">\r\n\t\t\t\t\t\t<textarea\r\n\t\t\t\t\t\t\tclass=\"company-textarea\"\r\n\t\t\t\t\t\t\tv-model=\"companyInfo.companyDesc\"\r\n\t\t\t\t\t\t\tplaceholder=\"请输入公司简介，建议300字以内\"\r\n\t\t\t\t\t\t\tmaxlength=\"400\"\r\n\t\t\t\t\t\t\tauto-height\r\n\t\t\t\t\t\t\t:disabled=\"!companyInfo.showCompanySection\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t<text class=\"word-count\">{{companyInfo.companyDesc ? companyInfo.companyDesc.length : 0}}/400</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"section-disabled\" v-else-if=\"!companyInfo.showCompanyDesc && companyInfo.showCompanySection\">\r\n\t\t\t\t\t\t<text class=\"disabled-text\">此内容已隐藏，不会在名片上显示</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"section-disabled\" v-else>\r\n\t\t\t\t\t\t<text class=\"disabled-text\">总开关已关闭，整个企业介绍板块将不会显示</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 公司标语 -->\r\n\t\t\t\t<view class=\"section-block company-block\" :class=\"{'section-disabled-all': !companyInfo.showCompanySection}\">\r\n\t\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t\t<view class=\"header-left\">\r\n\t\t\t\t\t\t\t<text class=\"section-title\">公司标语</text>\r\n\t\t\t\t\t\t\t<text class=\"section-desc\">填写公司slogan或宣传语</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"switch-container\">\r\n\t\t\t\t\t\t\t<text class=\"switch-label\">{{companyInfo.showCompanySlogan ? '显示' : '隐藏'}}</text>\r\n\t\t\t\t\t\t\t<switch \r\n\t\t\t\t\t\t\t\t:checked=\"companyInfo.showCompanySlogan\" \r\n\t\t\t\t\t\t\t\t@change=\"(e) => companyInfo.showCompanySlogan = e.detail.value\"\r\n\t\t\t\t\t\t\t\tcolor=\"#ec4aa9\"\r\n\t\t\t\t\t\t\t\tstyle=\"transform:scale(0.7)\"\r\n\t\t\t\t\t\t\t\t:disabled=\"!companyInfo.showCompanySection\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"input-wrapper\" v-if=\"companyInfo.showCompanySlogan && companyInfo.showCompanySection\">\r\n\t\t\t\t\t\t<input \r\n\t\t\t\t\t\t\tclass=\"company-input\" \r\n\t\t\t\t\t\t\tv-model=\"companyInfo.companySlogan\" \r\n\t\t\t\t\t\t\tplaceholder=\"例如：科技改变生活\"\r\n\t\t\t\t\t\t\t:disabled=\"!companyInfo.showCompanySection\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"section-disabled\" v-else-if=\"!companyInfo.showCompanySlogan && companyInfo.showCompanySection\">\r\n\t\t\t\t\t\t<text class=\"disabled-text\">此内容已隐藏，不会在名片上显示</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"section-disabled\" v-else>\r\n\t\t\t\t\t\t<text class=\"disabled-text\">总开关已关闭，整个企业介绍板块将不会显示</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 企业优势 -->\r\n\t\t\t\t<view class=\"section-block company-block\" :class=\"{'section-disabled-all': !companyInfo.showCompanySection}\">\r\n\t\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t\t<view class=\"header-left\">\r\n\t\t\t\t\t\t\t<text class=\"section-title\">企业优势</text>\r\n\t\t\t\t\t\t\t<text class=\"section-desc\">添加企业核心竞争力标签</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"switch-container\">\r\n\t\t\t\t\t\t\t<text class=\"switch-label\">{{companyInfo.showCompanyAdvantages ? '显示' : '隐藏'}}</text>\r\n\t\t\t\t\t\t\t<switch \r\n\t\t\t\t\t\t\t\t:checked=\"companyInfo.showCompanyAdvantages\" \r\n\t\t\t\t\t\t\t\t@change=\"(e) => companyInfo.showCompanyAdvantages = e.detail.value\"\r\n\t\t\t\t\t\t\t\tcolor=\"#ec4aa9\"\r\n\t\t\t\t\t\t\t\tstyle=\"transform:scale(0.7)\"\r\n\t\t\t\t\t\t\t\t:disabled=\"!companyInfo.showCompanySection\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"companyInfo.showCompanyAdvantages && companyInfo.showCompanySection\">\r\n\t\t\t\t\t\t<view class=\"tags-container company-tags\">\r\n\t\t\t\t\t\t\t<view \r\n\t\t\t\t\t\t\t\tv-for=\"(advantage, index) in companyInfo.advantages\" \r\n\t\t\t\t\t\t\t\t:key=\"index\" \r\n\t\t\t\t\t\t\t\tclass=\"tag-item company-tag\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<text class=\"tag-text company-tag-text\">{{advantage}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"tag-delete company-tag-delete\" @click=\"deleteAdvantage(index)\">×</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"tag-add company-tag-add\" @click=\"showAddAdvantageModal\" v-if=\"companyInfo.advantages.length < 6\">\r\n\t\t\t\t\t\t\t\t<text class=\"tag-add-icon\">+</text>\r\n\t\t\t\t\t\t\t\t<text class=\"tag-add-text\">添加优势</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"section-disabled\" v-else-if=\"!companyInfo.showCompanyAdvantages && companyInfo.showCompanySection\">\r\n\t\t\t\t\t\t<text class=\"disabled-text\">此内容已隐藏，不会在名片上显示</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"section-disabled\" v-else>\r\n\t\t\t\t\t\t<text class=\"disabled-text\">总开关已关闭，整个企业介绍板块将不会显示</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 公司地址 -->\r\n\t\t\t\t<view class=\"section-block company-block\" :class=\"{'section-disabled-all': !companyInfo.showCompanySection}\">\r\n\t\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t\t<view class=\"header-left\">\r\n\t\t\t\t\t\t\t<text class=\"section-title\">公司地址</text>\r\n\t\t\t\t\t\t\t<text class=\"section-desc\">填写公司详细地址</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"switch-container\">\r\n\t\t\t\t\t\t\t<text class=\"switch-label\">{{companyInfo.showCompanyAddress ? '显示' : '隐藏'}}</text>\r\n\t\t\t\t\t\t\t<switch \r\n\t\t\t\t\t\t\t\t:checked=\"companyInfo.showCompanyAddress\" \r\n\t\t\t\t\t\t\t\t@change=\"(e) => companyInfo.showCompanyAddress = e.detail.value\"\r\n\t\t\t\t\t\t\t\tcolor=\"#ec4aa9\"\r\n\t\t\t\t\t\t\t\tstyle=\"transform:scale(0.7)\"\r\n\t\t\t\t\t\t\t\t:disabled=\"!companyInfo.showCompanySection\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"input-wrapper\" v-if=\"companyInfo.showCompanyAddress && companyInfo.showCompanySection\">\r\n\t\t\t\t\t\t<input \r\n\t\t\t\t\t\t\tclass=\"company-input\" \r\n\t\t\t\t\t\t\tv-model=\"companyInfo.companyAddress\" \r\n\t\t\t\t\t\t\tplaceholder=\"请输入公司地址\"\r\n\t\t\t\t\t\t\t:disabled=\"!companyInfo.showCompanySection\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"section-disabled\" v-else-if=\"!companyInfo.showCompanyAddress && companyInfo.showCompanySection\">\r\n\t\t\t\t\t\t<text class=\"disabled-text\">此内容已隐藏，不会在名片上显示</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"section-disabled\" v-else>\r\n\t\t\t\t\t\t<text class=\"disabled-text\">总开关已关闭，整个企业介绍板块将不会显示</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 页面底部占位，确保内容不被悬浮按钮遮挡 -->\r\n\t\t\t<view class=\"bottom-placeholder\"></view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 添加标签弹窗 -->\r\n\t\t<view class=\"custom-popup\" v-if=\"showTagPopup\">\r\n\t\t\t<view class=\"popup-mask\" @click=\"closeTagPopup\"></view>\r\n\t\t\t<view class=\"popup-content\">\r\n\t\t\t\t<view class=\"popup-title\">添加标签</view>\r\n\t\t\t\t<input\r\n\t\t\t\t\tclass=\"popup-input\"\r\n\t\t\t\t\tv-model=\"tagInput\"\r\n\t\t\t\t\tplaceholder=\"请输入标签内容(8字以内)\"\r\n\t\t\t\t\tmaxlength=\"8\"\r\n\t\t\t\t\t@input=\"onTagInputChange\"\r\n\t\t\t\t/>\r\n\t\t\t\t<view class=\"popup-buttons\">\r\n\t\t\t\t\t<button class=\"popup-btn cancel-btn\" @click=\"closeTagPopup\">取消</button>\r\n\t\t\t\t\t<button class=\"popup-btn confirm-btn\" @click=\"confirmAddTag\">确定</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 添加成就弹窗 -->\r\n\t\t<view class=\"custom-popup\" v-if=\"showAchievementPopup\">\r\n\t\t\t<view class=\"popup-mask\" @click=\"closeAchievementPopup\"></view>\r\n\t\t\t<view class=\"popup-content\">\r\n\t\t\t\t<view class=\"popup-title\">添加成就</view>\r\n\t\t\t\t<input \r\n\t\t\t\t\tclass=\"popup-input\" \r\n\t\t\t\t\tv-model=\"achievementInput\" \r\n\t\t\t\t\tplaceholder=\"请输入成就内容(20字以内)\"\r\n\t\t\t\t\tmaxlength=\"20\"\r\n\t\t\t\t/>\r\n\t\t\t\t<view class=\"popup-buttons\">\r\n\t\t\t\t\t<button class=\"popup-btn cancel-btn\" @click=\"closeAchievementPopup\">取消</button>\r\n\t\t\t\t\t<button class=\"popup-btn confirm-btn\" @click=\"confirmAddAchievement\">确定</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 添加企业优势弹窗 -->\r\n\t\t<view class=\"custom-popup\" v-if=\"showAdvantagePopup\">\r\n\t\t\t<view class=\"popup-mask\" @click=\"closeAdvantagePopup\"></view>\r\n\t\t\t<view class=\"popup-content company-popup\">\r\n\t\t\t\t<view class=\"popup-title\">添加企业优势</view>\r\n\t\t\t\t<input \r\n\t\t\t\t\tclass=\"popup-input\" \r\n\t\t\t\t\tv-model=\"advantageInput\" \r\n\t\t\t\t\tplaceholder=\"请输入企业优势标签(10字以内)\"\r\n\t\t\t\t\tmaxlength=\"10\"\r\n\t\t\t\t/>\r\n\t\t\t\t<view class=\"popup-buttons\">\r\n\t\t\t\t\t<button class=\"popup-btn cancel-btn\" @click=\"closeAdvantagePopup\">取消</button>\r\n\t\t\t\t\t<button class=\"popup-btn confirm-btn company-confirm-btn\" @click=\"confirmAddAdvantage\">确定</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 悬浮保存按钮 -->\r\n\t\t<view class=\"floating-save-btn-wrapper\">\r\n\t\t\t<button class=\"floating-save-btn\" @click=\"saveProfile\">保存</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport userService from '../../services/userService.js';\r\nimport userInfoService from '../../services/userInfoService.js';\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tstatusBarHeight: 44, // 状态栏高度\r\n\t\t\tpageType: 'personal', // 页面类型：personal 或 company\r\n\t\t\tuserInfo: {\r\n\t\t\t\tdescription: '',\r\n\t\t\t\ttags: [],\r\n\t\t\t\tachievements: [],\r\n\t\t\t\teducation: '',\r\n\t\t\t\t// 显示控制开关\r\n\t\t\t\tshowProfileSection: true, // 总开关\r\n\t\t\t\tshowDescription: true,\r\n\t\t\t\tshowTags: true,\r\n\t\t\t\tshowAchievements: true,\r\n\t\t\t\tshowEducation: true\r\n\t\t\t},\r\n\t\t\tcompanyInfo: {\r\n\t\t\t\tcompanyName: '',\r\n\t\t\t\tcompanyDesc: '',\r\n\t\t\t\tcompanySlogan: '',\r\n\t\t\t\tadvantages: [],\r\n\t\t\t\tcompanyAddress: '',\r\n\t\t\t\t// 显示控制开关\r\n\t\t\t\tshowCompanySection: true, // 总开关\r\n\t\t\t\tshowCompanyName: true,\r\n\t\t\t\tshowCompanyDesc: true,\r\n\t\t\t\tshowCompanySlogan: true,\r\n\t\t\t\tshowCompanyAdvantages: true,\r\n\t\t\t\tshowCompanyAddress: true\r\n\t\t\t},\r\n\t\t\ttagInput: '',\r\n\t\t\tachievementInput: '',\r\n\t\t\t// 弹窗控制\r\n\t\t\tshowTagPopup: false,\r\n\t\t\tshowAchievementPopup: false,\r\n\t\t\t// 企业优势弹窗控制\r\n\t\t\tshowAdvantagePopup: false,\r\n\t\t\tadvantageInput: ''\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tnavbarStyle() {\r\n\t\t\treturn {\r\n\t\t\t\theight: `${this.statusBarHeight + 45}px`\r\n\t\t\t}\r\n\t\t},\r\n\t\tmainStyle() {\r\n\t\t\treturn {\r\n\t\t\t\tmarginTop: `${this.statusBarHeight + 55}px`\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tasync onLoad(option) {\r\n\t\tthis.setStatusBarHeight();\r\n\r\n\t\t// 根据参数决定页面类型\r\n\t\tif (option && option.type === 'company') {\r\n\t\t\tthis.pageType = 'company';\r\n\t\t\tawait this.loadCompanyInfo();\r\n\t\t} else {\r\n\t\t\tthis.pageType = 'personal';\r\n\t\t\tawait this.loadUserInfo();\r\n\t\t}\r\n\t},\r\n\r\n\tonShow() {\r\n\t\t// 页面显示时不重新加载数据，避免覆盖用户输入\r\n\t},\r\n\r\n\tmethods: {\r\n\t\tasync loadUserInfo() {\r\n\t\t\t// 如果用户已登录，优先从服务器加载数据\r\n\t\t\tif (userService.isLoggedIn()) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst result = await userInfoService.getCurrentUserInfo();\r\n\r\n\t\t\t\t\tif (result.success && result.data) {\r\n\t\t\t\t\t\tconst serverData = result.data;\r\n\r\n\t\t\t\t\t\t// 使用服务器数据，但保留当前用户输入\r\n\t\t\t\t\t\tthis.userInfo = {\r\n\t\t\t\t\t\t\tdescription: serverData.description || this.userInfo.description || '',\r\n\t\t\t\t\t\t\ttags: serverData.tags || this.userInfo.tags || [],\r\n\t\t\t\t\t\t\tachievements: serverData.achievements || this.userInfo.achievements || [],\r\n\t\t\t\t\t\t\teducation: serverData.education || this.userInfo.education || '',\r\n\t\t\t\t\t\t\t// 显示控制开关，优先使用服务器数据\r\n\t\t\t\t\t\t\tshowProfileSection: serverData.showProfileSection !== undefined ? serverData.showProfileSection : (this.userInfo.showProfileSection !== undefined ? this.userInfo.showProfileSection : true),\r\n\t\t\t\t\t\t\tshowDescription: serverData.showDescription !== undefined ? serverData.showDescription : (this.userInfo.showDescription !== undefined ? this.userInfo.showDescription : true),\r\n\t\t\t\t\t\t\tshowTags: serverData.showTags !== undefined ? serverData.showTags : (this.userInfo.showTags !== undefined ? this.userInfo.showTags : true),\r\n\t\t\t\t\t\t\tshowAchievements: serverData.showAchievements !== undefined ? serverData.showAchievements : (this.userInfo.showAchievements !== undefined ? this.userInfo.showAchievements : true),\r\n\t\t\t\t\t\t\tshowEducation: serverData.showEducation !== undefined ? serverData.showEducation : (this.userInfo.showEducation !== undefined ? this.userInfo.showEducation : true)\r\n\t\t\t\t\t\t};\r\n\r\n\t\t\t\t\t\t// 同步到本地存储\r\n\t\t\t\t\t\tconst currentUserInfo = uni.getStorageSync('userInfo') || {};\r\n\t\t\t\t\t\tconst updatedUserInfo = {\r\n\t\t\t\t\t\t\t...currentUserInfo,\r\n\t\t\t\t\t\t\t...this.userInfo\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tuni.setStorageSync('userInfo', updatedUserInfo);\r\n\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('从服务器加载个人介绍数据失败:', error);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// 服务器加载失败或未登录，使用本地数据\r\n\t\t\tthis.loadUserInfoFromLocal();\r\n\t\t},\r\n\r\n\t\tloadUserInfoFromLocal() {\r\n\t\t\tconst savedInfo = uni.getStorageSync('userInfo') || {};\r\n\r\n\t\t\t// 保留当前用户输入的数据，只更新未设置的字段\r\n\t\t\tthis.userInfo = {\r\n\t\t\t\tdescription: savedInfo.description || this.userInfo.description || '',\r\n\t\t\t\ttags: savedInfo.tags || this.userInfo.tags || [],\r\n\t\t\t\tachievements: savedInfo.achievements || this.userInfo.achievements || [],\r\n\t\t\t\teducation: savedInfo.education || this.userInfo.education || '',\r\n\t\t\t\t// 显示控制开关，默认为true，如果已保存则使用保存的值\r\n\t\t\t\tshowProfileSection: savedInfo.showProfileSection !== undefined ? savedInfo.showProfileSection : (this.userInfo.showProfileSection !== undefined ? this.userInfo.showProfileSection : true),\r\n\t\t\t\tshowDescription: savedInfo.showDescription !== undefined ? savedInfo.showDescription : (this.userInfo.showDescription !== undefined ? this.userInfo.showDescription : true),\r\n\t\t\t\tshowTags: savedInfo.showTags !== undefined ? savedInfo.showTags : (this.userInfo.showTags !== undefined ? this.userInfo.showTags : true),\r\n\t\t\t\tshowAchievements: savedInfo.showAchievements !== undefined ? savedInfo.showAchievements : (this.userInfo.showAchievements !== undefined ? this.userInfo.showAchievements : true),\r\n\t\t\t\tshowEducation: savedInfo.showEducation !== undefined ? savedInfo.showEducation : (this.userInfo.showEducation !== undefined ? this.userInfo.showEducation : true)\r\n\t\t\t};\r\n\t\t},\r\n\t\t\r\n\t\tasync loadCompanyInfo() {\r\n\t\t\t// 如果用户已登录，优先从服务器加载数据\r\n\t\t\tif (userService.isLoggedIn()) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst result = await userInfoService.getCurrentUserInfo();\r\n\r\n\t\t\t\t\tif (result.success && result.data) {\r\n\t\t\t\t\t\tconst serverData = result.data;\r\n\r\n\t\t\t\t\t\t// 使用服务器数据\r\n\t\t\t\t\t\tthis.companyInfo = {\r\n\t\t\t\t\t\t\tcompanyName: serverData.companyName || '',\r\n\t\t\t\t\t\t\tcompanyDesc: serverData.companyDesc || '',\r\n\t\t\t\t\t\t\tcompanySlogan: serverData.companySlogan || '',\r\n\t\t\t\t\t\t\tadvantages: serverData.advantages || [],\r\n\t\t\t\t\t\t\tcompanyAddress: serverData.companyAddress || '',\r\n\t\t\t\t\t\t\t// 显示控制开关，默认为true，如果已保存则使用保存的值\r\n\t\t\t\t\t\t\tshowCompanySection: serverData.showCompanySection !== undefined ? serverData.showCompanySection : true,\r\n\t\t\t\t\t\t\tshowCompanyName: serverData.showCompanyName !== undefined ? serverData.showCompanyName : true,\r\n\t\t\t\t\t\t\tshowCompanyDesc: serverData.showCompanyDesc !== undefined ? serverData.showCompanyDesc : true,\r\n\t\t\t\t\t\t\tshowCompanySlogan: serverData.showCompanySlogan !== undefined ? serverData.showCompanySlogan : true,\r\n\t\t\t\t\t\t\tshowCompanyAdvantages: serverData.showCompanyAdvantages !== undefined ? serverData.showCompanyAdvantages : true,\r\n\t\t\t\t\t\t\tshowCompanyAddress: serverData.showCompanyAddress !== undefined ? serverData.showCompanyAddress : true\r\n\t\t\t\t\t\t};\r\n\r\n\t\t\t\t\t\t// 同步到本地存储\r\n\t\t\t\t\t\tconst currentUserInfo = uni.getStorageSync('userInfo') || {};\r\n\t\t\t\t\t\tconst updatedUserInfo = {\r\n\t\t\t\t\t\t\t...currentUserInfo,\r\n\t\t\t\t\t\t\t...this.companyInfo\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tuni.setStorageSync('userInfo', updatedUserInfo);\r\n\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('从服务器加载企业介绍数据失败:', error);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// 服务器加载失败或未登录，使用本地数据\r\n\t\t\tthis.loadCompanyInfoFromLocal();\r\n\t\t},\r\n\r\n\t\tloadCompanyInfoFromLocal() {\r\n\t\t\tconst savedInfo = uni.getStorageSync('userInfo') || {};\r\n\r\n\t\t\t// 确保所有字段都存在\r\n\t\t\tthis.companyInfo = {\r\n\t\t\t\tcompanyName: savedInfo.companyName || '',\r\n\t\t\t\tcompanyDesc: savedInfo.companyDesc || '',\r\n\t\t\t\tcompanySlogan: savedInfo.companySlogan || '',\r\n\t\t\t\tadvantages: savedInfo.advantages || [],\r\n\t\t\t\tcompanyAddress: savedInfo.companyAddress || '',\r\n\t\t\t\t// 显示控制开关，默认为true，如果已保存则使用保存的值\r\n\t\t\t\tshowCompanySection: savedInfo.showCompanySection !== undefined ? savedInfo.showCompanySection : true,\r\n\t\t\t\tshowCompanyName: savedInfo.showCompanyName !== undefined ? savedInfo.showCompanyName : true,\r\n\t\t\t\tshowCompanyDesc: savedInfo.showCompanyDesc !== undefined ? savedInfo.showCompanyDesc : true,\r\n\t\t\t\tshowCompanySlogan: savedInfo.showCompanySlogan !== undefined ? savedInfo.showCompanySlogan : true,\r\n\t\t\t\tshowCompanyAdvantages: savedInfo.showCompanyAdvantages !== undefined ? savedInfo.showCompanyAdvantages : true,\r\n\t\t\t\tshowCompanyAddress: savedInfo.showCompanyAddress !== undefined ? savedInfo.showCompanyAddress : true\r\n\t\t\t};\r\n\t\t},\r\n\t\t\r\n\t\tsetStatusBarHeight() {\r\n\t\t\t// 获取系统信息，设置状态栏高度\r\n\t\t\ttry {\r\n\t\t\t\tconst windowInfo = uni.getWindowInfo();\r\n\t\t\t\tconst statusBarHeight = windowInfo.statusBarHeight || 0;\r\n\t\t\t\tthis.statusBarHeight = statusBarHeight;\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.log('获取状态栏高度失败:', error);\r\n\t\t\t\tthis.statusBarHeight = 44; // 默认值\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tgoBack() {\r\n\t\t\tuni.navigateBack();\r\n\t\t},\r\n\t\t\r\n\t\tshowAddTagModal() {\r\n\t\t\tthis.tagInput = ''; // 清空输入\r\n\t\t\tthis.showTagPopup = true;\r\n\r\n\t\t\t// 确保tags数组已初始化\r\n\t\t\tif (!this.userInfo.tags) {\r\n\t\t\t\tthis.userInfo.tags = [];\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tcloseTagPopup() {\r\n\t\t\tthis.showTagPopup = false;\r\n\t\t},\r\n\r\n\t\tonTagInputChange(e) {\r\n\t\t\tthis.tagInput = e.detail.value;\r\n\t\t},\r\n\t\t\r\n\t\tconfirmAddTag() {\r\n\t\t\tconst value = this.tagInput;\r\n\r\n\t\t\tif (value && value.trim() && value.trim().length > 0 && value.trim().length <= 8) {\r\n\t\t\t\t// 检查是否已存在相同标签\r\n\t\t\t\tif (!this.userInfo.tags.includes(value.trim())) {\r\n\t\t\t\t\tthis.userInfo.tags.push(value.trim());\r\n\t\t\t\t\tthis.closeTagPopup();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '标签添加成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '标签已存在',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t} else if (value && value.trim().length > 8) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '标签不能超过8个字',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请输入标签内容',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tdeleteTag(index) {\r\n\t\t\tthis.userInfo.tags.splice(index, 1);\r\n\t\t},\r\n\t\t\r\n\t\tshowAddAchievementModal() {\r\n\t\t\tthis.achievementInput = ''; // 清空输入\r\n\t\t\tthis.showAchievementPopup = true;\r\n\t\t},\r\n\t\t\r\n\t\tcloseAchievementPopup() {\r\n\t\t\tthis.showAchievementPopup = false;\r\n\t\t},\r\n\t\t\r\n\t\tconfirmAddAchievement() {\r\n\t\t\tconst value = this.achievementInput;\r\n\t\t\tif (value && value.trim() && value.length <= 20) {\r\n\t\t\t\tthis.userInfo.achievements.push(value.trim());\r\n\t\t\t\tthis.closeAchievementPopup();\r\n\t\t\t} else if (value.length > 20) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '成就内容不能超过20个字',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请输入成就内容',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tdeleteAchievement(index) {\r\n\t\t\tthis.userInfo.achievements.splice(index, 1);\r\n\t\t},\r\n\t\t\r\n\t\tshowAddAdvantageModal() {\r\n\t\t\tthis.advantageInput = '';\r\n\t\t\tthis.showAdvantagePopup = true;\r\n\t\t},\r\n\t\t\r\n\t\tcloseAdvantagePopup() {\r\n\t\t\tthis.showAdvantagePopup = false;\r\n\t\t},\r\n\t\t\r\n\t\tconfirmAddAdvantage() {\r\n\t\t\tconst value = this.advantageInput;\r\n\t\t\tif (value && value.trim() && value.length <= 10) {\r\n\t\t\t\t// 检查是否已存在相同优势\r\n\t\t\t\tif (!this.companyInfo.advantages.includes(value.trim())) {\r\n\t\t\t\t\tthis.companyInfo.advantages.push(value.trim());\r\n\t\t\t\t\tthis.closeAdvantagePopup();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '标签已存在',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t} else if (value.length > 10) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '标签不能超过10个字',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请输入标签内容',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tdeleteAdvantage(index) {\r\n\t\t\tthis.companyInfo.advantages.splice(index, 1);\r\n\t\t},\r\n\t\t\r\n\t\tasync saveProfile() {\r\n\t\t\t// 检查登录状态\r\n\t\t\tif (!userService.isLoggedIn()) {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '需要登录',\r\n\t\t\t\t\tcontent: '保存功能需要登录后才能使用，是否前往登录？',\r\n\t\t\t\t\tconfirmText: '去登录',\r\n\t\t\t\t\tcancelText: '取消',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: '/pages/auth/auth'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 检查是否为专业版或企业版用户\r\n\t\t\tconst isMember = uni.getStorageSync('isMember');\r\n\t\t\tconst memberLevel = uni.getStorageSync('memberLevel') || 0;\r\n\t\t\tconst memberExpireDate = uni.getStorageSync('memberExpireDate');\r\n\r\n\t\t\t// 检查是否过期\r\n\t\t\tlet isExpired = false;\r\n\t\t\tif (memberExpireDate) {\r\n\t\t\t\tconst now = new Date();\r\n\t\t\t\t// 修复iOS日期格式兼容性问题\r\n\t\t\t\tconst formattedDate = memberExpireDate.replace(/\\s/g, 'T');\r\n\t\t\t\tconst expireDate = new Date(formattedDate);\r\n\t\t\t\tisExpired = now > expireDate;\r\n\t\t\t}\r\n\r\n\t\t\t// 检查权限：需要专业版或企业版且未过期\r\n\t\t\tconst hasPermission = isMember && memberLevel >= 1 && !isExpired;\r\n\r\n\t\t\tif (!hasPermission) {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '专业版功能',\r\n\t\t\t\t\tcontent: this.pageType === 'company' ? '企业介绍保存功能需要专业版或企业版权限' : '个人介绍保存功能需要专业版或企业版权限',\r\n\t\t\t\t\tconfirmText: '立即升级',\r\n\t\t\t\t\tcancelText: '取消',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t// 跳转到会员开通页面\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: '/pages/company/premium'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 显示保存中状态\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '保存中...',\r\n\t\t\t\tmask: true\r\n\t\t\t});\r\n\r\n\t\t\ttry {\r\n\t\t\t\t// 获取当前存储的完整用户信息\r\n\t\t\t\tconst currentUserInfo = uni.getStorageSync('userInfo') || {};\r\n\t\t\t\tlet updatedUserInfo;\r\n\r\n\t\t\t\t// 根据页面类型准备不同的数据\r\n\t\t\t\tif (this.pageType === 'company') {\r\n\t\t\t\t\t// 更新企业介绍相关字段\r\n\t\t\t\t\tupdatedUserInfo = {\r\n\t\t\t\t\t\t...currentUserInfo,\r\n\t\t\t\t\t\tcompanyName: this.companyInfo.companyName,\r\n\t\t\t\t\t\tcompanyDesc: this.companyInfo.companyDesc,\r\n\t\t\t\t\t\tcompanySlogan: this.companyInfo.companySlogan,\r\n\t\t\t\t\t\tadvantages: this.companyInfo.advantages,\r\n\t\t\t\t\t\tcompanyAddress: this.companyInfo.companyAddress,\r\n\t\t\t\t\t\t// 保存显示控制开关状态\r\n\t\t\t\t\t\tshowCompanySection: this.companyInfo.showCompanySection,\r\n\t\t\t\t\t\tshowCompanyName: this.companyInfo.showCompanyName,\r\n\t\t\t\t\t\tshowCompanyDesc: this.companyInfo.showCompanyDesc,\r\n\t\t\t\t\t\tshowCompanySlogan: this.companyInfo.showCompanySlogan,\r\n\t\t\t\t\t\tshowCompanyAdvantages: this.companyInfo.showCompanyAdvantages,\r\n\t\t\t\t\t\tshowCompanyAddress: this.companyInfo.showCompanyAddress\r\n\t\t\t\t\t};\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 更新个人介绍相关字段\r\n\t\t\t\t\tupdatedUserInfo = {\r\n\t\t\t\t\t\t...currentUserInfo,\r\n\t\t\t\t\t\tdescription: this.userInfo.description,\r\n\t\t\t\t\t\ttags: this.userInfo.tags,\r\n\t\t\t\t\t\tachievements: this.userInfo.achievements,\r\n\t\t\t\t\t\teducation: this.userInfo.education,\r\n\t\t\t\t\t\t// 保存显示控制开关状态\r\n\t\t\t\t\t\tshowProfileSection: this.userInfo.showProfileSection,\r\n\t\t\t\t\t\tshowDescription: this.userInfo.showDescription,\r\n\t\t\t\t\t\tshowTags: this.userInfo.showTags,\r\n\t\t\t\t\t\tshowAchievements: this.userInfo.showAchievements,\r\n\t\t\t\t\t\tshowEducation: this.userInfo.showEducation\r\n\t\t\t\t\t};\r\n\t\t\t\t}\r\n\r\n\r\n\r\n\t\t\t\t// 先更新本地存储（乐观更新）\r\n\t\t\t\tuni.setStorageSync('userInfo', updatedUserInfo);\r\n\r\n\t\t\t\t// 同步到服务器\r\n\t\t\t\tconst result = await userInfoService.updateUserInfo(updatedUserInfo);\r\n\r\n\t\t\t\tif (result.success) {\r\n\t\t\t\t\t// 服务器保存成功\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '保存成功',\r\n\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t// 延迟返回上一页\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t\t\t\t}, 1500);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 服务器保存失败，但本地已保存\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: result.message || '保存失败，请重试',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('保存个人介绍失败:', error);\r\n\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '保存失败，请检查网络连接',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 3000\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.profile-container {\r\n\tmin-height: 100vh;\r\n\tbackground: #f8fafc;\r\n\tposition: relative;\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbackground: rgba(255, 255, 255, 0.95);\r\n\tbackdrop-filter: blur(20rpx);\r\n\tz-index: 1000;\r\n\tdisplay: flex;\r\n\talign-items: flex-end;\r\n\tpadding: 20rpx 32rpx 20rpx 32rpx;\r\n\tbox-sizing: border-box;\r\n\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.navbar-left {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.back-btn {\r\n\tpadding: 8rpx 16rpx;\r\n\tbackground: white;\r\n\tborder-radius: 20rpx;\r\n\tborder: 1rpx solid #e2e8f0;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 6rpx;\r\n}\r\n\r\n.back-icon {\r\n\tfont-size: 28rpx;\r\n\tcolor: #64748b;\r\n}\r\n\r\n.back-text {\r\n\tfont-size: 24rpx;\r\n\tcolor: #64748b;\r\n}\r\n\r\n.navbar-title {\r\n\tcolor: #111827;\r\n\tfont-size: 36rpx;\r\n\tfont-weight: 600;\r\n\tposition: absolute;\r\n\tleft: 50%;\r\n\ttransform: translateX(-50%);\r\n\tletter-spacing: 0.5rpx;\r\n}\r\n\r\n/* 主要内容 */\r\n.profile-content {\r\n\tpadding: 32rpx;\r\n}\r\n\r\n.section-block {\r\n\tbackground: white;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 24rpx;\r\n\tmargin-bottom: 24rpx;\r\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);\r\n}\r\n\r\n.section-header {\r\n\tmargin-bottom: 16rpx;\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n}\r\n\r\n.header-left {\r\n\tflex: 1;\r\n}\r\n\r\n.section-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #111827;\r\n\tdisplay: block;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.section-desc {\r\n\tfont-size: 24rpx;\r\n\tcolor: #64748b;\r\n\tdisplay: block;\r\n}\r\n\r\n.switch-container {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.switch-label {\r\n\tfont-size: 24rpx;\r\n\tcolor: #64748b;\r\n\tmargin-right: 8rpx;\r\n}\r\n\r\n.section-disabled {\r\n\tbackground: #f1f5f9;\r\n\tpadding: 24rpx;\r\n\tborder-radius: 12rpx;\r\n\ttext-align: center;\r\n}\r\n\r\n.disabled-text {\r\n\tfont-size: 26rpx;\r\n\tcolor: #94a3b8;\r\n}\r\n\r\n/* 总开关样式 */\r\n.master-switch-block {\r\n\tbackground: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);\r\n\tborder-left: 6rpx solid #4f46e5;\r\n}\r\n\r\n.master-switch-row {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n}\r\n\r\n.master-switch-left {\r\n\tflex: 1;\r\n}\r\n\r\n.master-switch-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #4f46e5;\r\n\tdisplay: block;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.master-switch-desc {\r\n\tfont-size: 24rpx;\r\n\tcolor: #6b7280;\r\n\tdisplay: block;\r\n}\r\n\r\n/* 总开关关闭时的灰度样式 */\r\n.section-disabled-all {\r\n\topacity: 0.6;\r\n\tposition: relative;\r\n}\r\n\r\n.textarea-wrapper {\r\n\tposition: relative;\r\n\tmargin-top: 16rpx;\r\n}\r\n\r\n.profile-textarea {\r\n\twidth: 100%;\r\n\tpadding: 20rpx;\r\n\tbackground: #f1f5f9;\r\n\tborder-radius: 12rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #334155;\r\n\tmin-height: 200rpx;\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n.word-count {\r\n\tposition: absolute;\r\n\tbottom: 12rpx;\r\n\tright: 20rpx;\r\n\tfont-size: 24rpx;\r\n\tcolor: #94a3b8;\r\n}\r\n\r\n.input-wrapper {\r\n\tmargin-top: 16rpx;\r\n}\r\n\r\n.profile-input {\r\n\twidth: 100%;\r\n\tpadding: 20rpx;\r\n\tbackground: #f1f5f9;\r\n\tborder-radius: 12rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #334155;\r\n\tbox-sizing: border-box;\r\n\theight: 88rpx;\r\n\tline-height: 48rpx;\r\n}\r\n\r\n/* 标签样式 */\r\n.tags-container {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tgap: 16rpx;\r\n\tmargin-top: 16rpx;\r\n}\r\n\r\n.tag-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tbackground: rgba(99, 102, 241, 0.1);\r\n\tpadding: 10rpx 20rpx;\r\n\tborder-radius: 40rpx;\r\n\tborder: 1rpx solid rgba(99, 102, 241, 0.2);\r\n}\r\n\r\n.tag-text {\r\n\tfont-size: 26rpx;\r\n\tcolor: #4f46e5;\r\n\tmargin-right: 8rpx;\r\n}\r\n\r\n.tag-delete {\r\n\tfont-size: 28rpx;\r\n\tcolor: #4f46e5;\r\n\tfont-weight: bold;\r\n\tpadding: 0 4rpx;\r\n}\r\n\r\n.tag-add {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tbackground: #f1f5f9;\r\n\tpadding: 10rpx 20rpx;\r\n\tborder-radius: 40rpx;\r\n\tborder: 1rpx dashed #cbd5e1;\r\n}\r\n\r\n.tag-add-icon {\r\n\tfont-size: 26rpx;\r\n\tcolor: #64748b;\r\n\tmargin-right: 8rpx;\r\n}\r\n\r\n.tag-add-text {\r\n\tfont-size: 26rpx;\r\n\tcolor: #64748b;\r\n}\r\n\r\n/* 成就样式 */\r\n.achievements-container {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 16rpx;\r\n\tmargin-top: 16rpx;\r\n}\r\n\r\n.achievement-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tbackground: rgba(79, 70, 229, 0.05);\r\n\tpadding: 16rpx;\r\n\tborder-radius: 12rpx;\r\n\tborder-left: 4rpx solid #4f46e5;\r\n}\r\n\r\n.achievement-text {\r\n\tfont-size: 26rpx;\r\n\tcolor: #4f46e5;\r\n\tflex: 1;\r\n}\r\n\r\n.achievement-delete {\r\n\tfont-size: 28rpx;\r\n\tcolor: #4f46e5;\r\n\tfont-weight: bold;\r\n\tpadding: 0 4rpx;\r\n}\r\n\r\n.achievement-add {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tbackground: #f1f5f9;\r\n\tpadding: 16rpx;\r\n\tborder-radius: 12rpx;\r\n\tborder: 1rpx dashed #cbd5e1;\r\n}\r\n\r\n.achievement-add-icon {\r\n\tfont-size: 26rpx;\r\n\tcolor: #64748b;\r\n\tmargin-right: 8rpx;\r\n}\r\n\r\n.achievement-add-text {\r\n\tfont-size: 26rpx;\r\n\tcolor: #64748b;\r\n}\r\n\r\n/* 弹窗样式 */\r\n.custom-popup {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tz-index: 9999;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.popup-mask {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tbackground: rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.popup-content {\r\n\twidth: 80%;\r\n\tbackground: white;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 40rpx;\r\n\tposition: relative;\r\n\tz-index: 10000;\r\n}\r\n\r\n.popup-title {\r\n\ttext-align: center;\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #4f46e5;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.popup-input {\r\n\twidth: 100%;\r\n\theight: 88rpx;\r\n\tbackground: #f1f5f9;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 0 24rpx;\r\n\tmargin-bottom: 30rpx;\r\n\tbox-sizing: border-box;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.popup-buttons {\r\n\tdisplay: flex;\r\n\tgap: 20rpx;\r\n}\r\n\r\n.popup-btn {\r\n\tflex: 1;\r\n\theight: 80rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tfont-size: 28rpx;\r\n\tborder-radius: 12rpx;\r\n\tborder: none;\r\n}\r\n\r\n.cancel-btn {\r\n\tbackground: #f1f5f9;\r\n\tcolor: #64748b;\r\n}\r\n\r\n.confirm-btn {\r\n\tbackground: #4f46e5;\r\n\tcolor: white;\r\n}\r\n\r\n/* 浮动保存按钮 */\r\n.floating-save-btn-wrapper {\r\n\tposition: fixed;\r\n\tbottom: 40rpx;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\tz-index: 100;\r\n}\r\n\r\n.floating-save-btn {\r\n\twidth: 80%;\r\n\theight: 88rpx;\r\n\tline-height: 88rpx;\r\n\tbackground-color: v-bind(pageType === 'company' ? '#ec4aa9' : '#4f46e5');\r\n\tcolor: white;\r\n\tborder-radius: 44rpx;\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tbox-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.bottom-placeholder {\r\n\theight: 140rpx;\r\n}\r\n\r\n/* 企业介绍样式 */\r\n.company-block {\r\n\tbackground: white;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 24rpx;\r\n\tmargin-bottom: 24rpx;\r\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);\r\n\tborder-left: 6rpx solid #ec4aa9;\r\n}\r\n\r\n.company-block.master-switch-block {\r\n\tbackground: linear-gradient(135deg, #fff0f7 0%, #fce7f3 100%);\r\n}\r\n\r\n.company-block .master-switch-title {\r\n\tcolor: #ec4aa9;\r\n}\r\n\r\n.company-textarea {\r\n\twidth: 100%;\r\n\tmin-height: 200rpx;\r\n\tbackground: #fdf2f8;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 20rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #1e293b;\r\n}\r\n\r\n.company-input {\r\n\twidth: 100%;\r\n\theight: 80rpx;\r\n\tbackground: #fdf2f8;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 0 24rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #1e293b;\r\n}\r\n\r\n.company-tags .tag-item {\r\n\tbackground-color: #fdf2ff;\r\n\tborder: 1rpx solid #fbcfe8;\r\n}\r\n\r\n.company-tag-text {\r\n\tcolor: #ec4aa9;\r\n}\r\n\r\n.company-tag-delete {\r\n\tcolor: #ec4aa9;\r\n}\r\n\r\n.company-tag-add {\r\n\tborder: 1rpx dashed #fbcfe8;\r\n}\r\n\r\n.company-popup .popup-title {\r\n\tcolor: #ec4aa9;\r\n}\r\n\r\n.company-confirm-btn {\r\n\tbackground-color: #ec4aa9;\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/NewDemo/unimp/pages/card/profile.vue'\nwx.createPage(MiniProgramPage)"], "names": ["userService", "userInfoService", "uni"], "mappings": ";;;;AA2cA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,iBAAiB;AAAA;AAAA,MACjB,UAAU;AAAA;AAAA,MACV,UAAU;AAAA,QACT,aAAa;AAAA,QACb,MAAM,CAAE;AAAA,QACR,cAAc,CAAE;AAAA,QAChB,WAAW;AAAA;AAAA,QAEX,oBAAoB;AAAA;AAAA,QACpB,iBAAiB;AAAA,QACjB,UAAU;AAAA,QACV,kBAAkB;AAAA,QAClB,eAAe;AAAA,MACf;AAAA,MACD,aAAa;AAAA,QACZ,aAAa;AAAA,QACb,aAAa;AAAA,QACb,eAAe;AAAA,QACf,YAAY,CAAE;AAAA,QACd,gBAAgB;AAAA;AAAA,QAEhB,oBAAoB;AAAA;AAAA,QACpB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,mBAAmB;AAAA,QACnB,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,MACpB;AAAA,MACD,UAAU;AAAA,MACV,kBAAkB;AAAA;AAAA,MAElB,cAAc;AAAA,MACd,sBAAsB;AAAA;AAAA,MAEtB,oBAAoB;AAAA,MACpB,gBAAgB;AAAA,IACjB;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,cAAc;AACb,aAAO;AAAA,QACN,QAAQ,GAAG,KAAK,kBAAkB,EAAE;AAAA,MACrC;AAAA,IACA;AAAA,IACD,YAAY;AACX,aAAO;AAAA,QACN,WAAW,GAAG,KAAK,kBAAkB,EAAE;AAAA,MACxC;AAAA,IACD;AAAA,EACA;AAAA,EACD,MAAM,OAAO,QAAQ;AACpB,SAAK,mBAAkB;AAGvB,QAAI,UAAU,OAAO,SAAS,WAAW;AACxC,WAAK,WAAW;AAChB,YAAM,KAAK;WACL;AACN,WAAK,WAAW;AAChB,YAAM,KAAK;IACZ;AAAA,EACA;AAAA,EAED,SAAS;AAAA,EAER;AAAA,EAED,SAAS;AAAA,IACR,MAAM,eAAe;AAEpB,UAAIA,qBAAAA,YAAY,cAAc;AAC7B,YAAI;AACH,gBAAM,SAAS,MAAMC,yCAAgB;AAErC,cAAI,OAAO,WAAW,OAAO,MAAM;AAClC,kBAAM,aAAa,OAAO;AAG1B,iBAAK,WAAW;AAAA,cACf,aAAa,WAAW,eAAe,KAAK,SAAS,eAAe;AAAA,cACpE,MAAM,WAAW,QAAQ,KAAK,SAAS,QAAQ,CAAE;AAAA,cACjD,cAAc,WAAW,gBAAgB,KAAK,SAAS,gBAAgB,CAAE;AAAA,cACzE,WAAW,WAAW,aAAa,KAAK,SAAS,aAAa;AAAA;AAAA,cAE9D,oBAAoB,WAAW,uBAAuB,SAAY,WAAW,qBAAsB,KAAK,SAAS,uBAAuB,SAAY,KAAK,SAAS,qBAAqB;AAAA,cACvL,iBAAiB,WAAW,oBAAoB,SAAY,WAAW,kBAAmB,KAAK,SAAS,oBAAoB,SAAY,KAAK,SAAS,kBAAkB;AAAA,cACxK,UAAU,WAAW,aAAa,SAAY,WAAW,WAAY,KAAK,SAAS,aAAa,SAAY,KAAK,SAAS,WAAW;AAAA,cACrI,kBAAkB,WAAW,qBAAqB,SAAY,WAAW,mBAAoB,KAAK,SAAS,qBAAqB,SAAY,KAAK,SAAS,mBAAmB;AAAA,cAC7K,eAAe,WAAW,kBAAkB,SAAY,WAAW,gBAAiB,KAAK,SAAS,kBAAkB,SAAY,KAAK,SAAS,gBAAgB;AAAA;AAI/J,kBAAM,kBAAkBC,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AAC1D,kBAAM,kBAAkB;AAAA,cACvB,GAAG;AAAA,cACH,GAAG,KAAK;AAAA;AAETA,0BAAAA,MAAI,eAAe,YAAY,eAAe;AAE9C;AAAA,UACD;AAAA,QACC,SAAO,OAAO;AACfA,wBAAc,MAAA,MAAA,SAAA,iCAAA,mBAAmB,KAAK;AAAA,QACvC;AAAA,MACD;AAGA,WAAK,sBAAqB;AAAA,IAC1B;AAAA,IAED,wBAAwB;AACvB,YAAM,YAAYA,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AAGpD,WAAK,WAAW;AAAA,QACf,aAAa,UAAU,eAAe,KAAK,SAAS,eAAe;AAAA,QACnE,MAAM,UAAU,QAAQ,KAAK,SAAS,QAAQ,CAAE;AAAA,QAChD,cAAc,UAAU,gBAAgB,KAAK,SAAS,gBAAgB,CAAE;AAAA,QACxE,WAAW,UAAU,aAAa,KAAK,SAAS,aAAa;AAAA;AAAA,QAE7D,oBAAoB,UAAU,uBAAuB,SAAY,UAAU,qBAAsB,KAAK,SAAS,uBAAuB,SAAY,KAAK,SAAS,qBAAqB;AAAA,QACrL,iBAAiB,UAAU,oBAAoB,SAAY,UAAU,kBAAmB,KAAK,SAAS,oBAAoB,SAAY,KAAK,SAAS,kBAAkB;AAAA,QACtK,UAAU,UAAU,aAAa,SAAY,UAAU,WAAY,KAAK,SAAS,aAAa,SAAY,KAAK,SAAS,WAAW;AAAA,QACnI,kBAAkB,UAAU,qBAAqB,SAAY,UAAU,mBAAoB,KAAK,SAAS,qBAAqB,SAAY,KAAK,SAAS,mBAAmB;AAAA,QAC3K,eAAe,UAAU,kBAAkB,SAAY,UAAU,gBAAiB,KAAK,SAAS,kBAAkB,SAAY,KAAK,SAAS,gBAAgB;AAAA;IAE7J;AAAA,IAED,MAAM,kBAAkB;AAEvB,UAAIF,qBAAAA,YAAY,cAAc;AAC7B,YAAI;AACH,gBAAM,SAAS,MAAMC,yCAAgB;AAErC,cAAI,OAAO,WAAW,OAAO,MAAM;AAClC,kBAAM,aAAa,OAAO;AAG1B,iBAAK,cAAc;AAAA,cAClB,aAAa,WAAW,eAAe;AAAA,cACvC,aAAa,WAAW,eAAe;AAAA,cACvC,eAAe,WAAW,iBAAiB;AAAA,cAC3C,YAAY,WAAW,cAAc,CAAE;AAAA,cACvC,gBAAgB,WAAW,kBAAkB;AAAA;AAAA,cAE7C,oBAAoB,WAAW,uBAAuB,SAAY,WAAW,qBAAqB;AAAA,cAClG,iBAAiB,WAAW,oBAAoB,SAAY,WAAW,kBAAkB;AAAA,cACzF,iBAAiB,WAAW,oBAAoB,SAAY,WAAW,kBAAkB;AAAA,cACzF,mBAAmB,WAAW,sBAAsB,SAAY,WAAW,oBAAoB;AAAA,cAC/F,uBAAuB,WAAW,0BAA0B,SAAY,WAAW,wBAAwB;AAAA,cAC3G,oBAAoB,WAAW,uBAAuB,SAAY,WAAW,qBAAqB;AAAA;AAInG,kBAAM,kBAAkBC,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AAC1D,kBAAM,kBAAkB;AAAA,cACvB,GAAG;AAAA,cACH,GAAG,KAAK;AAAA;AAETA,0BAAAA,MAAI,eAAe,YAAY,eAAe;AAE9C;AAAA,UACD;AAAA,QACC,SAAO,OAAO;AACfA,wBAAc,MAAA,MAAA,SAAA,iCAAA,mBAAmB,KAAK;AAAA,QACvC;AAAA,MACD;AAGA,WAAK,yBAAwB;AAAA,IAC7B;AAAA,IAED,2BAA2B;AAC1B,YAAM,YAAYA,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AAGpD,WAAK,cAAc;AAAA,QAClB,aAAa,UAAU,eAAe;AAAA,QACtC,aAAa,UAAU,eAAe;AAAA,QACtC,eAAe,UAAU,iBAAiB;AAAA,QAC1C,YAAY,UAAU,cAAc,CAAE;AAAA,QACtC,gBAAgB,UAAU,kBAAkB;AAAA;AAAA,QAE5C,oBAAoB,UAAU,uBAAuB,SAAY,UAAU,qBAAqB;AAAA,QAChG,iBAAiB,UAAU,oBAAoB,SAAY,UAAU,kBAAkB;AAAA,QACvF,iBAAiB,UAAU,oBAAoB,SAAY,UAAU,kBAAkB;AAAA,QACvF,mBAAmB,UAAU,sBAAsB,SAAY,UAAU,oBAAoB;AAAA,QAC7F,uBAAuB,UAAU,0BAA0B,SAAY,UAAU,wBAAwB;AAAA,QACzG,oBAAoB,UAAU,uBAAuB,SAAY,UAAU,qBAAqB;AAAA;IAEjG;AAAA,IAED,qBAAqB;AAEpB,UAAI;AACH,cAAM,aAAaA,oBAAI;AACvB,cAAM,kBAAkB,WAAW,mBAAmB;AACtD,aAAK,kBAAkB;AAAA,MACtB,SAAO,OAAO;AACfA,sBAAY,MAAA,MAAA,OAAA,iCAAA,cAAc,KAAK;AAC/B,aAAK,kBAAkB;AAAA,MACxB;AAAA,IACA;AAAA,IAED,SAAS;AACRA,oBAAG,MAAC,aAAY;AAAA,IAChB;AAAA,IAED,kBAAkB;AACjB,WAAK,WAAW;AAChB,WAAK,eAAe;AAGpB,UAAI,CAAC,KAAK,SAAS,MAAM;AACxB,aAAK,SAAS,OAAO;MACtB;AAAA,IACA;AAAA,IAED,gBAAgB;AACf,WAAK,eAAe;AAAA,IACpB;AAAA,IAED,iBAAiB,GAAG;AACnB,WAAK,WAAW,EAAE,OAAO;AAAA,IACzB;AAAA,IAED,gBAAgB;AACf,YAAM,QAAQ,KAAK;AAEnB,UAAI,SAAS,MAAM,KAAO,KAAG,MAAM,KAAI,EAAG,SAAS,KAAK,MAAM,KAAI,EAAG,UAAU,GAAG;AAEjF,YAAI,CAAC,KAAK,SAAS,KAAK,SAAS,MAAM,KAAI,CAAE,GAAG;AAC/C,eAAK,SAAS,KAAK,KAAK,MAAM,KAAI,CAAE;AACpC,eAAK,cAAa;AAClBA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,eACK;AACNA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD,WAAW,SAAS,MAAM,KAAI,EAAG,SAAS,GAAG;AAC5CA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,aACK;AACNA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAED,UAAU,OAAO;AAChB,WAAK,SAAS,KAAK,OAAO,OAAO,CAAC;AAAA,IAClC;AAAA,IAED,0BAA0B;AACzB,WAAK,mBAAmB;AACxB,WAAK,uBAAuB;AAAA,IAC5B;AAAA,IAED,wBAAwB;AACvB,WAAK,uBAAuB;AAAA,IAC5B;AAAA,IAED,wBAAwB;AACvB,YAAM,QAAQ,KAAK;AACnB,UAAI,SAAS,MAAM,KAAK,KAAK,MAAM,UAAU,IAAI;AAChD,aAAK,SAAS,aAAa,KAAK,MAAM,KAAI,CAAE;AAC5C,aAAK,sBAAqB;AAAA,iBAChB,MAAM,SAAS,IAAI;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,aACK;AACNA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAED,kBAAkB,OAAO;AACxB,WAAK,SAAS,aAAa,OAAO,OAAO,CAAC;AAAA,IAC1C;AAAA,IAED,wBAAwB;AACvB,WAAK,iBAAiB;AACtB,WAAK,qBAAqB;AAAA,IAC1B;AAAA,IAED,sBAAsB;AACrB,WAAK,qBAAqB;AAAA,IAC1B;AAAA,IAED,sBAAsB;AACrB,YAAM,QAAQ,KAAK;AACnB,UAAI,SAAS,MAAM,KAAK,KAAK,MAAM,UAAU,IAAI;AAEhD,YAAI,CAAC,KAAK,YAAY,WAAW,SAAS,MAAM,KAAI,CAAE,GAAG;AACxD,eAAK,YAAY,WAAW,KAAK,MAAM,KAAI,CAAE;AAC7C,eAAK,oBAAmB;AAAA,eAClB;AACNA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,iBACU,MAAM,SAAS,IAAI;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,aACK;AACNA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAED,gBAAgB,OAAO;AACtB,WAAK,YAAY,WAAW,OAAO,OAAO,CAAC;AAAA,IAC3C;AAAA,IAED,MAAM,cAAc;AAEnB,UAAI,CAACF,qBAAAA,YAAY,cAAc;AAC9BE,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,SAAS,CAAC,QAAQ;AACjB,gBAAI,IAAI,SAAS;AAChBA,4BAAAA,MAAI,WAAW;AAAA,gBACd,KAAK;AAAA,cACN,CAAC;AAAA,YACF;AAAA,UACD;AAAA,QACD,CAAC;AACD;AAAA,MACD;AAGA,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,YAAM,cAAcA,cAAG,MAAC,eAAe,aAAa,KAAK;AACzD,YAAM,mBAAmBA,cAAAA,MAAI,eAAe,kBAAkB;AAG9D,UAAI,YAAY;AAChB,UAAI,kBAAkB;AACrB,cAAM,MAAM,oBAAI;AAEhB,cAAM,gBAAgB,iBAAiB,QAAQ,OAAO,GAAG;AACzD,cAAM,aAAa,IAAI,KAAK,aAAa;AACzC,oBAAY,MAAM;AAAA,MACnB;AAGA,YAAM,gBAAgB,YAAY,eAAe,KAAK,CAAC;AAEvD,UAAI,CAAC,eAAe;AACnBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,SAAS,KAAK,aAAa,YAAY,wBAAwB;AAAA,UAC/D,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,SAAS,CAAC,QAAQ;AACjB,gBAAI,IAAI,SAAS;AAEhBA,4BAAAA,MAAI,WAAW;AAAA,gBACd,KAAK;AAAA,cACN,CAAC;AAAA,YACF;AAAA,UACD;AAAA,QACD,CAAC;AACD;AAAA,MACD;AAGAA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAED,UAAI;AAEH,cAAM,kBAAkBA,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AAC1D,YAAI;AAGJ,YAAI,KAAK,aAAa,WAAW;AAEhC,4BAAkB;AAAA,YACjB,GAAG;AAAA,YACH,aAAa,KAAK,YAAY;AAAA,YAC9B,aAAa,KAAK,YAAY;AAAA,YAC9B,eAAe,KAAK,YAAY;AAAA,YAChC,YAAY,KAAK,YAAY;AAAA,YAC7B,gBAAgB,KAAK,YAAY;AAAA;AAAA,YAEjC,oBAAoB,KAAK,YAAY;AAAA,YACrC,iBAAiB,KAAK,YAAY;AAAA,YAClC,iBAAiB,KAAK,YAAY;AAAA,YAClC,mBAAmB,KAAK,YAAY;AAAA,YACpC,uBAAuB,KAAK,YAAY;AAAA,YACxC,oBAAoB,KAAK,YAAY;AAAA;eAEhC;AAEN,4BAAkB;AAAA,YACjB,GAAG;AAAA,YACH,aAAa,KAAK,SAAS;AAAA,YAC3B,MAAM,KAAK,SAAS;AAAA,YACpB,cAAc,KAAK,SAAS;AAAA,YAC5B,WAAW,KAAK,SAAS;AAAA;AAAA,YAEzB,oBAAoB,KAAK,SAAS;AAAA,YAClC,iBAAiB,KAAK,SAAS;AAAA,YAC/B,UAAU,KAAK,SAAS;AAAA,YACxB,kBAAkB,KAAK,SAAS;AAAA,YAChC,eAAe,KAAK,SAAS;AAAA;QAE/B;AAKAA,sBAAAA,MAAI,eAAe,YAAY,eAAe;AAG9C,cAAM,SAAS,MAAMD,yBAAAA,gBAAgB,eAAe,eAAe;AAEnE,YAAI,OAAO,SAAS;AAEnBC,wBAAG,MAAC,YAAW;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,YACV,SAAS,MAAM;AAEd,yBAAW,MAAM;AAChBA,8BAAG,MAAC,aAAY;AAAA,cAChB,GAAE,IAAI;AAAA,YACR;AAAA,UACD,CAAC;AAAA,eACK;AAENA,wBAAG,MAAC,YAAW;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,OAAO,WAAW;AAAA,YACzB,MAAM;AAAA,YACN,UAAU;AAAA,UACX,CAAC;AAAA,QACF;AAAA,MAEC,SAAO,OAAO;AACfA,sBAAc,MAAA,MAAA,SAAA,iCAAA,aAAa,KAAK;AAEhCA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACX,CAAC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACz6BA,GAAG,WAAW,eAAe;"}