{"version": 3, "file": "edit.js", "sources": ["pages/card/edit.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.45.2025010502 (1)/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY2FyZC9lZGl0LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"edit-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"navbarStyle\">\n      <view class=\"navbar-left\">\n        <view class=\"back-btn\" @click=\"goBack\">\n          <text class=\"back-icon\">←</text>\n          <text class=\"back-text\">返回</text>\n        </view>\n      </view>\n      <view class=\"navbar-title\">编辑名片</view>\n    </view>\n    \n    <!-- 顶部区域（包含名片预览和选项卡） -->\n    <view class=\"top-section\" :style=\"{ paddingTop: (statusBarHeight + 45) + 'px' }\">\n      <!-- 名片预览 -->\n      <view class=\"card-preview\">\n        <view \n          class=\"classic-card-v2\" \n          :class=\"{'classic-card-v2-with-bg-image': form.customStyle.backgroundType === 'image' && form.customStyle.backgroundImage}\"\n          :style=\"Object.assign(cardPreviewMargin, cardPreviewStyle)\"\n        >\n          <!-- 当使用背景图片时，添加一个覆盖层提高文本可读性 -->\n          <view \n            v-if=\"form.customStyle.backgroundType === 'image' && form.customStyle.backgroundImage\" \n            class=\"classic-card-v2-overlay\"\n          ></view>\n          \n          <!-- 背景图片使用独立image组件 -->\n          <image \n            v-if=\"form.customStyle.backgroundType === 'image' && form.customStyle.backgroundImage\" \n            :src=\"form.customStyle.backgroundImage\" \n            class=\"classic-card-v2-bg-image\" \n            mode=\"aspectFill\"\n          ></image>\n          \n          <view class=\"classic-card-v2-content\">\n            <view class=\"classic-card-v2-header\">\n              <view class=\"classic-card-v2-info\">\n                <view class=\"classic-card-v2-name-row\">\n                  <text class=\"classic-card-v2-name\" :style=\"{color: form.customStyle.textColor}\">{{ form.name }}</text>\n                  <text class=\"classic-card-v2-position\" :style=\"positionStyle\">{{ form.position }}</text>\n                </view>\n                <text class=\"classic-card-v2-company\" :style=\"{color: form.customStyle.textColor}\">{{ form.company }}</text>\n              </view>\n              <view class=\"classic-card-v2-avatar-box\">\n                <image class=\"classic-card-v2-avatar\" :src=\"getDisplayAvatar(form.avatar)\" mode=\"aspectFill\" />\n                <image class=\"classic-card-v2-cert\" src=\"/static/icons/rz.png\" />\n              </view>\n            </view>\n            <view class=\"classic-card-v2-divider\" :style=\"{background: form.customStyle.textColor === '#ffffff' ? 'rgba(255,255,255,0.2)' : '#f0f0f0'}\"></view>\n            <view class=\"classic-card-v2-contact-list\">\n              <view class=\"classic-card-v2-contact-item\">\n                <image class=\"classic-card-v2-icon\" src=\"/static/icons/phone.png\" mode=\"aspectFit\" :style=\"iconStyle\" />\n                <text class=\"classic-card-v2-label\" :style=\"{color: form.customStyle.textColor === '#ffffff' ? 'rgba(255,255,255,0.7)' : '#888'}\">电话：</text>\n                <text class=\"classic-card-v2-value\" :style=\"{color: form.customStyle.textColor}\">{{ form.phone }}</text>\n              </view>\n              <view class=\"classic-card-v2-contact-item\">\n                <image class=\"classic-card-v2-icon\" src=\"/static/icons/chat.png\" mode=\"aspectFit\" :style=\"iconStyle\" />\n                <text class=\"classic-card-v2-label\" :style=\"{color: form.customStyle.textColor === '#ffffff' ? 'rgba(255,255,255,0.7)' : '#888'}\">微信：</text>\n                <text class=\"classic-card-v2-value\" :style=\"{color: form.customStyle.textColor}\">{{ form.wechat }}</text>\n              </view>\n              <view class=\"classic-card-v2-contact-item\">\n                <image class=\"classic-card-v2-icon\" src=\"/static/icons/email.png\" mode=\"aspectFit\" :style=\"iconStyle\" />\n                <text class=\"classic-card-v2-label\" :style=\"{color: form.customStyle.textColor === '#ffffff' ? 'rgba(255,255,255,0.7)' : '#888'}\">邮箱：</text>\n                <text class=\"classic-card-v2-value\" :style=\"{color: form.customStyle.textColor}\">{{ form.email }}</text>\n              </view>\n              <view class=\"classic-card-v2-contact-item\">\n                <image class=\"classic-card-v2-icon\" src=\"/static/icons/location.png\" mode=\"aspectFit\" :style=\"iconStyle\" />\n                <text class=\"classic-card-v2-label\" :style=\"{color: form.customStyle.textColor === '#ffffff' ? 'rgba(255,255,255,0.7)' : '#888'}\">地址：</text>\n                <text class=\"classic-card-v2-value\" :style=\"{color: form.customStyle.textColor}\">{{ form.address }}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 固定在名片预览下方的选项卡 -->\n      <view class=\"fixed-tab-bar\" :style=\"{ padding: `0 ${adaptiveSpacing.sm}px` }\">\n        <view class=\"tab-bar\">\n          <view :class=\"['tab-item', {active: activeTab === 0}]\" @click=\"activeTab = 0\">联系信息</view>\n          <view :class=\"['tab-item', {active: activeTab === 1}]\" @click=\"activeTab = 1\">个性定制</view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 内容区域 - 使用计算样式设置上边距 -->\n    <view class=\"content-area\" :style=\"contentStyle\">\n      <!-- 移除会员提示条 -->\n\n      <view v-show=\"activeTab === 0\" class=\"form-section\" :style=\"{ margin: `${adaptiveSpacing.xs}px ${adaptiveSpacing.sm}px ${adaptiveSpacing.md}px` }\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">联系信息</text>\n          <view class=\"section-line\"></view>\n        </view>\n        \n        <!-- 头像选择移到最上方 -->\n        <view class=\"form-item avatar-item\">\n          <text class=\"form-label\">头像</text>\n          <view class=\"avatar-selector\">\n            <image class=\"avatar-preview\" :src=\"getDisplayAvatar(form.avatar)\" mode=\"aspectFill\"></image>\n            <view class=\"avatar-buttons\">\n              <!-- #ifdef MP-WEIXIN -->\n              <!-- 微信小程序中使用微信头像选择 -->\n              <button class=\"avatar-change-btn\" open-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\">\n                <text class=\"btn-text\">更换头像</text>\n              </button>\n              <!-- #endif -->\n\n              <!-- #ifndef MP-WEIXIN -->\n              <!-- 非微信环境使用ActionSheet -->\n              <view class=\"avatar-change-btn\" @click=\"showAvatarActionSheet\">\n                <text class=\"btn-text\">更换头像</text>\n              </view>\n              <!-- #endif -->\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"form-group\">\n          <view class=\"form-item modern-form-item\">\n            <text class=\"form-label\">姓名</text>\n            <view class=\"input-container\">\n              <input class=\"form-input\" v-model=\"form.name\" placeholder=\"请输入姓名\" />\n            </view>\n          </view>\n          <view class=\"form-item modern-form-item\">\n            <text class=\"form-label\">职位</text>\n            <view class=\"input-container\">\n              <input class=\"form-input\" v-model=\"form.position\" placeholder=\"请输入职位\" />\n            </view>\n          </view>\n          <view class=\"form-item modern-form-item\">\n            <text class=\"form-label\">公司</text>\n            <view class=\"input-container\">\n              <input class=\"form-input\" v-model=\"form.company\" placeholder=\"请输入公司\" />\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"form-divider\"></view>\n        \n        <view class=\"form-group\">\n          <view class=\"form-item modern-form-item\">\n            <text class=\"form-label\">电话</text>\n            <view class=\"input-container\">\n              <image class=\"input-icon\" src=\"/static/icons/phone.png\" mode=\"aspectFit\" />\n              <input\n                class=\"form-input with-icon\"\n                v-model=\"form.phone\"\n                placeholder=\"请输入11位手机号\"\n                type=\"number\"\n                maxlength=\"11\"\n                @input=\"onPhoneInput\"\n              />\n              <view v-if=\"form.phone\" class=\"clear-btn\" @click=\"clearPhone\">\n                <text class=\"clear-icon\">×</text>\n              </view>\n            </view>\n            <text v-if=\"phoneError\" class=\"error-tip\">{{ phoneError }}</text>\n          </view>\n          <view class=\"form-item modern-form-item\">\n            <text class=\"form-label\">微信</text>\n            <view class=\"input-container\">\n              <image class=\"input-icon\" src=\"/static/icons/chat.png\" mode=\"aspectFit\" />\n              <input class=\"form-input with-icon\" v-model=\"form.wechat\" placeholder=\"请输入微信号\" />\n              <view v-if=\"form.wechat\" class=\"clear-btn\" @click=\"clearWechat\">\n                <text class=\"clear-icon\">×</text>\n              </view>\n            </view>\n          </view>\n          <view class=\"form-item modern-form-item\">\n            <text class=\"form-label\">邮箱</text>\n            <view class=\"input-container\">\n              <image class=\"input-icon\" src=\"/static/icons/email.png\" mode=\"aspectFit\" />\n              <input\n                class=\"form-input with-icon\"\n                v-model=\"form.email\"\n                placeholder=\"请输入邮箱地址\"\n                type=\"email\"\n                @blur=\"validateEmail\"\n              />\n              <view v-if=\"form.email\" class=\"clear-btn\" @click=\"clearEmail\">\n                <text class=\"clear-icon\">×</text>\n              </view>\n            </view>\n            <text v-if=\"emailError\" class=\"error-tip\">{{ emailError }}</text>\n          </view>\n          \n          <!-- 优化后的地址设置UI -->\n          <view class=\"form-item modern-form-item address-item\">\n            <text class=\"form-label\">地址</text>\n            <view class=\"input-container\">\n              <image class=\"input-icon\" src=\"/static/icons/location.png\" mode=\"aspectFit\" />\n              <view class=\"address-preview\" @click=\"showAddressSelector\">\n                <text class=\"address-text\">{{ form.address || '请选择地址' }}</text>\n                <text class=\"address-icon\">></text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <view v-show=\"activeTab === 1\" class=\"diy-container\" :style=\"{ padding: `${adaptiveSpacing.xs}px ${adaptiveSpacing.sm}px` }\">\n        <!-- 名片风格 -->\n        <view class=\"diy-card\">\n          <view class=\"diy-card-header\">\n            <text class=\"diy-card-title\">名片风格</text>\n          </view>\n          <view class=\"diy-card-content\">\n            <scroll-view class=\"style-scroll-view\" scroll-x=\"true\" show-scrollbar=\"false\">\n              <view class=\"style-scroll-content\">\n                <view \n                  v-for=\"(item, idx) in cardStyles\" \n                  :key=\"item.name\" \n                  :class=\"['style-option-item', {active: form.customStyle.cardStyleIndex === idx}]\" \n                  @click=\"applyCardStyle(idx)\"\n                >\n                  <view class=\"style-option-preview\" :style=\"item.previewStyle\">\n                    <view class=\"style-preview-content\">\n                      <view class=\"style-preview-avatar\"></view>\n                      <view class=\"style-preview-info\">\n                        <view class=\"style-preview-name\"></view>\n                        <view class=\"style-preview-position\"></view>\n                      </view>\n                    </view>\n                  </view>\n                  <text class=\"style-option-label\">{{ item.name }}</text>\n                </view>\n              </view>\n            </scroll-view>\n          </view>\n        </view>\n\n        <!-- 背景 -->\n        <view class=\"diy-card\">\n          <view class=\"diy-card-header\">\n            <text class=\"diy-card-title\">背景</text>\n          </view>\n          <view class=\"diy-card-content\">\n            <view class=\"diy-option-group\">\n              <text class=\"diy-option-group-title\">纯色</text>\n              <view class=\"diy-option-row\">\n                <view \n                  v-for=\"(color, idx) in bgColors\" \n                  :key=\"color\" \n                  :style=\"{background: color}\" \n                  :class=\"['diy-color-block', {active: form.customStyle.backgroundType === 'color' && form.customStyle.backgroundColor === color}]\" \n                  @click=\"applyBgColor(color)\"\n                ></view>\n              </view>\n            </view>\n            \n            <view class=\"diy-option-group\">\n              <text class=\"diy-option-group-title\">渐变</text>\n              <view class=\"diy-option-row\">\n                <view \n                  v-for=\"(gradient, idx) in gradients\" \n                  :key=\"idx\" \n                  :style=\"{background: gradient}\" \n                  :class=\"['diy-color-block', {active: form.customStyle.backgroundType === 'gradient' && form.customStyle.gradientIndex === idx}]\" \n                  @click=\"applyGradient(idx)\"\n                ></view>\n              </view>\n            </view>\n            \n            <view class=\"diy-option-group\">\n              <text class=\"diy-option-group-title\">图片</text>\n              <view class=\"diy-option-row\">\n                <view \n                  class=\"diy-upload-btn\" \n                  @click=\"uploadBgImage\"\n                >\n                  <text class=\"diy-upload-icon\">+</text>\n                  <text class=\"diy-upload-text\">上传</text>\n                </view>\n                <view \n                  v-for=\"(img, idx) in bgImages\" \n                  :key=\"img\" \n                  :class=\"['diy-image-block', {active: form.customStyle.backgroundType === 'image' && form.customStyle.backgroundImage === img}]\" \n                  @click=\"applyBgImage(img)\"\n                >\n                  <image :src=\"img\" mode=\"aspectFill\" class=\"diy-image-preview\"></image>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n\n        <!-- 字体颜色 -->\n        <view class=\"diy-card\">\n          <view class=\"diy-card-header\">\n            <text class=\"diy-card-title\">字体颜色</text>\n          </view>\n          <view class=\"diy-card-content\">\n            <view class=\"diy-option-row\">\n              <view \n                v-for=\"(color, idx) in textColors\" \n                :key=\"color\" \n                :style=\"{background: color}\" \n                :class=\"['diy-color-block', {active: form.customStyle.textColor === color}]\" \n                @click=\"applyTextColor(color)\"\n              >\n                <view v-if=\"color === '#ffffff'\" class=\"diy-color-block-border\"></view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 添加底部安全区域，防止保存按钮遮挡内容 -->\n      <view class=\"bottom-safe-area\"></view>\n    </view>\n    \n    <!-- 悬浮保存按钮 -->\n    <button class=\"save-btn-float\" :class=\"{show: showSaveBtn}\" @click=\"saveCard\">\n      {{ activeTab === 0 ? '保存联系信息' : '保存个性定制' }}\n    </button>\n\n\n\n    <!-- 地址选择弹窗 -->\n    <view class=\"address-modal\" v-if=\"showAddressModal\">\n      <view class=\"address-modal-mask\" @click=\"showAddressModal = false\"></view>\n      <view class=\"address-modal-content\">\n        <view class=\"address-modal-header\">\n          <text class=\"address-modal-title\">选择地址</text>\n          <text class=\"address-modal-close\" @click=\"showAddressModal = false\">×</text>\n        </view>\n\n        <view class=\"address-tabs\">\n          <view :class=\"['address-tab', {active: addressTab === 0}]\" @click=\"addressTab = 0\">手动输入</view>\n          <view :class=\"['address-tab', {active: addressTab === 1}]\" @click=\"addressTab = 1\">地图选点</view>\n        </view>\n\n        <view class=\"address-content\">\n          <!-- 手动输入 -->\n          <view v-if=\"addressTab === 0\" class=\"address-manual\">\n            <view class=\"address-picker-group\">\n              <picker mode=\"region\" @change=\"onRegionChange\" :value=\"regionValue\">\n                <view class=\"region-picker\">\n                  <text>{{ regionText || '选择省/市/区' }}</text>\n                  <text class=\"picker-arrow\">></text>\n                </view>\n              </picker>\n            </view>\n            <input class=\"address-detail-input\" v-model=\"addressDetail\" placeholder=\"详细地址，如街道、门牌号等\" />\n          </view>\n\n          <!-- 地图选点 -->\n          <view v-if=\"addressTab === 1\" class=\"map-container\">\n            <view class=\"map-placeholder\">\n              <image class=\"map-image\" src=\"/static/icons/location.png\" mode=\"aspectFit\" />\n              <text class=\"map-tip\">点击获取当前位置</text>\n            </view>\n            <button class=\"map-btn\" @click=\"getLocation\">获取当前位置</button>\n          </view>\n        </view>\n\n        <view class=\"address-modal-footer\">\n          <button class=\"address-cancel-btn\" @click=\"showAddressModal = false\">取消</button>\n          <button class=\"address-confirm-btn\" @click=\"confirmAddress\">确认</button>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport userService from '../../services/userService.js';\nimport userInfoService from '../../services/userInfoService.js';\nimport { getData } from '../../utils/storage.js';\nimport { TOKEN_KEY, USER_INFO_KEY, API_BASE_URL } from '../../utils/config.js';\n\nexport default {\n  data() {\n    return {\n      statusBarHeight: 44,\n      activeTab: 0,\n      showSaveBtn: true,\n      lastScrollTop: 0,\n\n\n      showAddressModal: false,\n      addressTab: 0,\n      regionValue: ['', '', ''],\n      regionText: '',\n      addressDetail: '',\n      topSectionHeight: 0, // 将默认值设为0，避免初始大间距\n      contentPaddingTop: 0, // 添加新属性用于设置内容区域padding-top\n      screenWidth: 375,\n      screenHeight: 667,\n      deviceRatio: 1,\n      isPremiumMember: false, // 添加会员状态标志\n      phoneError: '', // 电话号码错误提示\n      emailError: '', // 邮箱错误提示\n      form: {\n        avatar: '/static/default-avatar.png',\n        name: '徐敏',\n        position: '销售专员',\n        company: '德萨大数据股份有限公司',\n        phone: '18596320120',\n        email: 'www.1099458780.com',\n        wechat: 'xumin_wx',\n        address: '山东省庄市滕州市北辛西路2000号',\n        customStyle: {\n          backgroundType: 'color',\n          gradientIndex: 0,\n          backgroundColor: '#fff',\n          backgroundImage: '',\n          textColor: '#222',\n          borderRadius: 'small',\n          borderRadiusIndex: 1,\n          cardStyleIndex: 0\n        }\n      },\n      cardStyles: [\n        { \n          name: '经典商务', \n          previewStyle: 'background: #ffffff; border: 2px solid #4a6ef2; box-shadow: 0 4px 10px rgba(0,0,0,0.15);'\n        },\n        { \n          name: '科技蓝', \n          previewStyle: 'background: linear-gradient(90deg, #4a6ef2 0%, #6fc3ff 100%);'\n        },\n        { \n          name: '简约灰', \n          previewStyle: 'background: #f5f7fa; border: 1px solid #eee;'\n        },\n        { \n          name: '暗夜黑', \n          previewStyle: 'background: #333333; color: #ffffff;'\n        },\n        { \n          name: '活力橙', \n          previewStyle: 'background: linear-gradient(90deg, #f2a54a 0%, #ffd36f 100%);'\n        },\n        { \n          name: '森林绿', \n          previewStyle: 'background: linear-gradient(90deg, #2c9d6d 0%, #6fd39f 100%);'\n        },\n        { \n          name: '浪漫粉', \n          previewStyle: 'background: linear-gradient(90deg, #f25a8e 0%, #ff9cc0 100%);'\n        },\n        { \n          name: '高端黑金', \n          previewStyle: 'background: linear-gradient(90deg, #333333 0%, #666666 100%); border: 1px solid #d4af37;'\n        },\n        { \n          name: '紫色梦幻', \n          previewStyle: 'background: linear-gradient(90deg, #9d2c8d 0%, #d36fc6 100%);'\n        },\n        { \n          name: '复古棕', \n          previewStyle: 'background: #8b5a2b; color: #f5deb3;'\n        },\n        { \n          name: '海洋蓝', \n          previewStyle: 'background: linear-gradient(135deg, #1a2980 0%, #26d0ce 100%);'\n        },\n        { \n          name: '珊瑚红', \n          previewStyle: 'background: #FF6F61; color: #ffffff;'\n        },\n        { \n          name: '薄荷绿', \n          previewStyle: 'background: #98ff98; color: #006400; border: 1px solid #7ccc7c;'\n        },\n        { \n          name: '太空灰', \n          previewStyle: 'background: linear-gradient(135deg, #232526 0%, #414345 100%); color: #ffffff;'\n        },\n        { \n          name: '日落橙', \n          previewStyle: 'background: linear-gradient(135deg, #ff512f 0%, #f09819 100%); color: #ffffff;'\n        },\n        { \n          name: '翡翠绿', \n          previewStyle: 'background: linear-gradient(135deg, #43c6ac 0%, #191654 100%); color: #ffffff;'\n        },\n        { \n          name: '商务蓝', \n          previewStyle: 'background: #1e3c72; color: #ffffff; border: 2px solid #2a5298;'\n        },\n        { \n          name: '玫瑰金', \n          previewStyle: 'background: linear-gradient(135deg, #e6b980 0%, #eacda3 100%); color: #8b4513;'\n        },\n        { \n          name: '星空紫', \n          previewStyle: 'background: linear-gradient(135deg, #5f2c82 0%, #49a09d 100%); color: #ffffff;'\n        }\n      ],\n      bgColors: [\n        '#ffffff', // 纯白\n        '#f5f7fa', // 浅灰\n        '#e6f0ff', // 浅蓝\n        '#f0f9eb', // 浅绿\n        '#fef0f0', // 浅红\n        '#fdf6ec', // 浅黄\n        '#f0f2f5', // 银灰\n        '#2c5aa0', // 深蓝\n        '#333333', // 深灰\n        '#67c23a', // 绿色\n        '#1e3c72', // 商务蓝\n        '#8b5a2b', // 复古棕\n        '#FF6F61', // 珊瑚红\n        '#98ff98', // 薄荷绿\n        '#5f2c82', // 深紫\n        '#b8860b', // 暗金色\n        '#20b2aa', // 浅绿松石\n        '#4b0082', // 靛青\n        '#800000', // 栗色\n        '#008080'  // 蓝绿色\n      ],\n      gradients: [\n        'linear-gradient(90deg, #4a6ef2 0%, #6fc3ff 100%)', // 蓝色渐变\n        'linear-gradient(90deg, #f2a54a 0%, #ffd36f 100%)', // 橙色渐变\n        'linear-gradient(90deg, #2c9d6d 0%, #6fd39f 100%)', // 绿色渐变\n        'linear-gradient(90deg, #f25a8e 0%, #ff9cc0 100%)', // 粉色渐变\n        'linear-gradient(90deg, #333333 0%, #666666 100%)', // 灰色渐变\n        'linear-gradient(90deg, #9d2c8d 0%, #d36fc6 100%)', // 紫色渐变\n        'linear-gradient(135deg, #1a2980 0%, #26d0ce 100%)', // 海洋蓝渐变\n        'linear-gradient(135deg, #ff512f 0%, #f09819 100%)', // 日落橙渐变\n        'linear-gradient(135deg, #43c6ac 0%, #191654 100%)', // 翡翠绿渐变\n        'linear-gradient(135deg, #e6b980 0%, #eacda3 100%)', // 玫瑰金渐变\n        'linear-gradient(135deg, #5f2c82 0%, #49a09d 100%)', // 星空紫渐变\n        'linear-gradient(135deg, #232526 0%, #414345 100%)', // 太空灰渐变\n        'linear-gradient(135deg, #2980b9 0%, #6dd5fa 100%)', // 清新天蓝\n        'linear-gradient(135deg, #8e2de2 0%, #4a00e0 100%)', // 皇家紫\n        'linear-gradient(135deg, #f953c6 0%, #b91d73 100%)', // 热情玫红\n        'linear-gradient(135deg, #00b09b 0%, #96c93d 100%)', // 春日绿\n        'linear-gradient(135deg, #c31432 0%, #240b36 100%)', // 深红暗紫\n        'linear-gradient(135deg, #3a1c71 0%, #d76d77 50%, #ffaf7b 100%)', // 三色渐变\n        'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', // 电光蓝\n        'linear-gradient(135deg, #7F7FD5 0%, #86A8E7 50%, #91EAE4 100%)' // 薰衣草蓝\n      ],\n      bgImages: ['/static/template/1.jpg', '/static/template/2.png'],\n      textColors: [\n        '#222222', // 黑色\n        '#ffffff', // 白色\n        '#4a6ef2', // 蓝色\n        '#f2a54a', // 橙色\n        '#2c9d6d', // 绿色\n        '#f25a8e', // 粉色\n        '#9d2c8d', // 紫色\n        '#d4af37', // 金色\n        '#1e3c72', // 商务蓝\n        '#8b5a2b', // 复古棕\n        '#FF6F61', // 珊瑚红\n        '#006400', // 深绿色\n        '#800000', // 栗色\n        '#4b0082', // 靛青\n        '#f5deb3', // 小麦色\n        '#00b09b', // 薄荷绿\n        '#c31432', // 深红色\n        '#3a1c71', // 深紫色\n        '#4facfe', // 电光蓝\n        '#7F7FD5', // 薰衣草蓝\n        '#b91d73', // 玫红\n        '#96c93d', // 嫩绿\n        '#240b36', // 暗紫\n        '#ffaf7b', // 杏橙\n        '#00f2fe', // 亮青\n        '#2E8B57', // 海绿色\n        '#CD5C5C', // 印第安红\n        '#191970', // 午夜蓝\n        '#DAA520', // 金菊色\n        '#20B2AA'  // 浅海绿\n      ]\n    }\n  },\n  computed: {\n    navbarStyle() {\n      return {\n        height: `${this.statusBarHeight + 45}px`,\n        paddingTop: `${this.statusBarHeight}px`,\n        zIndex: 1001 // 确保导航栏在最上层\n      }\n    },\n    // 计算实际背景样式\n    computedBackgroundStyle() {\n      const style = {};\n      \n      // 根据背景类型设置样式，确保只应用一种背景\n      if (this.form.customStyle.backgroundType === 'solid' || this.form.customStyle.backgroundType === 'color') {\n        // 纯色背景\n        style.background = this.form.customStyle.backgroundColor || '#ffffff';\n      } else if (this.form.customStyle.backgroundType === 'gradient') {\n        try {\n          // 渐变背景\n          const gradientIndex = parseInt(this.form.customStyle.gradientIndex || 0);\n          \n          // 确保索引有效\n          if (gradientIndex >= 0 && gradientIndex < this.gradients.length) {\n            const gradient = this.gradients[gradientIndex];\n            if (gradient) {\n              style.background = gradient;\n            } else {\n              style.background = '#ffffff'; // 回退到默认背景\n            }\n          } else {\n            style.background = '#ffffff'; // 回退到默认背景\n          }\n        } catch (error) {\n          style.background = '#ffffff'; // 回退到默认背景\n        }\n      } else if (this.form.customStyle.backgroundType === 'image' && this.form.customStyle.backgroundImage) {\n        // 图片背景 - 使用独立的image组件，这里不设置background-image\n        // 为了避免冲突，我们只设置一个透明背景\n        style.background = 'transparent';\n      } else {\n        // 默认背景\n        style.background = '#ffffff';\n      }\n      \n      return style;\n    },\n    iconStyle() {\n      // 根据文字颜色调整图标颜色\n      if (this.form.customStyle.textColor === '#ffffff') {\n        // 白色文字时，图标也显示为白色\n        return {\n          filter: 'brightness(0) invert(1)',\n          opacity: 0.9\n        };\n      } else {\n        // 深色文字时，保持图标原色\n        return {\n          opacity: 0.8\n        };\n      }\n    },\n    cardPreviewStyle() {\n      // 使用计算好的背景样式\n      const style = {...this.computedBackgroundStyle};\n      \n\n      \n      // 圆角\n      style.borderRadius = this.form.customStyle.borderRadiusIndex === undefined ? '12rpx' : \n                          ['12rpx', '24rpx', '0'][this.form.customStyle.borderRadiusIndex];\n      \n      // 阴影\n      if (this.form.customStyle.shadow) {\n        style.boxShadow = '0 4rpx 16rpx rgba(0,0,0,0.1)';\n      }\n      \n      return style;\n    },\n    cardTextStyle() {\n      return {\n        color: this.form.customStyle.textColor || '#222222',\n        fontSize: this.fontSizes[this.form.customStyle.fontSizeIndex || 1].previewSize\n      };\n    },\n    avatarPreviewStyle() {\n      const style = {\n        borderRadius: this.avatarStyles[this.form.customStyle.avatarStyleIndex || 0]\n      };\n      \n      if (this.form.customStyle.avatarBorder) {\n        style.border = '2rpx solid #ffffff';\n      }\n      \n      return style;\n    },\n    adaptiveSpacing() {\n      // 基于屏幕宽度计算基础间距单位，减小系数从25改为30\n      const baseSpacing = this.screenWidth / 30;\n      return {\n        xs: baseSpacing * 0.4, // 超小间距（减小系数）\n        sm: baseSpacing * 0.7, // 小间距（减小系数）\n        md: baseSpacing * 0.9, // 中等间距（减小系数）\n        lg: baseSpacing * 1.2, // 大间距（减小系数）\n        xl: baseSpacing * 1.5 // 超大间距（减小系数）\n      };\n    },\n    cardPreviewMargin() {\n      const spacing = this.adaptiveSpacing;\n      return {\n        margin: `${spacing.md}px ${spacing.sm}px ${spacing.md}px ${spacing.sm}px`\n      };\n    },\n    contentStyle() {\n      // 如果有contentPaddingTop值（由updateContentPadding方法设置），则优先使用\n      if (this.contentPaddingTop > 0) {\n        return {\n          paddingTop: `${this.contentPaddingTop}px`\n        };\n      }\n      \n      // 否则使用计算值\n      const sectionHeight = this.topSectionHeight || 400;\n      // 增加减少量，让内容更靠近选项卡\n      const reductionRatio = this.screenHeight < 700 ? 0.25 : 0.2;\n      const paddingReduction = sectionHeight * reductionRatio;\n      \n      return {\n        paddingTop: `${this.statusBarHeight + 45 + sectionHeight - paddingReduction}px`\n      };\n    },\n    // 计算职位样式\n    positionStyle() {\n      const textColor = this.form.customStyle.textColor;\n      \n      // 根据文字颜色和背景类型决定职位样式\n      if (textColor === '#ffffff') {\n        // 白色文字时，使用半透明白色背景和白色文字\n        return {\n          color: '#ffffff',\n          background: 'rgba(255, 255, 255, 0.2)'\n        };\n      } else if (this.form.customStyle.backgroundType === 'gradient' || \n                this.form.customStyle.backgroundColor === '#333333' || \n                this.form.customStyle.backgroundColor === '#2c5aa0') {\n        // 深色背景时，使用半透明白色背景和白色文字\n        return {\n          color: '#ffffff',\n          background: 'rgba(255, 255, 255, 0.2)'\n        };\n      } else {\n        // 浅色背景时，使用浅灰色背景和深色文字\n        return {\n          color: textColor,\n          background: 'rgba(0, 0, 0, 0.05)'\n        };\n      }\n    },\n  },\n  methods: {\n    // 同步会员状态\n    async syncMemberStatus() {\n      try {\n        // 静默同步会员状态\n        await userService.syncMemberStatus();\n\n        // 重新检查会员状态\n        this.checkMemberStatus();\n      } catch (error) {\n        console.error('会员状态同步失败:', error);\n      }\n    },\n\n    // 电话号码输入验证\n    onPhoneInput(e) {\n      let value = e.detail.value;\n\n      // 只允许数字\n      value = value.replace(/[^\\d]/g, '');\n\n      // 限制最大长度为11位\n      if (value.length > 11) {\n        value = value.slice(0, 11);\n      }\n\n      // 更新表单数据\n      this.form.phone = value;\n\n      // 验证手机号格式\n      this.validatePhone(value);\n    },\n\n    // 验证手机号\n    validatePhone(phone) {\n      if (!phone) {\n        this.phoneError = '';\n        return;\n      }\n\n      if (phone.length < 11) {\n        this.phoneError = '手机号必须为11位';\n      } else if (!/^1[3-9]\\d{9}$/.test(phone)) {\n        this.phoneError = '请输入正确的手机号格式';\n      } else {\n        this.phoneError = '';\n      }\n    },\n\n    // 验证邮箱格式\n    validateEmail() {\n      if (!this.form.email) {\n        this.emailError = '';\n        return;\n      }\n\n      if (!userInfoService.validateEmail(this.form.email)) {\n        this.emailError = '请输入正确的邮箱格式';\n      } else {\n        this.emailError = '';\n      }\n    },\n\n    // 清除输入框内容的方法\n    clearPhone() {\n      this.form.phone = '';\n      this.phoneError = '';\n    },\n    clearWechat() {\n      this.form.wechat = '';\n    },\n    clearEmail() {\n      this.form.email = '';\n      this.emailError = '';\n    },\n\n    // 从服务器加载用户信息\n    async loadUserInfoFromServer() {\n      try {\n        // 检查是否已登录\n        const isLoggedIn = userService.isLoggedIn();\n        const token = getData(TOKEN_KEY);\n        const currentUser = getData(USER_INFO_KEY);\n\n        if (!isLoggedIn) {\n          return;\n        }\n\n        uni.showLoading({\n          title: '加载中...',\n          mask: true\n        });\n\n        const result = await userInfoService.getCurrentUserInfo();\n\n        if (result.success && result.data) {\n          // 将服务器数据映射到表单\n          const serverData = result.data;\n\n          // 更新基本信息和自定义样式\n          const updatedForm = {\n            ...this.form,\n            id: serverData.id,\n            name: serverData.name || '',\n            phone: serverData.phone || '',\n            email: serverData.email || '',\n            wechat: serverData.wechat || '',\n            position: serverData.position || '',\n            company: serverData.company || '',\n            address: serverData.address || '',\n            description: serverData.description || '',\n            avatar: serverData.avatar || '/static/default-avatar.png',\n            // 重要：同步自定义样式，包括背景图片\n            customStyle: {\n              ...this.form.customStyle,\n              ...(serverData.customStyle || {}),\n              // 确保关键字段被正确同步\n              backgroundType: serverData.customStyle?.backgroundType || this.form.customStyle?.backgroundType || 'color',\n              backgroundImage: serverData.customStyle?.backgroundImage || this.form.customStyle?.backgroundImage || '',\n              backgroundColor: serverData.customStyle?.backgroundColor || this.form.customStyle?.backgroundColor || '#ffffff',\n              gradientIndex: serverData.customStyle?.gradientIndex !== undefined ? serverData.customStyle.gradientIndex : (this.form.customStyle?.gradientIndex || 0),\n              textColor: serverData.customStyle?.textColor || this.form.customStyle?.textColor || '#333333',\n              borderRadiusIndex: serverData.customStyle?.borderRadiusIndex !== undefined ? serverData.customStyle.borderRadiusIndex : (this.form.customStyle?.borderRadiusIndex || 1),\n              cardStyleIndex: serverData.customStyle?.cardStyleIndex !== undefined ? serverData.customStyle.cardStyleIndex : (this.form.customStyle?.cardStyleIndex || 0)\n            }\n          };\n\n          this.form = updatedForm;\n\n          // 更新本地存储\n          uni.setStorageSync('userInfo', this.form);\n\n\n\n          // 静默同步，不显示提示\n          // uni.showToast({\n          //   title: '保存成功',\n          //   icon: 'success',\n          //   duration: 1500\n          // });\n        } else {\n\n          uni.showToast({\n            title: '网络异常，请检查网络连接',\n            icon: 'none',\n            duration: 2000\n          });\n        }\n      } catch (error) {\n        console.error('加载服务器用户信息失败:', error);\n      } finally {\n        uni.hideLoading();\n      }\n    },\n\n    // 静默上传头像（相册和拍照使用，不显示loading避免重复）\n    async uploadAvatarSilently(filePath) {\n      try {\n        // 如果用户已登录，上传到服务器\n        if (userService.isLoggedIn()) {\n          const result = await userInfoService.uploadAvatar(filePath);\n\n          if (result.success && result.url) {\n            // 使用服务器返回的URL\n            this.form.avatar = result.url;\n\n            // 强制更新视图\n            this.$forceUpdate();\n\n            // 不显示成功提示，避免干扰用户体验\n          } else {\n            // 上传失败，保持默认头像\n            this.form.avatar = '/static/default-avatar.png';\n            console.error('头像上传失败:', result.message);\n            uni.showToast({\n              title: '头像更新失败，请重试',\n              icon: 'none'\n            });\n          }\n        } else {\n          // 未登录，使用默认头像\n          this.form.avatar = '/static/default-avatar.png';\n          uni.showToast({\n            title: '请先登录后更换头像',\n            icon: 'none'\n          });\n        }\n      } catch (error) {\n        console.error('头像上传失败:', error);\n\n        // 上传失败，使用默认头像\n        this.form.avatar = '/static/default-avatar.png';\n\n        // 上传失败时的友好提示\n        let errorMessage = '头像上传失败';\n        if (error.message.includes('404')) {\n          errorMessage = '上传接口不存在，请联系管理员';\n        } else if (error.message.includes('directory')) {\n          errorMessage = '服务器存储配置问题，请联系管理员';\n        } else if (error.message.includes('权限')) {\n          errorMessage = '服务器权限问题，请联系管理员';\n        }\n\n        uni.showToast({\n          title: errorMessage,\n          icon: 'none',\n          duration: 3000\n        });\n      }\n      // 注意：这里不需要finally隐藏loading，因为没有显示loading\n    },\n\n    // 显示头像选择ActionSheet（仅非微信环境使用）\n    showAvatarActionSheet() {\n      uni.showActionSheet({\n        itemList: ['从相册选择', '拍照'],\n        success: (res) => {\n          switch(res.tapIndex) {\n            case 0: // 从相册选择\n              this.chooseAvatarFromAlbum();\n              break;\n            case 1: // 拍照\n              this.chooseAvatarFromCamera();\n              break;\n          }\n        }\n      });\n    },\n\n\n    // 从相册选择头像\n    chooseAvatarFromAlbum() {\n      uni.chooseImage({\n        count: 1,\n        sizeType: ['compressed'],\n        sourceType: ['album'],\n        success: (res) => {\n          // 使用静默上传，避免重复loading\n          this.uploadAvatarSilently(res.tempFilePaths[0]);\n        }\n      });\n    },\n\n    // 拍照获取头像\n    chooseAvatarFromCamera() {\n      uni.chooseImage({\n        count: 1,\n        sizeType: ['compressed'],\n        sourceType: ['camera'],\n        success: (res) => {\n          // 使用静默上传，避免重复loading\n          this.uploadAvatarSilently(res.tempFilePaths[0]);\n        }\n      });\n    },\n\n    // 上传头像到服务器（微信头像选择使用，会显示loading）\n    async uploadAvatarToServer(filePath) {\n      try {\n        // 显示加载状态\n        uni.showLoading({\n          title: '上传头像中...',\n          mask: true\n        });\n\n        // 如果用户已登录，上传到服务器\n        if (userService.isLoggedIn()) {\n          const result = await userInfoService.uploadAvatar(filePath);\n\n          if (result.success && result.url) {\n            // 使用服务器返回的URL\n            this.form.avatar = result.url;\n\n            // 强制更新视图\n            this.$forceUpdate();\n\n            // 微信头像选择时不显示上传成功提示，避免干扰用户体验\n            // uni.showToast({\n            //   title: '头像更新成功',\n            //   icon: 'success'\n            // });\n          } else {\n            // 上传失败，保持默认头像\n            this.form.avatar = '/static/default-avatar.png';\n            console.error('头像上传失败:', result.message);\n            uni.showToast({\n              title: '头像更新失败，请重试',\n              icon: 'none'\n            });\n          }\n        } else {\n          // 未登录，使用默认头像\n          this.form.avatar = '/static/default-avatar.png';\n          uni.showToast({\n            title: '请先登录后更换头像',\n            icon: 'none'\n          });\n        }\n      } catch (error) {\n        console.error('头像上传失败:', error);\n\n        // 上传失败，使用默认头像\n        this.form.avatar = '/static/default-avatar.png';\n\n        // 上传失败时的友好提示\n        let errorMessage = '头像上传失败';\n        if (error.message.includes('404')) {\n          errorMessage = '上传接口不存在，请联系管理员';\n        } else if (error.message.includes('directory')) {\n          errorMessage = '服务器存储配置问题，请联系管理员';\n        } else if (error.message.includes('权限')) {\n          errorMessage = '服务器权限问题，请联系管理员';\n        }\n\n        uni.showToast({\n          title: errorMessage,\n          icon: 'none',\n          duration: 3000\n        });\n      } finally {\n        uni.hideLoading();\n      }\n    },\n    \n    // 微信头像选择回调（新版API）\n    onChooseAvatar(e) {\n      if (e.detail && e.detail.avatarUrl) {\n        const avatarUrl = e.detail.avatarUrl;\n        console.log('微信头像URL:', avatarUrl);\n\n        // 根据微信最新规范：\n        // 1. chooseAvatar 返回的是临时文件路径\n        // 2. 需要立即上传到服务器保存\n        // 3. 临时路径在小程序中可以使用，但有时效性\n\n        // 立即上传到服务器获取永久URL\n        this.uploadAvatarToServer(avatarUrl);\n      } else {\n        uni.showToast({\n          title: '头像获取失败，请重试',\n          icon: 'none'\n        });\n      }\n    },\n\n\n\n\n\n    // 添加会员状态检查方法\n    checkMemberStatus() {\n      try {\n        const isMember = uni.getStorageSync('isMember');\n        const isPro = uni.getStorageSync('isPro');\n        const memberExpireDate = uni.getStorageSync('memberExpireDate');\n        \n        if (isMember || isPro) {\n          this.isPremiumMember = true;\n          return true;\n        }\n        \n        return false;\n      } catch (e) {\n        console.error('加载会员状态失败', e);\n        return false;\n      }\n    },\n    \n    // 跳转到会员开通页面\n    goPremium() {\n      uni.navigateTo({ url: '/pages/company/premium' });\n    },\n    \n    // 修改保存方法，根据选项卡实现分级权限检查\n    saveCard() {\n      // 检查登录状态\n      if (!userService.isLoggedIn()) {\n        uni.showModal({\n          title: '需要登录',\n          content: '保存功能需要登录后才能使用，是否前往登录？',\n          confirmText: '去登录',\n          cancelText: '取消',\n          success: (res) => {\n            if (res.confirm) {\n              uni.navigateTo({\n                url: '/pages/auth/auth'\n              });\n            }\n          }\n        });\n        return;\n      }\n\n      // 获取会员信息\n      const isMember = uni.getStorageSync('isMember');\n      const memberLevel = uni.getStorageSync('memberLevel') || 0;\n      const memberExpireDate = uni.getStorageSync('memberExpireDate');\n\n      // 检查是否过期\n      let isExpired = false;\n      if (memberExpireDate) {\n        const now = new Date();\n        // 修复iOS日期格式兼容性问题\n        const formattedDate = memberExpireDate.replace(/\\s/g, 'T');\n        const expireDate = new Date(formattedDate);\n        isExpired = now > expireDate;\n      }\n\n      // 根据当前选项卡检查权限\n      if (this.activeTab === 0) {\n        // 联系信息：需要专业版或企业版\n        const hasPermission = isMember && memberLevel >= 1 && !isExpired;\n        if (!hasPermission) {\n          uni.showModal({\n            title: '专业版功能',\n            content: '联系信息保存功能需要专业版或企业版权限',\n            confirmText: '立即升级',\n            cancelText: '取消',\n            success: (res) => {\n              if (res.confirm) {\n                this.goPremium();\n              }\n            }\n          });\n          return;\n        }\n      } else if (this.activeTab === 1) {\n        // 个性定制：只有企业版\n        const isEnterprise = isMember && memberLevel >= 2 && !isExpired;\n        if (!isEnterprise) {\n          uni.showModal({\n            title: '企业版功能',\n            content: '个性定制保存功能仅限企业版用户使用，专业版用户需要升级到企业版',\n            confirmText: '立即升级',\n            cancelText: '取消',\n            success: (res) => {\n              if (res.confirm) {\n                this.goPremium();\n              }\n            }\n          });\n          return;\n        }\n      }\n\n      // 有权限，可以正常保存\n      // 获取当前存储的完整用户信息\n      const currentUserInfo = uni.getStorageSync('userInfo') || {};\n\n      let userInfoToSave;\n      let successMessage;\n\n      if (this.activeTab === 0) {\n        // 联系信息：只保存联系相关字段\n        userInfoToSave = {\n          ...currentUserInfo,\n          avatar: this.form.avatar || '/static/default-avatar.png',\n          name: this.form.name || '徐敏',\n          position: this.form.position || '销售专员',\n          company: this.form.company || '德萨大数据股份有限公司',\n          phone: this.form.phone || '18596320120',\n          email: this.form.email || 'www.1099458780.com',\n          wechat: this.form.wechat || 'xumin_wx',\n          address: this.form.address || '山东省庄市滕州市北辛西路2000号',\n          description: this.form.description || '拥有5年销售经验，专注于客户关系维护和业务拓展。热爱学习新技术，善于沟通协调，致力于为客户提供最优质的服务体验。'\n        };\n        successMessage = '联系信息保存成功';\n      } else {\n        // 个性定制：只保存样式相关字段\n        userInfoToSave = {\n          ...currentUserInfo,\n          customStyle: {\n            ...(currentUserInfo.customStyle || {}),\n            backgroundType: this.form.customStyle?.backgroundType || 'color',\n            gradientIndex: this.form.customStyle?.gradientIndex || 0,\n            backgroundColor: this.form.customStyle?.backgroundColor || '#ffffff',\n            backgroundImage: this.form.customStyle?.backgroundImage || '',\n            textColor: this.form.customStyle?.textColor || '#333333',\n            borderRadiusIndex: this.form.customStyle?.borderRadiusIndex || 1,\n            cardStyleIndex: this.form.customStyle?.cardStyleIndex || 0\n          }\n        };\n        successMessage = '个性定制保存成功';\n      }\n\n      // 保存到本地存储和服务器\n      this.saveToServerAndLocal(userInfoToSave, successMessage);\n    },\n\n    // 保存到服务器和本地存储\n    async saveToServerAndLocal(userInfoToSave, successMessage) {\n      try {\n        uni.showLoading({\n          title: '保存中...',\n          mask: true\n        });\n\n        // 先保存到本地存储\n        uni.setStorageSync('userInfo', userInfoToSave);\n\n        // 检查用户是否已登录\n        const isLoggedIn = await userService.isLoggedIn();\n\n        if (isLoggedIn) {\n          // 用户已登录，同步到服务器（联系信息和个性定制都同步）\n          const result = await userInfoService.updateUserInfo(userInfoToSave);\n\n          if (result.success) {\n            uni.showToast({\n              title: successMessage,\n              icon: 'success',\n              duration: 1500\n            });\n\n            // 保存成功后重新从服务器加载数据，确保数据一致性\n            setTimeout(() => {\n              this.loadUserInfoFromServer();\n            }, 500);\n          } else {\n            // 服务器保存失败，但本地已保存\n            uni.showToast({\n              title: successMessage + '（网络异常，已保存到本地）',\n              icon: 'none',\n              duration: 2500\n            });\n            console.error('服务器保存失败:', result.message);\n          }\n        } else {\n          // 用户未登录，只保存到本地\n          uni.showToast({\n            title: successMessage,\n            icon: 'success',\n            duration: 1500\n          });\n        }\n\n        // 延迟返回，让用户看到保存成功的提示\n        setTimeout(() => {\n          uni.navigateBack();\n        }, 1000);\n\n      } catch (error) {\n        console.error('保存失败:', error);\n        uni.showToast({\n          title: '保存失败，请检查网络后重试',\n          icon: 'none',\n          duration: 2000\n        });\n      } finally {\n        uni.hideLoading();\n      }\n    },\n    \n    // 修改应用样式方法，增加会员检查\n    applyCardStyle(idx) {\n      // 先设置索引，确保UI显示正确的选中状态\n      this.form.customStyle.cardStyleIndex = idx;\n      \n      // 根据不同风格设置对应的样式\n      switch(idx) {\n        case 0: // 经典商务\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'solid',\n            backgroundColor: '#ffffff',\n            backgroundImage: '',\n            gradientIndex: -1,\n            textColor: '#2c5aa0',\n            cardStyleIndex: idx\n          };\n          break;\n        case 1: // 科技蓝\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 0, // 蓝色渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 2: // 简约灰\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'solid',\n            backgroundColor: '#f5f7fa',\n            backgroundImage: '',\n            gradientIndex: -1,\n            textColor: '#222222',\n            cardStyleIndex: idx\n          };\n          break;\n        case 3: // 暗夜黑\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'solid',\n            backgroundColor: '#333333',\n            backgroundImage: '',\n            gradientIndex: -1,\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 4: // 活力橙\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 1, // 橙色渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 5: // 森林绿\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 2, // 绿色渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 6: // 浪漫粉\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 3, // 粉色渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 7: // 高端黑金\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 4, // 灰色渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#d4af37', // 金色文字\n            cardStyleIndex: idx\n          };\n          break;\n        case 8: // 紫色梦幻\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 5, // 紫色渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 9: // 复古棕\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'solid',\n            backgroundColor: '#8b5a2b',\n            backgroundImage: '',\n            gradientIndex: -1,\n            textColor: '#f5deb3',\n            cardStyleIndex: idx\n          };\n          break;\n        case 10: // 海洋蓝\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 6, // 海洋蓝渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 11: // 珊瑚红\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'solid',\n            backgroundColor: '#FF6F61',\n            backgroundImage: '',\n            gradientIndex: -1,\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 12: // 薄荷绿\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'solid',\n            backgroundColor: '#98ff98',\n            backgroundImage: '',\n            gradientIndex: -1,\n            textColor: '#006400',\n            cardStyleIndex: idx\n          };\n          break;\n        case 13: // 太空灰\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 11, // 太空灰渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 14: // 日落橙\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 7, // 日落橙渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 15: // 翡翠绿\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 8, // 翡翠绿渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 16: // 商务蓝\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'solid',\n            backgroundColor: '#1e3c72',\n            backgroundImage: '',\n            gradientIndex: -1,\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 17: // 玫瑰金\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 9, // 玫瑰金渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#8b4513',\n            cardStyleIndex: idx\n          };\n          break;\n        case 18: // 星空紫\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 10, // 星空紫渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n      }\n      \n      // 强制更新视图\n      this.$forceUpdate();\n    },\n    \n    // 修改 applyBgColor 方法\n    applyBgColor(color) {\n      this.form.customStyle.backgroundType = 'color';\n      this.form.customStyle.backgroundColor = color;\n      this.form.customStyle.backgroundImage = '';\n      this.form.customStyle.gradientIndex = -1;\n      \n      // 根据背景色设置文字颜色\n      if (color === '#ffffff' || color === '#f5f7fa' || color === '#e6f0ff' || \n          color === '#f0f9eb' || color === '#fef0f0' || color === '#fdf6ec') {\n        this.form.customStyle.textColor = '#222222';\n      } else {\n        this.form.customStyle.textColor = '#ffffff';\n      }\n      \n      // 强制更新视图\n      this.$forceUpdate();\n    },\n    \n    // 修改 applyGradient 方法\n    applyGradient(idx) {\n      \n      // 确保索引有效\n      if (idx >= 0 && idx < this.gradients.length) {\n        this.form.customStyle.backgroundType = 'gradient';\n        this.form.customStyle.gradientIndex = idx;\n        this.form.customStyle.backgroundImage = '';\n        this.form.customStyle.backgroundColor = '';\n        this.form.customStyle.textColor = '#ffffff';\n        \n\n        \n        // 强制更新视图\n        this.$forceUpdate();\n        \n        // 延迟执行，确保样式更新\n        setTimeout(() => {\n          this.$forceUpdate();\n        }, 100);\n      } else {\n        console.error('无效的渐变索引:', idx);\n      }\n    },\n    \n    // 修改 applyBgImage 方法\n    applyBgImage(img) {\n      this.form.customStyle.backgroundType = 'image';\n      this.form.customStyle.backgroundImage = img;\n      this.form.customStyle.backgroundColor = '';\n      this.form.customStyle.gradientIndex = -1;\n      this.form.customStyle.textColor = '#ffffff';\n      \n      // 强制更新视图\n      this.$forceUpdate();\n      \n      // 延迟执行，确保样式更新\n      setTimeout(() => {\n        this.$forceUpdate();\n      }, 100);\n    },\n    \n    // 修改 applyTextColor 方法\n    applyTextColor(color) {\n      this.form.customStyle.textColor = color;\n      \n      // 强制更新视图\n      this.$forceUpdate();\n    },\n    \n    // 上传背景图片\n    async uploadBgImage() {\n      uni.chooseImage({\n        count: 1,\n        success: async (res) => {\n          const tempFilePath = res.tempFilePaths[0];\n\n          try {\n            uni.showLoading({\n              title: '上传中...',\n              mask: true\n            });\n\n            // 先使用临时路径显示\n            this.form.customStyle.backgroundType = 'image';\n            this.form.customStyle.backgroundImage = tempFilePath;\n            this.form.customStyle.backgroundColor = '';\n            this.form.customStyle.gradientIndex = -1;\n            this.form.customStyle.textColor = '#ffffff';\n            this.$forceUpdate();\n\n            // 如果用户已登录，上传到服务器\n            if (userService.isLoggedIn()) {\n              const result = await this.uploadImageToServer(tempFilePath);\n\n              if (result.success && result.url) {\n                // 使用服务器返回的URL\n                this.form.customStyle.backgroundImage = result.url;\n                this.$forceUpdate();\n\n                uni.showToast({\n                  title: '背景图片设置成功',\n                  icon: 'success'\n                });\n              } else {\n                uni.showToast({\n                  title: '背景图片设置成功',\n                  icon: 'success'\n                });\n              }\n            } else {\n              uni.showToast({\n                title: '背景图片设置成功',\n                icon: 'success'\n              });\n            }\n          } catch (error) {\n            console.error('背景图片上传失败:', error);\n            uni.showToast({\n              title: '背景图片设置失败',\n              icon: 'none'\n            });\n          } finally {\n            uni.hideLoading();\n          }\n        },\n        fail: (err) => {\n          console.error('选择图片失败:', err);\n          uni.showToast({ title: '图片选择失败，请重试', icon: 'none' });\n        }\n      });\n    },\n\n    // 获取可显示的头像URL\n    getDisplayAvatar(avatarUrl) {\n      if (!avatarUrl) {\n        return '/static/default-avatar.png';\n      }\n\n      // 根据微信最新规范：\n      // 1. 微信临时路径在小程序内部可以显示\n      // 2. 但我们优先使用服务器的HTTPS URL\n      // 3. 如果是HTTP路径且不是微信临时路径，则使用默认头像\n\n      if (avatarUrl.startsWith('https://')) {\n        // HTTPS URL 可以正常显示\n        return avatarUrl;\n      } else if (avatarUrl.startsWith('http://tmp/')) {\n        // 微信临时路径，在小程序中可以显示\n        return avatarUrl;\n      } else if (avatarUrl.startsWith('http://')) {\n        // 其他HTTP路径，使用默认头像\n        return '/static/default-avatar.png';\n      } else if (avatarUrl.startsWith('/')) {\n        // 相对路径，直接使用\n        return avatarUrl;\n      }\n\n      return avatarUrl;\n    },\n\n    // 上传图片到服务器\n    async uploadImageToServer(filePath) {\n      try {\n        if (!userService.isLoggedIn()) {\n          throw new Error('请先登录');\n        }\n\n        const token = userService.getToken();\n\n        return new Promise((resolve, reject) => {\n          const uploadUrl = `${API_BASE_URL}/upload/image`;\n\n          uni.uploadFile({\n            url: uploadUrl,\n            filePath: filePath,\n            name: 'image',\n            header: {\n              'Authorization': `Bearer ${token}`\n            },\n            success: (res) => {\n              try {\n                console.log('图片上传响应:', res);\n\n                if (res.statusCode !== 200) {\n                  reject(new Error(`服务器响应错误: ${res.statusCode}`));\n                  return;\n                }\n\n                if (!res.data) {\n                  reject(new Error('服务器返回空数据'));\n                  return;\n                }\n\n                const data = JSON.parse(res.data);\n                console.log('解析后的数据:', data);\n\n                if (data.code === 200) {\n                  resolve({\n                    success: true,\n                    data: data.data,\n                    url: data.data.url\n                  });\n                } else {\n                  reject(new Error(data.message || '上传失败'));\n                }\n              } catch (error) {\n                console.error('解析响应数据失败:', error);\n                reject(new Error(`响应数据解析失败: ${error.message}`));\n              }\n            },\n            fail: (error) => {\n              console.error('上传请求失败:', error);\n              reject(new Error('上传请求失败'));\n            }\n          });\n        });\n      } catch (error) {\n        console.error('上传图片失败:', error);\n        return {\n          success: false,\n          message: error.message || '上传失败'\n        };\n      }\n    },\n\n    setBorderRadius(idx) {\n      this.form.customStyle.borderRadiusIndex = idx;\n      this.form.customStyle.borderRadius = this.borderRadiusMap[idx];\n    },\n    setAvatarStyle(idx) {\n      this.form.customStyle.avatarStyleIndex = idx;\n      this.form.customStyle.avatarStyle = this.avatarStyleMap[idx];\n    },\n    setBgColor(color) {\n      this.form.customStyle.backgroundType = 'color';\n      this.form.customStyle.backgroundColor = color;\n      // 清除背景图片\n      this.form.customStyle.backgroundImage = '';\n      // 根据背景色设置文字颜色\n      if (color === '#ffffff' || color === '#f5f7fa' || color === '#e6f0ff' || \n          color === '#f0f9eb' || color === '#fef0f0' || color === '#fdf6ec') {\n        this.form.customStyle.textColor = '#222222';\n      } else {\n        this.form.customStyle.textColor = '#ffffff';\n      }\n    },\n    setTextColor(color) {\n      this.form.customStyle.textColor = color;\n    },\n    setCardStyle(idx) {\n      this.form.customStyle.cardStyleIndex = idx;\n      \n      // 根据不同风格设置对应的样式\n      switch(idx) {\n        case 0: // 经典商务\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'color',\n            backgroundColor: '#ffffff',\n            backgroundImage: '',\n            gradientIndex: -1,\n            textColor: '#2c5aa0',\n            cardStyleIndex: idx\n          };\n          break;\n        case 1: // 科技蓝\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 0, // 蓝色渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 2: // 简约灰\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'color',\n            backgroundColor: '#f5f7fa',\n            backgroundImage: '',\n            gradientIndex: -1,\n            textColor: '#222222',\n            cardStyleIndex: idx\n          };\n          break;\n        case 3: // 暗夜黑\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'color',\n            backgroundColor: '#333333',\n            backgroundImage: '',\n            gradientIndex: -1,\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 4: // 活力橙\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 1, // 橙色渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 5: // 森林绿\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 2, // 绿色渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 6: // 浪漫粉\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 3, // 粉色渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 7: // 高端黑金\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 4, // 灰色渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#d4af37', // 金色文字\n            cardStyleIndex: idx\n          };\n          break;\n        case 8: // 紫色梦幻\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 5, // 紫色渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 9: // 复古棕\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'color',\n            backgroundColor: '#8b5a2b',\n            backgroundImage: '',\n            gradientIndex: -1,\n            textColor: '#f5deb3',\n            cardStyleIndex: idx\n          };\n          break;\n        case 10: // 海洋蓝\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 6, // 海洋蓝渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 11: // 珊瑚红\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'solid',\n            backgroundColor: '#FF6F61',\n            backgroundImage: '',\n            gradientIndex: -1,\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 12: // 薄荷绿\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'solid',\n            backgroundColor: '#98ff98',\n            backgroundImage: '',\n            gradientIndex: -1,\n            textColor: '#006400',\n            cardStyleIndex: idx\n          };\n          break;\n        case 13: // 太空灰\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 11, // 太空灰渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 14: // 日落橙\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 7, // 日落橙渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 15: // 翡翠绿\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 8, // 翡翠绿渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 16: // 商务蓝\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'color',\n            backgroundColor: '#1e3c72',\n            backgroundImage: '',\n            gradientIndex: -1,\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n        case 17: // 玫瑰金\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 9, // 玫瑰金渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#8b4513',\n            cardStyleIndex: idx\n          };\n          break;\n        case 18: // 星空紫\n          this.form.customStyle = {\n            ...this.form.customStyle,\n            backgroundType: 'gradient',\n            gradientIndex: 10, // 星空紫渐变\n            backgroundColor: '',\n            backgroundImage: '',\n            textColor: '#ffffff',\n            cardStyleIndex: idx\n          };\n          break;\n      }\n    },\n    setGradient(idx) {\n      this.form.customStyle.backgroundType = 'gradient';\n      this.form.customStyle.gradientIndex = idx;\n      // 清除背景图片和纯色背景\n      this.form.customStyle.backgroundImage = '';\n      this.form.customStyle.backgroundColor = '';\n      this.form.customStyle.textColor = '#ffffff';\n      \n      // 强制更新视图\n      this.$forceUpdate();\n    },\n    setShadow(isShadow) {\n      this.form.customStyle.shadow = isShadow;\n    },\n    setFontSize(idx) {\n      this.form.customStyle.fontSizeIndex = idx;\n    },\n    setAvatarBorder(hasBorder) {\n      this.form.customStyle.avatarBorder = hasBorder;\n    },\n    goBack() {\n      uni.navigateBack();\n    },\n    handleScroll() {\n      // #ifdef H5\n      const scrollTop = window.scrollY || document.documentElement.scrollTop;\n      if (scrollTop > this.lastScrollTop) {\n        // 向下滑动，隐藏按钮\n        this.showSaveBtn = false;\n      } else {\n        // 向上滑动，显示按钮\n        this.showSaveBtn = true;\n      }\n      this.lastScrollTop = scrollTop;\n      // #endif\n    },\n    chooseBgImage() {\n      this.form.customStyle.backgroundType = 'image';\n    },\n    setBgImage(img) {\n      this.form.customStyle.backgroundType = 'image';\n      this.form.customStyle.backgroundImage = img;\n      // 清除纯色背景和渐变背景\n      this.form.customStyle.backgroundColor = '';\n      this.form.customStyle.gradientIndex = -1;\n      this.form.customStyle.textColor = '#ffffff';\n      \n      // 强制更新视图\n      this.$forceUpdate();\n      \n      // 延迟执行，确保样式更新\n      setTimeout(() => {\n        this.$forceUpdate();\n      }, 100);\n    },\n    onRegionChange(e) {\n      this.regionValue = e.detail.value;\n      this.regionText = this.regionValue.join(' ');\n    },\n    getLocation() {\n      uni.getLocation({\n        type: 'gcj02',\n        success: (res) => {\n          const latitude = res.latitude;\n          const longitude = res.longitude;\n          \n          // 逆地理编码，将经纬度转换为地址\n          uni.request({\n            url: `https://apis.map.qq.com/ws/geocoder/v1/?location=${latitude},${longitude}&key=您的腾讯地图KEY`,\n            success: (res) => {\n              if (res.data && res.data.result) {\n                const address = res.data.result.address;\n                const province = res.data.result.address_component.province;\n                const city = res.data.result.address_component.city;\n                const district = res.data.result.address_component.district;\n                \n                this.regionValue = [province, city, district];\n                this.regionText = this.regionValue.join(' ');\n                this.addressDetail = address.replace(this.regionText, '');\n                \n                uni.showToast({\n                  title: '位置信息已更新',\n                  icon: 'success'\n                });\n              }\n            },\n            fail: () => {\n              uni.showToast({\n                title: '地址解析失败，请重试',\n                icon: 'none'\n              });\n            }\n          });\n        },\n        fail: () => {\n          uni.showToast({\n            title: '位置获取失败，请检查定位权限',\n            icon: 'none'\n          });\n        }\n      });\n    },\n    confirmAddress() {\n      const fullAddress = this.regionText + ' ' + this.addressDetail;\n      this.form.address = fullAddress.trim();\n      this.showAddressModal = false;\n      \n      // 保存到本地\n      uni.setStorageSync('userAddress', {\n        regionValue: this.regionValue,\n        regionText: this.regionText,\n        addressDetail: this.addressDetail\n      });\n    },\n    updateContentPadding() {\n      // 直接设置样式，不使用动态类\n      if (this.topSectionHeight > 0) {\n        const paddingTop = this.statusBarHeight + 45 + this.topSectionHeight;\n        const reductionRatio = this.screenHeight < 700 ? 0.25 : 0.2;\n        const paddingReduction = this.topSectionHeight * reductionRatio;\n        const finalPadding = paddingTop - paddingReduction;\n        \n        // #ifdef H5\n        const contentArea = document.querySelector('.content-area');\n        if (contentArea) {\n          contentArea.style.paddingTop = `${finalPadding}px`;\n        }\n        // #endif\n        \n        // #ifndef H5\n        // 在小程序环境中使用uni.createSelectorQuery\n        const query = uni.createSelectorQuery();\n        query.select('.content-area').boundingClientRect(data => {\n          if (data) {\n            // 通过样式类或直接设置computed属性来更新\n            this.contentPaddingTop = finalPadding;\n          }\n        }).exec();\n        // #endif\n      }\n    },\n    // 添加地址选择器显示方法\n    showAddressSelector() {\n      this.showAddressModal = true;\n    },\n\n    // 初始化用户数据（优先从服务器加载）\n    async initializeUserData() {\n      // 确保表单数据有默认值\n      const defaultForm = {\n        avatar: '/static/default-avatar.png',\n        name: '徐敏',\n        position: '销售专员',\n        company: '德萨大数据股份有限公司',\n        phone: '18596320120',\n        email: 'www.1099458780.com',\n        wechat: 'xumin_wx',\n        address: '山东省庄市滕州市北辛西路2000号',\n        description: '拥有5年销售经验，专注于客户关系维护和业务拓展。热爱学习新技术，善于沟通协调，致力于为客户提供最优质的服务体验。',\n        customStyle: {\n          backgroundType: 'solid',\n          gradientIndex: 0,\n          backgroundColor: '#ffffff',\n          backgroundImage: '',\n          textColor: '#333333',\n          fontSize: 'medium',\n          avatarStyle: 'rounded',\n          borderRadius: 'small',\n          borderRadiusIndex: 1,\n          cardStyleIndex: 0\n        }\n      };\n\n      // 先设置默认数据\n      this.form = { ...defaultForm };\n\n      // 如果用户已登录，优先从服务器加载数据\n      if (userService.isLoggedIn()) {\n        try {\n          // 显示加载状态\n          uni.showLoading({\n            title: '加载数据中...',\n            mask: true\n          });\n\n          const result = await userInfoService.getCurrentUserInfo();\n\n          if (result.success && result.data) {\n            const serverData = result.data;\n\n            // 使用服务器数据更新表单\n            this.form = {\n              ...defaultForm,\n              id: serverData.id,\n              name: serverData.name || defaultForm.name,\n              phone: serverData.phone || defaultForm.phone,\n              email: serverData.email || defaultForm.email,\n              wechat: serverData.wechat || defaultForm.wechat,\n              position: serverData.position || defaultForm.position,\n              company: serverData.company || defaultForm.company,\n              address: serverData.address || defaultForm.address,\n              description: serverData.description || defaultForm.description,\n              avatar: serverData.avatar || defaultForm.avatar,\n              // 重要：使用服务器的自定义样式数据\n              customStyle: {\n                ...defaultForm.customStyle,\n                ...(serverData.customStyle || {})\n              }\n            };\n\n            // 保存到本地存储\n            uni.setStorageSync('userInfo', this.form);\n\n          } else {\n            this.loadFromLocalStorage(defaultForm);\n          }\n\n        } catch (error) {\n          console.error('从服务器加载数据失败:', error);\n          this.loadFromLocalStorage(defaultForm);\n        } finally {\n          uni.hideLoading();\n        }\n\n      } else {\n        this.loadFromLocalStorage(defaultForm);\n      }\n    },\n\n    // 从本地存储加载数据\n    loadFromLocalStorage(defaultForm) {\n      try {\n        // 读取本地存储信息\n        const saved = uni.getStorageSync('userInfo');\n\n        // 检查saved是否为有效数据\n        const isValidData = saved && typeof saved === 'object' &&\n                           Object.keys(saved).length > 0;\n\n        if (isValidData) {\n          // 合并保存的数据和默认数据，确保所有字段都存在\n          this.form = {\n            ...defaultForm,\n            ...saved\n          };\n\n          // 确保customStyle存在且完整\n          if (saved.customStyle) {\n            this.form.customStyle = {\n              ...defaultForm.customStyle,\n              ...saved.customStyle\n            };\n          } else {\n            this.form.customStyle = { ...defaultForm.customStyle };\n          }\n        } else {\n          // 如果没有保存的数据，使用默认值\n          this.form = { ...defaultForm };\n\n          // 将默认信息保存到本地存储，确保下次加载时有数据\n          uni.setStorageSync('userInfo', this.form);\n        }\n      } catch (error) {\n        // 出错时使用默认信息\n        this.form = { ...defaultForm };\n        // 尝试保存默认信息\n        try {\n          uni.setStorageSync('userInfo', this.form);\n        } catch (e) {\n          // 保存失败时不做处理\n        }\n      }\n    },\n\n    // 读取地址信息\n    loadAddressInfo() {\n      try {\n        const savedAddress = uni.getStorageSync('userAddress');\n        if (savedAddress) {\n          this.regionValue = savedAddress.regionValue || ['', '', ''];\n          this.regionText = savedAddress.regionText || '';\n          this.addressDetail = savedAddress.addressDetail || '';\n        }\n      } catch (error) {\n        // 加载地址信息出错时不做处理\n      }\n    },\n\n    // 获取系统信息\n    loadSystemInfo() {\n      try {\n        const windowInfo = uni.getWindowInfo();\n        const statusBarHeight = windowInfo.statusBarHeight || 0;\n        this.statusBarHeight = statusBarHeight;\n        this.screenWidth = windowInfo.windowWidth || 375;\n        this.screenHeight = windowInfo.windowHeight || 667;\n        this.deviceRatio = windowInfo.pixelRatio || 1;\n      } catch (error) {\n        this.statusBarHeight = 44;\n      }\n    },\n\n    // 强制更新视图\n    forceUpdateView() {\n      this.$nextTick(() => {\n        this.$forceUpdate();\n\n        // 延迟再次更新视图\n        setTimeout(() => {\n          this.$forceUpdate();\n        }, 300);\n      });\n    }\n  },\n  mounted() {\n    // #ifdef H5\n    window.addEventListener('scroll', this.handleScroll);\n    // #endif\n    \n    // 检查会员状态\n    this.checkMemberStatus();\n  },\n  beforeDestroy() {\n    // #ifdef H5\n    window.removeEventListener('scroll', this.handleScroll);\n    // #endif\n  },\n  onReady() {\n    // 在页面渲染完成后立即测量顶部区域高度\n    setTimeout(() => {\n      const query = uni.createSelectorQuery();\n      query.select('.top-section').boundingClientRect(data => {\n        if (data) {\n          this.topSectionHeight = data.height;\n          // 立即更新内容区域的上边距\n          this.$nextTick(() => {\n            this.updateContentPadding();\n          });\n        }\n      }).exec();\n    }, 50); // 使用更短的延迟\n  },\n  async onLoad() {\n    // 检查会员状态\n    this.checkMemberStatus();\n\n    // 加载系统信息\n    this.loadSystemInfo();\n\n    // 加载地址信息\n    this.loadAddressInfo();\n\n    // 优先尝试从服务器加载数据\n    await this.initializeUserData();\n\n    // 强制更新视图\n    this.forceUpdateView();\n  },\n\n  onShow() {\n    // 页面显示时同步会员状态\n    this.syncMemberStatus();\n  }\n}\n</script>\n\n<style scoped>\n.edit-container {\n  background: #f7f8fa;\n  min-height: 100vh;\n  padding-bottom: 140rpx;\n}\n.custom-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20rpx);\n  z-index: 1000;\n  display: flex;\n  align-items: flex-end;\n  padding: 20rpx 32rpx 20rpx 32rpx;\n  box-sizing: border-box;\n  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n}\n.navbar-left {\n  display: flex;\n  align-items: center;\n}\n.back-btn {\n  padding: 8rpx 16rpx;\n  background: linear-gradient(to bottom, #ffffff, #f5f5f5);\n  border-radius: 20rpx;\n  border: 1rpx solid rgba(0, 0, 0, 0.1);\n  display: flex;\n  align-items: center;\n  gap: 6rpx;\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);\n  transition: all 0.2s ease;\n}\n\n.back-btn:active {\n  transform: scale(0.96);\n  background: linear-gradient(to bottom, #f5f5f5, #eeeeee);\n}\n\n.back-icon {\n  font-size: 24rpx;\n  font-weight: bold;\n  margin-right: 4rpx;\n  color: #4f46e5;\n}\n\n.back-text {\n  font-size: 24rpx;\n  color: #333;\n  font-weight: 500;\n}\n.navbar-title {\n  color: #111827;\n  font-size: 36rpx;\n  font-weight: 700;\n  position: absolute;\n  left: 50%;\n  transform: translateX(-50%);\n  letter-spacing: 0.5rpx;\n  background: linear-gradient(to right, #4f46e5, #6366f1);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n}\n.top-section {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 1000;\n  background-color: #f7f8fa;\n}\n.card-preview {\n  padding: 0;\n  background-color: #f7f8fa;\n}\n.fixed-tab-bar {\n  padding: 0 20rpx;\n  margin-bottom: 0; /* 减小底部间距 */\n}\n.tab-bar {\n  display: flex;\n  background: #fff;\n  border-radius: 16rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);\n  overflow: hidden;\n}\n.tab-item {\n  flex: 1;\n  text-align: center;\n  font-size: 28rpx; /* 减小字体大小 */\n  color: #888;\n  padding: 20rpx 0; /* 减小内边距 */\n  background: #fff;\n  transition: color 0.2s, background 0.2s;\n}\n.tab-item.active {\n  color: #2c5aa0;\n  background: #f0f4ff;\n  font-weight: bold;\n}\n.content-area {\n  min-height: 100vh;\n  padding-top: 0; /* 初始值为0，由JS动态设置 */\n  transition: padding-top 0.2s ease-out; /* 添加平滑过渡效果 */\n}\n.form-section {\n  background: #fff;\n  border-radius: 16rpx;\n  padding: 30rpx 24rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);\n}\n.section-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 30rpx;\n}\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #2c5aa0;\n  margin-right: 16rpx;\n}\n.section-line {\n  flex: 1;\n  height: 2rpx;\n  background: linear-gradient(90deg, #4a6ef2, rgba(74,110,242,0.1));\n}\n.form-group {\n  margin-bottom: 20rpx;\n}\n.form-divider {\n  height: 1rpx;\n  background: #f0f0f0;\n  margin: 20rpx 0;\n}\n.modern-form-item {\n  margin-bottom: 24rpx;\n  display: flex;\n  align-items: center;\n}\n.form-label {\n  width: 120rpx;\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n.input-container {\n  flex: 1;\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n.input-icon {\n  width: 36rpx;\n  height: 36rpx;\n  position: absolute;\n  left: 16rpx;\n  opacity: 0.6;\n}\n\n.clear-btn {\n  position: absolute;\n  right: 16rpx;\n  width: 32rpx;\n  height: 32rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #e0e0e0;\n  border-radius: 50%;\n  transition: all 0.2s ease;\n  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n}\n\n.clear-btn:active {\n  transform: scale(0.9);\n  background: #d0d0d0;\n  box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.15);\n}\n\n.clear-icon {\n  font-size: 24rpx;\n  color: #666;\n  font-weight: bold;\n  line-height: 1;\n}\n\n.error-tip {\n  font-size: 24rpx;\n  color: #ff4757;\n  margin-top: 8rpx;\n  margin-left: 16rpx;\n}\n.form-input {\n  flex: 1;\n  height: 80rpx;\n  border: 1rpx solid #e5e5e5;\n  border-radius: 12rpx;\n  padding: 0 20rpx;\n  font-size: 28rpx;\n  background: #f8f8f8;\n  transition: all 0.3s;\n}\n.form-input.with-icon {\n  padding-left: 60rpx;\n  padding-right: 60rpx;\n}\n.form-input:focus {\n  border-color: #4a6ef2;\n  background: #f0f4ff;\n}\n.form-textarea {\n  flex: 1;\n  min-height: 80rpx;\n  border: 1rpx solid #e5e5e5;\n  border-radius: 8rpx;\n  padding: 8rpx 16rpx;\n  font-size: 26rpx;\n  background: #f8f8f8;\n}\n.form-btn {\n  font-size: 26rpx;\n  color: #4a6ef2;\n  background: #f0f4ff;\n  border-radius: 8rpx;\n  padding: 0 24rpx;\n  height: 56rpx;\n  border: none;\n}\n.picker-view {\n  flex: 1;\n  height: 64rpx;\n  display: flex;\n  align-items: center;\n  border: 1rpx solid #e5e5e5;\n  border-radius: 8rpx;\n  padding: 0 16rpx;\n  font-size: 26rpx;\n  background: #f8f8f8;\n}\n.diy-container {\n  padding: 10rpx 20rpx; /* 减小上下内边距 */\n  background-color: #f5f7fa;\n}\n.diy-card {\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);\n  margin-bottom: 16rpx; /* 减小底部外边距 */\n  overflow: hidden;\n}\n.diy-card-header {\n  padding: 16rpx; /* 减小内边距 */\n  border-bottom: 1rpx solid #f0f0f0;\n}\n.diy-card-title {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #333;\n}\n.diy-card-content {\n  padding: 16rpx; /* 减小内边距 */\n}\n.diy-option-group {\n  margin-bottom: 24rpx;\n}\n.diy-option-group:last-child {\n  margin-bottom: 0;\n}\n.diy-option-group-title {\n  font-size: 26rpx;\n  color: #666;\n  margin-bottom: 12rpx;\n  display: block;\n}\n.diy-option-row {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16rpx;\n  align-items: center;\n  justify-content: flex-start;\n  padding: 6rpx 0;\n}\n.diy-option-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-right: 16rpx;\n  margin-bottom: 16rpx;\n}\n.diy-option-preview {\n  width: 60rpx;\n  height: 40rpx;\n  border-radius: 8rpx;\n  margin-bottom: 8rpx;\n  transition: all 0.2s;\n}\n.diy-option-label {\n  font-size: 24rpx;\n  color: #666;\n  text-align: center;\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 50rpx;\n  line-height: 1.2;\n  padding: 0 4rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: normal;\n  word-break: break-word;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  display: -webkit-box;\n}\n.diy-color-block {\n  width: 44rpx;\n  height: 44rpx;\n  border-radius: 8rpx;\n  border: 2rpx solid transparent;\n  transition: all 0.2s;\n  margin: 6rpx;\n  position: relative; /* 添加相对定位，用于白色边框 */\n}\n.diy-color-block.active {\n  border-color: #4a6ef2;\n  transform: scale(1.1);\n  box-shadow: 0 2rpx 8rpx rgba(74,110,242,0.3);\n  z-index: 1; /* 确保激活状态的颜色块显示在上层 */\n}\n.diy-color-block-border {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border: 1rpx solid #ddd;\n  border-radius: 7rpx;\n  pointer-events: none; /* 确保不影响点击事件 */\n}\n.diy-image-block {\n  width: 140rpx;\n  height: 110rpx;\n  border-radius: 10rpx;\n  overflow: hidden;\n  border: 2rpx solid transparent;\n  transition: all 0.2s;\n  margin: 10rpx;\n}\n.diy-image-block.active {\n  border-color: #4a6ef2;\n  transform: scale(1.05);\n  box-shadow: 0 4rpx 12rpx rgba(74,110,242,0.3);\n}\n.diy-image-preview {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n.diy-upload-btn {\n  width: 140rpx;\n  height: 110rpx;\n  border-radius: 10rpx;\n  border: 2rpx dashed #999;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background-color: #f9f9f9;\n  transition: all 0.2s;\n  margin: 10rpx;\n}\n.diy-upload-btn:active {\n  background-color: #f0f0f0;\n}\n.diy-upload-icon {\n  font-size: 36rpx;\n  color: #999;\n  line-height: 1;\n  margin-bottom: 6rpx;\n}\n.diy-upload-text {\n  font-size: 28rpx;\n  color: #999;\n  line-height: 1;\n  margin-top: 8rpx;\n}\n.diy-text-size-block {\n  min-width: 64rpx;\n  height: 48rpx;\n  border-radius: 8rpx;\n  background-color: #f5f5f5;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 12rpx;\n  border: 2rpx solid transparent;\n  transition: all 0.2s;\n}\n.diy-text-size-block.active {\n  border-color: #4a6ef2;\n  background-color: #e6f0ff;\n}\n.diy-avatar-block {\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 8rpx;\n  background-color: #f5f5f5;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 2rpx solid transparent;\n  transition: all 0.2s;\n}\n.diy-avatar-block.active {\n  border-color: #4a6ef2;\n  background-color: #e6f0ff;\n}\n.diy-avatar-preview {\n  width: 36rpx;\n  height: 36rpx;\n  background-color: #999;\n}\n.diy-switch-btn {\n  min-width: 64rpx;\n  height: 48rpx;\n  border-radius: 8rpx;\n  background-color: #f5f5f5;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 12rpx;\n  border: 2rpx solid transparent;\n  font-size: 26rpx;\n  color: #666;\n  transition: all 0.2s;\n}\n.diy-switch-btn.active {\n  border-color: #4a6ef2;\n  background-color: #e6f0ff;\n  color: #4a6ef2;\n}\n\n/* 地址输入样式优化 */\n.address-item {\n  align-items: flex-start;\n}\n\n.address-input-container {\n  flex: 1;\n}\n\n.address-preview {\n  flex: 1;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 80rpx;\n  border: 1rpx solid #e5e5e5;\n  border-radius: 12rpx;\n  padding: 0 20rpx 0 60rpx;\n  font-size: 28rpx;\n  background: #f8f8f8;\n}\n\n.address-text {\n  flex: 1;\n  color: #333;\n  word-break: break-all;\n}\n\n.address-icon {\n  color: #999;\n  font-size: 28rpx;\n  margin-left: 10rpx;\n}\n\n/* 地址选择弹窗 */\n.address-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 9999;\n  display: flex;\n  align-items: flex-end;\n}\n\n.address-modal-mask {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n}\n\n.address-modal-content {\n  position: relative;\n  width: 100%;\n  background-color: #fff;\n  border-radius: 24rpx 24rpx 0 0;\n  padding-bottom: env(safe-area-inset-bottom);\n  z-index: 10000;\n}\n\n.address-modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 24rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.address-modal-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.address-modal-close {\n  font-size: 40rpx;\n  color: #999;\n  padding: 10rpx;\n}\n\n.address-tabs {\n  display: flex;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.address-tab {\n  flex: 1;\n  text-align: center;\n  padding: 20rpx 0;\n  font-size: 28rpx;\n  color: #666;\n  position: relative;\n}\n\n.address-tab.active {\n  color: #4a6ef2;\n  font-weight: bold;\n}\n\n.address-tab.active::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 40rpx;\n  height: 4rpx;\n  background-color: #4a6ef2;\n  border-radius: 2rpx;\n}\n\n.address-content {\n  padding: 30rpx 24rpx;\n  min-height: 400rpx;\n}\n\n.address-manual {\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n}\n\n.address-picker-group {\n  border: 1rpx solid #e5e5e5;\n  border-radius: 8rpx;\n  overflow: hidden;\n}\n\n.region-picker {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20rpx;\n  font-size: 28rpx;\n  color: #333;\n  background-color: #f8f8f8;\n}\n\n.picker-arrow {\n  color: #999;\n  font-size: 24rpx;\n}\n\n.address-detail-input {\n  border: 1rpx solid #e5e5e5;\n  border-radius: 8rpx;\n  padding: 20rpx;\n  font-size: 28rpx;\n  background-color: #f8f8f8;\n  min-height: 100rpx;\n}\n\n.map-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 20rpx;\n}\n\n.map-placeholder {\n  width: 100%;\n  height: 300rpx;\n  background-color: #f5f7fa;\n  border-radius: 8rpx;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n}\n\n.map-image {\n  width: 80rpx;\n  height: 80rpx;\n  margin-bottom: 20rpx;\n}\n\n.map-tip {\n  font-size: 28rpx;\n  color: #999;\n}\n\n.map-btn {\n  width: 100%;\n  height: 80rpx;\n  background: linear-gradient(135deg, #4A90E2, #357abd);\n  color: #fff;\n  font-size: 28rpx;\n  border-radius: 8rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: none;\n}\n\n.address-modal-footer {\n  display: flex;\n  padding: 24rpx;\n  gap: 20rpx;\n  border-top: 1rpx solid #f0f0f0;\n}\n\n.address-cancel-btn,\n.address-confirm-btn {\n  flex: 1;\n  height: 80rpx;\n  border-radius: 8rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n  border: none;\n}\n\n.address-cancel-btn {\n  background-color: #f5f5f5;\n  color: #666;\n}\n\n.address-confirm-btn {\n  background: linear-gradient(135deg, #4A90E2, #357abd);\n  color: #fff;\n}\n\n/* 名片风格横向滚动样式 */\n.style-scroll-view {\n  width: 100%;\n  white-space: nowrap;\n  margin: 10rpx 0;\n  height: 260rpx;\n  padding-bottom: 50rpx;\n}\n\n.style-scroll-content {\n  display: inline-flex;\n  padding: 10rpx 0;\n  height: 100%;\n}\n\n.style-option-item {\n  display: inline-flex;\n  flex-direction: column;\n  align-items: center;\n  margin-right: 24rpx;\n  width: 160rpx;\n  transition: all 0.2s;\n  position: relative;\n  padding-bottom: 50rpx;\n  height: 200rpx; /* 固定高度 */\n}\n\n.style-option-preview {\n  width: 160rpx;\n  height: 200rpx;\n  border-radius: 12rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.12);\n  padding: 16rpx;\n  box-sizing: border-box;\n  transition: all 0.2s;\n  position: relative;\n  overflow: hidden;\n}\n\n.style-preview-content {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.style-preview-avatar {\n  width: 40rpx;\n  height: 40rpx;\n  border-radius: 50%;\n  background-color: rgba(0,0,0,0.2);\n  margin-bottom: 16rpx;\n}\n\n.style-preview-info {\n  display: flex;\n  flex-direction: column;\n  gap: 12rpx;\n}\n\n.style-preview-name {\n  height: 16rpx;\n  width: 80rpx;\n  background-color: rgba(0,0,0,0.2);\n  border-radius: 4rpx;\n}\n\n.style-preview-position {\n  height: 12rpx;\n  width: 60rpx;\n  background-color: rgba(0,0,0,0.15);\n  border-radius: 4rpx;\n}\n\n.style-option-label {\n  font-size: 24rpx;\n  color: #666;\n  text-align: center;\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 50rpx;\n  line-height: 1.2;\n  padding: 0 4rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: normal;\n  word-break: break-word;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  background-color: rgba(255,255,255,0.7);\n  border-radius: 0 0 12rpx 12rpx;\n  margin-top: 4rpx;\n}\n\n.style-option-item.active .style-option-preview {\n  transform: scale(1.05);\n  box-shadow: 0 4rpx 16rpx rgba(74,110,242,0.5);\n  border: 3rpx solid #4a6ef2;\n}\n\n.style-option-item.active .style-option-label {\n  color: #4a6ef2;\n  font-weight: bold;\n}\n\n/* 保存按钮样式 */\n.save-btn-float {\n  position: fixed;\n  left: 5%;\n  width: 90%;\n  bottom: 40rpx;\n  height: 88rpx;\n  border-radius: 20rpx;\n  background: linear-gradient(135deg, #4A90E2, #357abd);\n  color: #fff;\n  font-size: 30rpx;\n  font-weight: 600;\n  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);\n  border: none;\n  opacity: 0;\n  transform: translateY(100%);\n  transition: all 0.3s cubic-bezier(.4,0,.2,1);\n  z-index: 9999;\n}\n\n.save-btn-float.show {\n  opacity: 1;\n  transform: translateY(0);\n}\n\n/* 名片样式 */\n.classic-card-v2 {\n  background: #fff;\n  border-radius: 24rpx;\n  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.12);\n  padding: 32rpx 30rpx 28rpx 30rpx;\n  position: relative;\n  border: 1rpx solid #e0e0e0;\n  overflow: hidden; /* 确保背景图片不会溢出 */\n}\n\n/* 背景图片样式 */\n.classic-card-v2-bg-image {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 0;\n}\n\n/* 背景图片覆盖层 */\n.classic-card-v2-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.3);\n  z-index: 1;\n}\n\n/* 内容容器 */\n.classic-card-v2-content {\n  position: relative;\n  z-index: 2;\n}\n\n/* 为背景图片添加暗色遮罩，提高文字可读性 */\n.classic-card-v2 {\n  isolation: isolate; /* 创建新的层叠上下文 */\n}\n\n.classic-card-v2::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.3);\n  z-index: 1;\n  opacity: 0;\n  pointer-events: none;\n  transition: opacity 0.3s;\n}\n\n.classic-card-v2[style*=\"background-image\"]::before {\n  opacity: 1;\n}\n\n.classic-card-v2-header,\n.classic-card-v2-divider,\n.classic-card-v2-contact-list {\n  position: relative;\n  z-index: 2;\n}\n\n.classic-card-v2-header {\n  display: flex;\n  flex-direction: row;\n  justify-content: space-between;\n  align-items: flex-start;\n}\n\n.classic-card-v2-info {\n  flex: 1;\n  min-width: 0;\n}\n\n.classic-card-v2-name-row {\n  display: flex;\n  align-items: center;\n  margin-bottom: 12rpx;\n}\n\n.classic-card-v2-name {\n  font-size: 48rpx;\n  font-weight: bold;\n  color: #222;\n  margin-right: 18rpx;\n}\n\n.classic-card-v2-position {\n  font-size: 28rpx;\n  border-radius: 24rpx;\n  padding: 4rpx 20rpx;\n  margin-left: 8rpx;\n  transition: all 0.3s ease;\n}\n\n.classic-card-v2-company {\n  font-size: 28rpx;\n  color: #4a6ef2;\n  margin-bottom: 0;\n}\n\n.classic-card-v2-avatar-box {\n  position: relative;\n  width: 120rpx;\n  height: 120rpx;\n}\n\n.classic-card-v2-avatar-bg {\n  background: #f7bfa3;\n  border-radius: 50%;\n  width: 120rpx;\n  height: 120rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n}\n\n.classic-card-v2-avatar {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 50%;\n  border: 2rpx solid rgba(255,255,255,0.8);\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);\n}\n\n.classic-card-v2-cert {\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  width: 32rpx;\n  height: 32rpx;\n  border-radius: 50%;\n  background: #fff;\n  border: 4rpx solid #fff;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);\n}\n\n.classic-card-v2-divider {\n  height: 1rpx;\n  background: #f0f0f0;\n  margin: 24rpx 0 18rpx 0;\n}\n\n.classic-card-v2-contact-list {\n  margin-top: 0;\n}\n\n.classic-card-v2-contact-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 18rpx;\n}\n\n.classic-card-v2-icon {\n  width: 36rpx;\n  height: 36rpx;\n  margin-right: 16rpx;\n  flex-shrink: 0;\n}\n\n.classic-card-v2-label {\n  font-size: 28rpx;\n  color: #888;\n  margin-right: 8rpx;\n}\n\n.classic-card-v2-value {\n  font-size: 28rpx;\n  color: #4a6ef2;\n  word-break: break-all;\n}\n\n/* 小屏幕设备样式调整 - 进一步减小 */\n@media screen and (max-height: 700px) {\n  .classic-card-v2 {\n    padding: 24rpx 24rpx 20rpx 24rpx;\n  }\n  \n  .tab-item {\n    padding: 16rpx 0;\n    font-size: 26rpx;\n  }\n  \n  .form-section {\n    padding: 16rpx 14rpx 6rpx 14rpx;\n  }\n  \n  .form-title {\n    margin-bottom: 10rpx;\n  }\n  \n  .form-item {\n    margin-bottom: 10rpx;\n  }\n}\n\n/* 确保名片预览中的文字颜色可以被动态修改 */\n.classic-card-v2-name,\n.classic-card-v2-company,\n.classic-card-v2-value {\n  transition: color 0.3s ease;\n}\n\n/* 头像选择器样式 */\n.avatar-item {\n  align-items: flex-start;\n  margin-bottom: 30rpx;\n  padding-bottom: 20rpx;\n  border-bottom: 1rpx dashed #e0e0e0;\n}\n\n.avatar-selector {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-top: 30rpx;\n}\n\n.avatar-preview {\n  width: 160rpx;\n  height: 160rpx;\n  border-radius: 80rpx;\n  border: 4rpx solid #f0f0f0;\n  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);\n  margin-bottom: 20rpx;\n}\n\n.avatar-buttons {\n  display: flex;\n  flex-direction: row;\n  gap: 20rpx;\n  margin-top: 16rpx;\n  align-items: center;\n  justify-content: center;\n}\n\n.avatar-btn {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  background: linear-gradient(135deg, #1aad19, #2dc653);\n  color: white;\n  font-size: 24rpx;\n  padding: 16rpx 20rpx;\n  border-radius: 16rpx;\n  border: none;\n  box-shadow: 0 4rpx 12rpx rgba(26, 173, 25, 0.3);\n  min-width: 120rpx;\n}\n\n.avatar-btn::after {\n  border: none;\n}\n\n.wechat-avatar-btn {\n  background: linear-gradient(135deg, #1aad19, #2dc653);\n}\n\n.btn-icon {\n  font-size: 32rpx;\n  margin-bottom: 8rpx;\n}\n\n.btn-text {\n  font-size: 26rpx;\n  font-weight: 500;\n  color: white;\n}\n\n.avatar-change-btn {\n  background: linear-gradient(135deg, #52c41a, #73d13d);\n  color: white;\n  padding: 12rpx 32rpx;\n  border-radius: 50rpx;\n  font-size: 26rpx;\n  font-weight: 500;\n  box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);\n  transition: all 0.3s ease;\n  text-align: center;\n  height: 60rpx;\n  line-height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.avatar-change-btn:active {\n  transform: scale(0.95);\n  box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.4);\n  background: linear-gradient(135deg, #389e0d, #52c41a);\n}\n\n/* 微信小程序button组件样式重置 */\n.avatar-change-btn::after {\n  border: none;\n}\n\n/* 悬停效果（支持的平台） */\n.avatar-change-btn:hover {\n  background: linear-gradient(135deg, #73d13d, #95de64);\n  box-shadow: 0 6rpx 16rpx rgba(82, 196, 26, 0.4);\n  transform: translateY(-2rpx);\n}\n\n\n\n\n\n\n\n/* 底部安全区域，防止保存按钮遮挡内容 */\n.bottom-safe-area {\n  height: 120rpx;\n  width: 100%;\n}\n\n/* 为背景图片场景单独添加类 */\n.classic-card-v2-with-bg-image::before {\n  opacity: 1 !important;\n}\n\n/* 个人介绍样式 */\n.profile-section {\n\tbackground: linear-gradient(145deg, #ffffff, #f8faff);\n\tborder-radius: 20rpx;\n\tpadding: 0;\n\tmargin-top: 30rpx;\n\tbox-shadow: 0 10rpx 30rpx rgba(59, 130, 246, 0.08);\n\tposition: relative;\n\toverflow: hidden;\n\tborder: 1rpx solid rgba(59, 130, 246, 0.1);\n}\n\n.profile-section::before {\n\tcontent: \"\";\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\theight: 1rpx;\n\tbackground: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);\n}\n\n.profile-header {\n\tpadding: 28rpx 24rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tborder-bottom: 1rpx solid rgba(59, 130, 246, 0.08);\n\tbackground: linear-gradient(90deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.02));\n\tposition: relative;\n}\n\n.profile-header::after {\n\tcontent: \"\";\n\tposition: absolute;\n\tbottom: 0;\n\tleft: 24rpx;\n\tright: 24rpx;\n\theight: 1rpx;\n\tbackground: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);\n}\n\n.profile-header-left {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.profile-icon-wrapper {\n\twidth: 48rpx;\n\theight: 48rpx;\n\tborder-radius: 12rpx;\n\tbackground: linear-gradient(135deg, #3b82f6, #60a5fa);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 16rpx;\n\tbox-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.2);\n}\n\n.profile-icon {\n\twidth: 32rpx;\n\theight: 32rpx;\n\tfilter: brightness(0) invert(1);\n}\n\n.profile-title {\n\tfont-size: 34rpx;\n\tfont-weight: 600;\n\tcolor: #1e40af;\n\tposition: relative;\n\tletter-spacing: 2rpx;\n}\n\n.profile-header-badge {\n\tbackground: linear-gradient(90deg, #3b82f6, #60a5fa);\n\tborder-radius: 30rpx;\n\tpadding: 8rpx 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tbox-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.15);\n}\n\n.profile-badge-text {\n\tfont-size: 22rpx;\n\tcolor: white;\n\tfont-weight: 500;\n\tletter-spacing: 1rpx;\n}\n\n.badge-dot {\n\twidth: 8rpx;\n\theight: 8rpx;\n\tborder-radius: 50%;\n\tbackground: #ffffff;\n\tmargin-left: 8rpx;\n\tbox-shadow: 0 0 4rpx rgba(255, 255, 255, 0.8);\n}\n\n.profile-content {\n\tpadding: 30rpx 24rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 30rpx;\n}\n\n.profile-desc-container {\n\tbackground: rgba(59, 130, 246, 0.02);\n\tborder-radius: 16rpx;\n\tpadding: 24rpx;\n\tborder-left: 4rpx solid rgba(59, 130, 246, 0.2);\n\tposition: relative;\n}\n\n.profile-desc-decoration-top {\n\tposition: absolute;\n\ttop: 12rpx;\n\tright: 12rpx;\n\twidth: 20rpx;\n\theight: 20rpx;\n\tborder-top: 3rpx solid rgba(59, 130, 246, 0.2);\n\tborder-right: 3rpx solid rgba(59, 130, 246, 0.2);\n}\n\n.profile-desc-decoration-bottom {\n\tposition: absolute;\n\tbottom: 12rpx;\n\tright: 12rpx;\n\twidth: 20rpx;\n\theight: 20rpx;\n\tborder-bottom: 3rpx solid rgba(59, 130, 246, 0.2);\n\tborder-right: 3rpx solid rgba(59, 130, 246, 0.2);\n}\n\n.profile-desc {\n\tfont-size: 28rpx;\n\tcolor: #4b5563;\n\tline-height: 1.8;\n\ttext-align: justify;\n\tletter-spacing: 0.5rpx;\n}\n\n.profile-section-header {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n}\n\n.section-header-line {\n\twidth: 8rpx;\n\theight: 28rpx;\n\tbackground: linear-gradient(to bottom, #3b82f6, #60a5fa);\n\tborder-radius: 4rpx;\n\tmargin-right: 12rpx;\n}\n\n.section-header-icon {\n\tfont-size: 28rpx;\n\tmargin-right: 10rpx;\n}\n\n.section-header-title {\n\tfont-size: 30rpx;\n\tcolor: #1e40af;\n\tfont-weight: 600;\n\tletter-spacing: 1rpx;\n}\n\n.profile-tags-container {\n\tmargin-top: 10rpx;\n}\n\n.profile-tags-wrapper {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tgap: 16rpx;\n\tmargin-top: 20rpx;\n}\n\n.profile-tag-item {\n\tdisplay: flex;\n\talign-items: center;\n\tbackground: linear-gradient(145deg, #ffffff, #f8faff);\n\tpadding: 12rpx 20rpx;\n\tborder-radius: 30rpx;\n\tbox-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.06);\n\tborder: 1rpx solid rgba(59, 130, 246, 0.08);\n\ttransition: all 0.3s ease;\n}\n\n.profile-tag-item:active {\n\ttransform: translateY(-4rpx);\n\tbox-shadow: 0 8rpx 20rpx rgba(59, 130, 246, 0.1);\n}\n\n.tag-dot {\n\twidth: 12rpx;\n\theight: 12rpx;\n\tborder-radius: 50%;\n\tbackground: linear-gradient(135deg, #3b82f6, #60a5fa);\n\tmargin-right: 10rpx;\n\tbox-shadow: 0 2rpx 6rpx rgba(59, 130, 246, 0.2);\n}\n\n.tag-text {\n\tfont-size: 26rpx;\n\tcolor: #1e40af;\n\tfont-weight: 500;\n}\n\n.profile-achievements-container {\n\tmargin-top: 10rpx;\n}\n\n.profile-achievements-list {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 20rpx;\n\tmargin-top: 20rpx;\n}\n\n.profile-achievement-item {\n\tdisplay: flex;\n\talign-items: flex-start;\n\tbackground: linear-gradient(145deg, #ffffff, #f8faff);\n\tpadding: 20rpx;\n\tborder-radius: 16rpx;\n\tbox-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.06);\n\tborder: 1rpx solid rgba(59, 130, 246, 0.08);\n\ttransition: all 0.3s ease;\n}\n\n.profile-achievement-item:active {\n\ttransform: translateY(-4rpx);\n\tbox-shadow: 0 8rpx 20rpx rgba(59, 130, 246, 0.1);\n}\n\n.achievement-icon {\n\twidth: 44rpx;\n\theight: 44rpx;\n\tborder-radius: 50%;\n\tbackground: linear-gradient(135deg, #3b82f6, #60a5fa);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 16rpx;\n\tflex-shrink: 0;\n\tbox-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.15);\n}\n\n.achievement-icon-text {\n\tfont-size: 22rpx;\n\tcolor: white;\n\tfont-weight: bold;\n}\n\n.achievement-content {\n\tflex: 1;\n\tposition: relative;\n}\n\n.achievement-text {\n\tfont-size: 28rpx;\n\tcolor: #1e40af;\n\tfont-weight: 500;\n\tline-height: 1.6;\n}\n\n.achievement-decoration {\n\theight: 2rpx;\n\twidth: 60rpx;\n\tbackground: linear-gradient(90deg, #3b82f6, rgba(59, 130, 246, 0.2));\n\tmargin-top: 12rpx;\n}\n\n.profile-education-container {\n\tmargin-top: 10rpx;\n}\n\n.profile-education-timeline {\n\tmargin-top: 20rpx;\n}\n\n.profile-education-item {\n\tdisplay: flex;\n\tmargin-bottom: 24rpx;\n}\n\n.profile-education-item:last-child {\n\tmargin-bottom: 0;\n}\n\n.education-timeline-left {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tmargin-right: 16rpx;\n\tpadding-top: 8rpx;\n}\n\n.timeline-dot {\n\twidth: 16rpx;\n\theight: 16rpx;\n\tborder-radius: 50%;\n\tbackground: linear-gradient(135deg, #3b82f6, #60a5fa);\n\tbox-shadow: 0 0 0 6rpx rgba(59, 130, 246, 0.1);\n}\n\n.timeline-line {\n\twidth: 2rpx;\n\tflex: 1;\n\tbackground: linear-gradient(to bottom, #3b82f6, rgba(59, 130, 246, 0.2));\n\tmargin: 8rpx 0;\n}\n\n.education-content {\n\tbackground: linear-gradient(145deg, #ffffff, #f8faff);\n\tpadding: 20rpx;\n\tborder-radius: 16rpx;\n\tbox-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.06);\n\tborder: 1rpx solid rgba(59, 130, 246, 0.08);\n\tflex: 1;\n}\n\n.education-school {\n\tfont-size: 28rpx;\n\tcolor: #1e40af;\n\tfont-weight: 600;\n\tmargin-bottom: 8rpx;\n\tdisplay: block;\n}\n\n.education-major {\n\tfont-size: 26rpx;\n\tcolor: #4b5563;\n\tmargin-bottom: 8rpx;\n\tdisplay: block;\n}\n\n.education-time {\n\tfont-size: 24rpx;\n\tcolor: #6b7280;\n\tdisplay: block;\n}\n\n.profile-footer {\n\tpadding: 16rpx 24rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tborder-top: 1rpx solid rgba(59, 130, 246, 0.05);\n}\n\n.footer-line {\n\twidth: 60rpx;\n\theight: 2rpx;\n\tbackground: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);\n}\n\n.footer-dot {\n\twidth: 6rpx;\n\theight: 6rpx;\n\tborder-radius: 50%;\n\tbackground: rgba(59, 130, 246, 0.3);\n\tmargin: 0 4rpx;\n}\n\n/* 删除企业版/专业版限制遮罩样式 */\n\n/* 添加禁用状态样式 */\n.form-input:disabled {\n  background-color: #f5f5f5;\n  color: #999;\n}\n\n.avatar-upload-btn.disabled, .address-preview.disabled {\n  opacity: 0.6;\n  background: #f5f5f5;\n  color: #999;\n}\n\n/* 删除会员提示条样式 */\n\n/* CSS-based contact icons */\n.contact-icon {\n  width: 24px;\n  height: 24px;\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* Phone icon */\n.phone-icon::before {\n  content: '';\n  position: absolute;\n  width: 16px;\n  height: 16px;\n  border: 2px solid currentColor;\n  border-radius: 2px;\n  transform: rotate(45deg);\n  top: 2px;\n  left: 4px;\n}\n\n.phone-icon::after {\n  content: '';\n  position: absolute;\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background-color: currentColor;\n  top: 8px;\n  left: 8px;\n}\n\n/* Chat icon */\n.chat-icon::before {\n  content: '';\n  position: absolute;\n  width: 18px;\n  height: 14px;\n  border: 2px solid currentColor;\n  border-radius: 4px;\n  top: 4px;\n  left: 3px;\n}\n\n.chat-icon::after {\n  content: '';\n  position: absolute;\n  width: 0;\n  height: 0;\n  border-left: 5px solid transparent;\n  border-right: 5px solid transparent;\n  border-top: 6px solid currentColor;\n  bottom: 2px;\n  left: 7px;\n}\n\n/* Email icon */\n.email-icon::before {\n  content: '';\n  position: absolute;\n  width: 18px;\n  height: 14px;\n  border: 2px solid currentColor;\n  border-radius: 2px;\n  top: 4px;\n  left: 3px;\n}\n\n.email-icon::after {\n  content: '';\n  position: absolute;\n  width: 14px;\n  height: 8px;\n  border-left: 2px solid currentColor;\n  border-right: 2px solid currentColor;\n  border-top: 2px solid currentColor;\n  border-radius: 2px 2px 0 0;\n  transform: rotate(-45deg);\n  top: 7px;\n  left: 5px;\n}\n\n/* Location icon */\n.location-icon::before {\n  content: '';\n  position: absolute;\n  width: 14px;\n  height: 14px;\n  border: 2px solid currentColor;\n  border-radius: 50% 50% 50% 0;\n  transform: rotate(-45deg);\n  top: 2px;\n  left: 5px;\n}\n\n.location-icon::after {\n  content: '';\n  position: absolute;\n  width: 4px;\n  height: 4px;\n  background-color: currentColor;\n  border-radius: 50%;\n  top: 7px;\n  left: 10px;\n}\n</style>\n", "import MiniProgramPage from 'D:/NewDemo/unimp/pages/card/edit.vue'\nwx.createPage(MiniProgramPage)"], "names": ["userService", "uni", "userInfoService", "getData", "TOKEN_KEY", "USER_INFO_KEY", "API_BASE_URL", "res"], "mappings": ";;;;;;;AAsXA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,iBAAiB;AAAA,MACjB,WAAW;AAAA,MACX,aAAa;AAAA,MACb,eAAe;AAAA,MAGf,kBAAkB;AAAA,MAClB,YAAY;AAAA,MACZ,aAAa,CAAC,IAAI,IAAI,EAAE;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,kBAAkB;AAAA;AAAA,MAClB,mBAAmB;AAAA;AAAA,MACnB,aAAa;AAAA,MACb,cAAc;AAAA,MACd,aAAa;AAAA,MACb,iBAAiB;AAAA;AAAA,MACjB,YAAY;AAAA;AAAA,MACZ,YAAY;AAAA;AAAA,MACZ,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,UAAU;AAAA,QACV,SAAS;AAAA,QACT,OAAO;AAAA,QACP,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,aAAa;AAAA,UACX,gBAAgB;AAAA,UAChB,eAAe;AAAA,UACf,iBAAiB;AAAA,UACjB,iBAAiB;AAAA,UACjB,WAAW;AAAA,UACX,cAAc;AAAA,UACd,mBAAmB;AAAA,UACnB,gBAAgB;AAAA,QAClB;AAAA,MACD;AAAA,MACD,YAAY;AAAA,QACV;AAAA,UACE,MAAM;AAAA,UACN,cAAc;AAAA,QACf;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,cAAc;AAAA,QACf;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,cAAc;AAAA,QACf;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,cAAc;AAAA,QACf;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,cAAc;AAAA,QACf;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,cAAc;AAAA,QACf;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,cAAc;AAAA,QACf;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,cAAc;AAAA,QACf;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,cAAc;AAAA,QACf;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,cAAc;AAAA,QACf;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,cAAc;AAAA,QACf;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,cAAc;AAAA,QACf;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,cAAc;AAAA,QACf;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,cAAc;AAAA,QACf;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,cAAc;AAAA,QACf;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,cAAc;AAAA,QACf;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,cAAc;AAAA,QACf;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,cAAc;AAAA,QACf;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACD;AAAA,MACD,UAAU;AAAA,QACR;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACD;AAAA,MACD,WAAW;AAAA,QACT;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACD;AAAA,MACD,UAAU,CAAC,0BAA0B,wBAAwB;AAAA,MAC7D,YAAY;AAAA,QACV;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,cAAc;AACZ,aAAO;AAAA,QACL,QAAQ,GAAG,KAAK,kBAAkB,EAAE;AAAA,QACpC,YAAY,GAAG,KAAK,eAAe;AAAA,QACnC,QAAQ;AAAA;AAAA,MACV;AAAA,IACD;AAAA;AAAA,IAED,0BAA0B;AACxB,YAAM,QAAQ,CAAA;AAGd,UAAI,KAAK,KAAK,YAAY,mBAAmB,WAAW,KAAK,KAAK,YAAY,mBAAmB,SAAS;AAExG,cAAM,aAAa,KAAK,KAAK,YAAY,mBAAmB;AAAA,MAC5D,WAAS,KAAK,KAAK,YAAY,mBAAmB,YAAY;AAC9D,YAAI;AAEF,gBAAM,gBAAgB,SAAS,KAAK,KAAK,YAAY,iBAAiB,CAAC;AAGvE,cAAI,iBAAiB,KAAK,gBAAgB,KAAK,UAAU,QAAQ;AAC/D,kBAAM,WAAW,KAAK,UAAU,aAAa;AAC7C,gBAAI,UAAU;AACZ,oBAAM,aAAa;AAAA,mBACd;AACL,oBAAM,aAAa;AAAA,YACrB;AAAA,iBACK;AACL,kBAAM,aAAa;AAAA,UACrB;AAAA,QACA,SAAO,OAAO;AACd,gBAAM,aAAa;AAAA,QACrB;AAAA,iBACS,KAAK,KAAK,YAAY,mBAAmB,WAAW,KAAK,KAAK,YAAY,iBAAiB;AAGpG,cAAM,aAAa;AAAA,aACd;AAEL,cAAM,aAAa;AAAA,MACrB;AAEA,aAAO;AAAA,IACR;AAAA,IACD,YAAY;AAEV,UAAI,KAAK,KAAK,YAAY,cAAc,WAAW;AAEjD,eAAO;AAAA,UACL,QAAQ;AAAA,UACR,SAAS;AAAA;aAEN;AAEL,eAAO;AAAA,UACL,SAAS;AAAA;MAEb;AAAA,IACD;AAAA,IACD,mBAAmB;AAEjB,YAAM,QAAQ,EAAC,GAAG,KAAK,wBAAuB;AAK9C,YAAM,eAAe,KAAK,KAAK,YAAY,sBAAsB,SAAY,UACzD,CAAC,SAAS,SAAS,GAAG,EAAE,KAAK,KAAK,YAAY,iBAAiB;AAGnF,UAAI,KAAK,KAAK,YAAY,QAAQ;AAChC,cAAM,YAAY;AAAA,MACpB;AAEA,aAAO;AAAA,IACR;AAAA,IACD,gBAAgB;AACd,aAAO;AAAA,QACL,OAAO,KAAK,KAAK,YAAY,aAAa;AAAA,QAC1C,UAAU,KAAK,UAAU,KAAK,KAAK,YAAY,iBAAiB,CAAC,EAAE;AAAA;IAEtE;AAAA,IACD,qBAAqB;AACnB,YAAM,QAAQ;AAAA,QACZ,cAAc,KAAK,aAAa,KAAK,KAAK,YAAY,oBAAoB,CAAC;AAAA;AAG7E,UAAI,KAAK,KAAK,YAAY,cAAc;AACtC,cAAM,SAAS;AAAA,MACjB;AAEA,aAAO;AAAA,IACR;AAAA,IACD,kBAAkB;AAEhB,YAAM,cAAc,KAAK,cAAc;AACvC,aAAO;AAAA,QACL,IAAI,cAAc;AAAA;AAAA,QAClB,IAAI,cAAc;AAAA;AAAA,QAClB,IAAI,cAAc;AAAA;AAAA,QAClB,IAAI,cAAc;AAAA;AAAA,QAClB,IAAI,cAAc;AAAA;AAAA;IAErB;AAAA,IACD,oBAAoB;AAClB,YAAM,UAAU,KAAK;AACrB,aAAO;AAAA,QACL,QAAQ,GAAG,QAAQ,EAAE,MAAM,QAAQ,EAAE,MAAM,QAAQ,EAAE,MAAM,QAAQ,EAAE;AAAA;IAExE;AAAA,IACD,eAAe;AAEb,UAAI,KAAK,oBAAoB,GAAG;AAC9B,eAAO;AAAA,UACL,YAAY,GAAG,KAAK,iBAAiB;AAAA;MAEzC;AAGA,YAAM,gBAAgB,KAAK,oBAAoB;AAE/C,YAAM,iBAAiB,KAAK,eAAe,MAAM,OAAO;AACxD,YAAM,mBAAmB,gBAAgB;AAEzC,aAAO;AAAA,QACL,YAAY,GAAG,KAAK,kBAAkB,KAAK,gBAAgB,gBAAgB;AAAA;IAE9E;AAAA;AAAA,IAED,gBAAgB;AACd,YAAM,YAAY,KAAK,KAAK,YAAY;AAGxC,UAAI,cAAc,WAAW;AAE3B,eAAO;AAAA,UACL,OAAO;AAAA,UACP,YAAY;AAAA;MAEd,WAAS,KAAK,KAAK,YAAY,mBAAmB,cAC1C,KAAK,KAAK,YAAY,oBAAoB,aAC1C,KAAK,KAAK,YAAY,oBAAoB,WAAW;AAE7D,eAAO;AAAA,UACL,OAAO;AAAA,UACP,YAAY;AAAA;aAET;AAEL,eAAO;AAAA,UACL,OAAO;AAAA,UACP,YAAY;AAAA;MAEhB;AAAA,IACD;AAAA,EACF;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,MAAM,mBAAmB;AACvB,UAAI;AAEF,cAAMA,qBAAAA,YAAY;AAGlB,aAAK,kBAAiB;AAAA,MACtB,SAAO,OAAO;AACdC,sBAAc,MAAA,MAAA,SAAA,8BAAA,aAAa,KAAK;AAAA,MAClC;AAAA,IACD;AAAA;AAAA,IAGD,aAAa,GAAG;AACd,UAAI,QAAQ,EAAE,OAAO;AAGrB,cAAQ,MAAM,QAAQ,UAAU,EAAE;AAGlC,UAAI,MAAM,SAAS,IAAI;AACrB,gBAAQ,MAAM,MAAM,GAAG,EAAE;AAAA,MAC3B;AAGA,WAAK,KAAK,QAAQ;AAGlB,WAAK,cAAc,KAAK;AAAA,IACzB;AAAA;AAAA,IAGD,cAAc,OAAO;AACnB,UAAI,CAAC,OAAO;AACV,aAAK,aAAa;AAClB;AAAA,MACF;AAEA,UAAI,MAAM,SAAS,IAAI;AACrB,aAAK,aAAa;AAAA,MACpB,WAAW,CAAC,gBAAgB,KAAK,KAAK,GAAG;AACvC,aAAK,aAAa;AAAA,aACb;AACL,aAAK,aAAa;AAAA,MACpB;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB;AACd,UAAI,CAAC,KAAK,KAAK,OAAO;AACpB,aAAK,aAAa;AAClB;AAAA,MACF;AAEA,UAAI,CAACC,yBAAe,gBAAC,cAAc,KAAK,KAAK,KAAK,GAAG;AACnD,aAAK,aAAa;AAAA,aACb;AACL,aAAK,aAAa;AAAA,MACpB;AAAA,IACD;AAAA;AAAA,IAGD,aAAa;AACX,WAAK,KAAK,QAAQ;AAClB,WAAK,aAAa;AAAA,IACnB;AAAA,IACD,cAAc;AACZ,WAAK,KAAK,SAAS;AAAA,IACpB;AAAA,IACD,aAAa;AACX,WAAK,KAAK,QAAQ;AAClB,WAAK,aAAa;AAAA,IACnB;AAAA;AAAA,IAGD,MAAM,yBAAyB;;AAC7B,UAAI;AAEF,cAAM,aAAaF,iCAAY;AAC/B,cAAM,QAAQG,sBAAQC,aAAAA,SAAS;AAC/B,cAAM,cAAcD,sBAAQE,aAAAA,aAAa;AAEzC,YAAI,CAAC,YAAY;AACf;AAAA,QACF;AAEAJ,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAED,cAAM,SAAS,MAAMC,yCAAgB;AAErC,YAAI,OAAO,WAAW,OAAO,MAAM;AAEjC,gBAAM,aAAa,OAAO;AAG1B,gBAAM,cAAc;AAAA,YAClB,GAAG,KAAK;AAAA,YACR,IAAI,WAAW;AAAA,YACf,MAAM,WAAW,QAAQ;AAAA,YACzB,OAAO,WAAW,SAAS;AAAA,YAC3B,OAAO,WAAW,SAAS;AAAA,YAC3B,QAAQ,WAAW,UAAU;AAAA,YAC7B,UAAU,WAAW,YAAY;AAAA,YACjC,SAAS,WAAW,WAAW;AAAA,YAC/B,SAAS,WAAW,WAAW;AAAA,YAC/B,aAAa,WAAW,eAAe;AAAA,YACvC,QAAQ,WAAW,UAAU;AAAA;AAAA,YAE7B,aAAa;AAAA,cACX,GAAG,KAAK,KAAK;AAAA,cACb,GAAI,WAAW,eAAe,CAAE;AAAA;AAAA,cAEhC,kBAAgB,gBAAW,gBAAX,mBAAwB,qBAAkB,UAAK,KAAK,gBAAV,mBAAuB,mBAAkB;AAAA,cACnG,mBAAiB,gBAAW,gBAAX,mBAAwB,sBAAmB,UAAK,KAAK,gBAAV,mBAAuB,oBAAmB;AAAA,cACtG,mBAAiB,gBAAW,gBAAX,mBAAwB,sBAAmB,UAAK,KAAK,gBAAV,mBAAuB,oBAAmB;AAAA,cACtG,iBAAe,gBAAW,gBAAX,mBAAwB,mBAAkB,SAAY,WAAW,YAAY,kBAAiB,UAAK,KAAK,gBAAV,mBAAuB,kBAAiB;AAAA,cACrJ,aAAW,gBAAW,gBAAX,mBAAwB,gBAAa,UAAK,KAAK,gBAAV,mBAAuB,cAAa;AAAA,cACpF,qBAAmB,gBAAW,gBAAX,mBAAwB,uBAAsB,SAAY,WAAW,YAAY,sBAAqB,UAAK,KAAK,gBAAV,mBAAuB,sBAAqB;AAAA,cACrK,kBAAgB,gBAAW,gBAAX,mBAAwB,oBAAmB,SAAY,WAAW,YAAY,mBAAkB,UAAK,KAAK,gBAAV,mBAAuB,mBAAkB;AAAA,YAC3J;AAAA;AAGF,eAAK,OAAO;AAGZD,wBAAAA,MAAI,eAAe,YAAY,KAAK,IAAI;AAAA,eAUnC;AAELA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACZ,CAAC;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,8BAAc,gBAAgB,KAAK;AAAA,MACrC,UAAU;AACRA,sBAAG,MAAC,YAAW;AAAA,MACjB;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,qBAAqB,UAAU;AACnC,UAAI;AAEF,YAAID,qBAAAA,YAAY,cAAc;AAC5B,gBAAM,SAAS,MAAME,yBAAAA,gBAAgB,aAAa,QAAQ;AAE1D,cAAI,OAAO,WAAW,OAAO,KAAK;AAEhC,iBAAK,KAAK,SAAS,OAAO;AAG1B,iBAAK,aAAY;AAAA,iBAGZ;AAEL,iBAAK,KAAK,SAAS;AACnBD,0BAAc,MAAA,MAAA,SAAA,8BAAA,WAAW,OAAO,OAAO;AACvCA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,eACK;AAEL,eAAK,KAAK,SAAS;AACnBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,8BAAc,WAAW,KAAK;AAG9B,aAAK,KAAK,SAAS;AAGnB,YAAI,eAAe;AACnB,YAAI,MAAM,QAAQ,SAAS,KAAK,GAAG;AACjC,yBAAe;AAAA,QACjB,WAAW,MAAM,QAAQ,SAAS,WAAW,GAAG;AAC9C,yBAAe;AAAA,QACjB,WAAW,MAAM,QAAQ,SAAS,IAAI,GAAG;AACvC,yBAAe;AAAA,QACjB;AAEAA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACZ,CAAC;AAAA,MACH;AAAA,IAED;AAAA;AAAA,IAGD,wBAAwB;AACtBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,SAAS,IAAI;AAAA,QACxB,SAAS,CAAC,QAAQ;AAChB,kBAAO,IAAI,UAAQ;AAAA,YACjB,KAAK;AACH,mBAAK,sBAAqB;AAC1B;AAAA,YACF,KAAK;AACH,mBAAK,uBAAsB;AAC3B;AAAA,UACJ;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAID,wBAAwB;AACtBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,OAAO;AAAA,QACpB,SAAS,CAAC,QAAQ;AAEhB,eAAK,qBAAqB,IAAI,cAAc,CAAC,CAAC;AAAA,QAChD;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,yBAAyB;AACvBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,QAAQ;AAAA,QACrB,SAAS,CAAC,QAAQ;AAEhB,eAAK,qBAAqB,IAAI,cAAc,CAAC,CAAC;AAAA,QAChD;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,qBAAqB,UAAU;AACnC,UAAI;AAEFA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAGD,YAAID,qBAAAA,YAAY,cAAc;AAC5B,gBAAM,SAAS,MAAME,yBAAAA,gBAAgB,aAAa,QAAQ;AAE1D,cAAI,OAAO,WAAW,OAAO,KAAK;AAEhC,iBAAK,KAAK,SAAS,OAAO;AAG1B,iBAAK,aAAY;AAAA,iBAOZ;AAEL,iBAAK,KAAK,SAAS;AACnBD,0BAAc,MAAA,MAAA,SAAA,+BAAA,WAAW,OAAO,OAAO;AACvCA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,eACK;AAEL,eAAK,KAAK,SAAS;AACnBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,+BAAc,WAAW,KAAK;AAG9B,aAAK,KAAK,SAAS;AAGnB,YAAI,eAAe;AACnB,YAAI,MAAM,QAAQ,SAAS,KAAK,GAAG;AACjC,yBAAe;AAAA,QACjB,WAAW,MAAM,QAAQ,SAAS,WAAW,GAAG;AAC9C,yBAAe;AAAA,QACjB,WAAW,MAAM,QAAQ,SAAS,IAAI,GAAG;AACvC,yBAAe;AAAA,QACjB;AAEAA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACZ,CAAC;AAAA,MACH,UAAU;AACRA,sBAAG,MAAC,YAAW;AAAA,MACjB;AAAA,IACD;AAAA;AAAA,IAGD,eAAe,GAAG;AAChB,UAAI,EAAE,UAAU,EAAE,OAAO,WAAW;AAClC,cAAM,YAAY,EAAE,OAAO;AAC3BA,sBAAA,MAAA,MAAA,OAAA,+BAAY,YAAY,SAAS;AAQjC,aAAK,qBAAqB,SAAS;AAAA,aAC9B;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAOD,oBAAoB;AAClB,UAAI;AACF,cAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,cAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,cAAM,mBAAmBA,cAAAA,MAAI,eAAe,kBAAkB;AAE9D,YAAI,YAAY,OAAO;AACrB,eAAK,kBAAkB;AACvB,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT,SAAS,GAAG;AACVA,sBAAc,MAAA,MAAA,SAAA,+BAAA,YAAY,CAAC;AAC3B,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,YAAY;AACVA,oBAAAA,MAAI,WAAW,EAAE,KAAK,yBAA0B,CAAA;AAAA,IACjD;AAAA;AAAA,IAGD,WAAW;;AAET,UAAI,CAACD,qBAAAA,YAAY,cAAc;AAC7BC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AACfA,4BAAAA,MAAI,WAAW;AAAA,gBACb,KAAK;AAAA,cACP,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF,CAAC;AACD;AAAA,MACF;AAGA,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,YAAM,cAAcA,cAAG,MAAC,eAAe,aAAa,KAAK;AACzD,YAAM,mBAAmBA,cAAAA,MAAI,eAAe,kBAAkB;AAG9D,UAAI,YAAY;AAChB,UAAI,kBAAkB;AACpB,cAAM,MAAM,oBAAI;AAEhB,cAAM,gBAAgB,iBAAiB,QAAQ,OAAO,GAAG;AACzD,cAAM,aAAa,IAAI,KAAK,aAAa;AACzC,oBAAY,MAAM;AAAA,MACpB;AAGA,UAAI,KAAK,cAAc,GAAG;AAExB,cAAM,gBAAgB,YAAY,eAAe,KAAK,CAAC;AACvD,YAAI,CAAC,eAAe;AAClBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,aAAa;AAAA,YACb,YAAY;AAAA,YACZ,SAAS,CAAC,QAAQ;AAChB,kBAAI,IAAI,SAAS;AACf,qBAAK,UAAS;AAAA,cAChB;AAAA,YACF;AAAA,UACF,CAAC;AACD;AAAA,QACF;AAAA,iBACS,KAAK,cAAc,GAAG;AAE/B,cAAM,eAAe,YAAY,eAAe,KAAK,CAAC;AACtD,YAAI,CAAC,cAAc;AACjBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,aAAa;AAAA,YACb,YAAY;AAAA,YACZ,SAAS,CAAC,QAAQ;AAChB,kBAAI,IAAI,SAAS;AACf,qBAAK,UAAS;AAAA,cAChB;AAAA,YACF;AAAA,UACF,CAAC;AACD;AAAA,QACF;AAAA,MACF;AAIA,YAAM,kBAAkBA,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AAE1D,UAAI;AACJ,UAAI;AAEJ,UAAI,KAAK,cAAc,GAAG;AAExB,yBAAiB;AAAA,UACf,GAAG;AAAA,UACH,QAAQ,KAAK,KAAK,UAAU;AAAA,UAC5B,MAAM,KAAK,KAAK,QAAQ;AAAA,UACxB,UAAU,KAAK,KAAK,YAAY;AAAA,UAChC,SAAS,KAAK,KAAK,WAAW;AAAA,UAC9B,OAAO,KAAK,KAAK,SAAS;AAAA,UAC1B,OAAO,KAAK,KAAK,SAAS;AAAA,UAC1B,QAAQ,KAAK,KAAK,UAAU;AAAA,UAC5B,SAAS,KAAK,KAAK,WAAW;AAAA,UAC9B,aAAa,KAAK,KAAK,eAAe;AAAA;AAExC,yBAAiB;AAAA,aACZ;AAEL,yBAAiB;AAAA,UACf,GAAG;AAAA,UACH,aAAa;AAAA,YACX,GAAI,gBAAgB,eAAe,CAAE;AAAA,YACrC,kBAAgB,UAAK,KAAK,gBAAV,mBAAuB,mBAAkB;AAAA,YACzD,iBAAe,UAAK,KAAK,gBAAV,mBAAuB,kBAAiB;AAAA,YACvD,mBAAiB,UAAK,KAAK,gBAAV,mBAAuB,oBAAmB;AAAA,YAC3D,mBAAiB,UAAK,KAAK,gBAAV,mBAAuB,oBAAmB;AAAA,YAC3D,aAAW,UAAK,KAAK,gBAAV,mBAAuB,cAAa;AAAA,YAC/C,qBAAmB,UAAK,KAAK,gBAAV,mBAAuB,sBAAqB;AAAA,YAC/D,kBAAgB,UAAK,KAAK,gBAAV,mBAAuB,mBAAkB;AAAA,UAC3D;AAAA;AAEF,yBAAiB;AAAA,MACnB;AAGA,WAAK,qBAAqB,gBAAgB,cAAc;AAAA,IACzD;AAAA;AAAA,IAGD,MAAM,qBAAqB,gBAAgB,gBAAgB;AACzD,UAAI;AACFA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAGDA,sBAAAA,MAAI,eAAe,YAAY,cAAc;AAG7C,cAAM,aAAa,MAAMD,iCAAY;AAErC,YAAI,YAAY;AAEd,gBAAM,SAAS,MAAME,yBAAAA,gBAAgB,eAAe,cAAc;AAElE,cAAI,OAAO,SAAS;AAClBD,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YACZ,CAAC;AAGD,uBAAW,MAAM;AACf,mBAAK,uBAAsB;AAAA,YAC5B,GAAE,GAAG;AAAA,iBACD;AAELA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,iBAAiB;AAAA,cACxB,MAAM;AAAA,cACN,UAAU;AAAA,YACZ,CAAC;AACDA,0BAAc,MAAA,MAAA,SAAA,+BAAA,YAAY,OAAO,OAAO;AAAA,UAC1C;AAAA,eACK;AAELA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACZ,CAAC;AAAA,QACH;AAGA,mBAAW,MAAM;AACfA,wBAAG,MAAC,aAAY;AAAA,QACjB,GAAE,GAAI;AAAA,MAEP,SAAO,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,+BAAA,SAAS,KAAK;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACZ,CAAC;AAAA,MACH,UAAU;AACRA,sBAAG,MAAC,YAAW;AAAA,MACjB;AAAA,IACD;AAAA;AAAA,IAGD,eAAe,KAAK;AAElB,WAAK,KAAK,YAAY,iBAAiB;AAGvC,cAAO,KAAG;AAAA,QACR,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,eAAe;AAAA,YACf,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,eAAe;AAAA,YACf,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,eAAe;AAAA,YACf,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,eAAe;AAAA,YACf,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,eAAe;AAAA,YACf,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,eAAe;AAAA,YACf,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,eAAe;AAAA,YACf,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,MACJ;AAGA,WAAK,aAAY;AAAA,IAClB;AAAA;AAAA,IAGD,aAAa,OAAO;AAClB,WAAK,KAAK,YAAY,iBAAiB;AACvC,WAAK,KAAK,YAAY,kBAAkB;AACxC,WAAK,KAAK,YAAY,kBAAkB;AACxC,WAAK,KAAK,YAAY,gBAAgB;AAGtC,UAAI,UAAU,aAAa,UAAU,aAAa,UAAU,aACxD,UAAU,aAAa,UAAU,aAAa,UAAU,WAAW;AACrE,aAAK,KAAK,YAAY,YAAY;AAAA,aAC7B;AACL,aAAK,KAAK,YAAY,YAAY;AAAA,MACpC;AAGA,WAAK,aAAY;AAAA,IAClB;AAAA;AAAA,IAGD,cAAc,KAAK;AAGjB,UAAI,OAAO,KAAK,MAAM,KAAK,UAAU,QAAQ;AAC3C,aAAK,KAAK,YAAY,iBAAiB;AACvC,aAAK,KAAK,YAAY,gBAAgB;AACtC,aAAK,KAAK,YAAY,kBAAkB;AACxC,aAAK,KAAK,YAAY,kBAAkB;AACxC,aAAK,KAAK,YAAY,YAAY;AAKlC,aAAK,aAAY;AAGjB,mBAAW,MAAM;AACf,eAAK,aAAY;AAAA,QAClB,GAAE,GAAG;AAAA,aACD;AACLA,sBAAA,MAAA,MAAA,SAAA,+BAAc,YAAY,GAAG;AAAA,MAC/B;AAAA,IACD;AAAA;AAAA,IAGD,aAAa,KAAK;AAChB,WAAK,KAAK,YAAY,iBAAiB;AACvC,WAAK,KAAK,YAAY,kBAAkB;AACxC,WAAK,KAAK,YAAY,kBAAkB;AACxC,WAAK,KAAK,YAAY,gBAAgB;AACtC,WAAK,KAAK,YAAY,YAAY;AAGlC,WAAK,aAAY;AAGjB,iBAAW,MAAM;AACf,aAAK,aAAY;AAAA,MAClB,GAAE,GAAG;AAAA,IACP;AAAA;AAAA,IAGD,eAAe,OAAO;AACpB,WAAK,KAAK,YAAY,YAAY;AAGlC,WAAK,aAAY;AAAA,IAClB;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,SAAS,OAAO,QAAQ;AACtB,gBAAM,eAAe,IAAI,cAAc,CAAC;AAExC,cAAI;AACFA,0BAAAA,MAAI,YAAY;AAAA,cACd,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAGD,iBAAK,KAAK,YAAY,iBAAiB;AACvC,iBAAK,KAAK,YAAY,kBAAkB;AACxC,iBAAK,KAAK,YAAY,kBAAkB;AACxC,iBAAK,KAAK,YAAY,gBAAgB;AACtC,iBAAK,KAAK,YAAY,YAAY;AAClC,iBAAK,aAAY;AAGjB,gBAAID,qBAAAA,YAAY,cAAc;AAC5B,oBAAM,SAAS,MAAM,KAAK,oBAAoB,YAAY;AAE1D,kBAAI,OAAO,WAAW,OAAO,KAAK;AAEhC,qBAAK,KAAK,YAAY,kBAAkB,OAAO;AAC/C,qBAAK,aAAY;AAEjBC,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACR,CAAC;AAAA,qBACI;AACLA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACR,CAAC;AAAA,cACH;AAAA,mBACK;AACLA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACA,SAAO,OAAO;AACdA,0BAAA,MAAA,MAAA,SAAA,+BAAc,aAAa,KAAK;AAChCA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH,UAAU;AACRA,0BAAG,MAAC,YAAW;AAAA,UACjB;AAAA,QACD;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAA,MAAA,SAAA,+BAAc,WAAW,GAAG;AAC5BA,wBAAG,MAAC,UAAU,EAAE,OAAO,cAAc,MAAM,OAAO,CAAC;AAAA,QACrD;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB,WAAW;AAC1B,UAAI,CAAC,WAAW;AACd,eAAO;AAAA,MACT;AAOA,UAAI,UAAU,WAAW,UAAU,GAAG;AAEpC,eAAO;AAAA,MACT,WAAW,UAAU,WAAW,aAAa,GAAG;AAE9C,eAAO;AAAA,MACT,WAAW,UAAU,WAAW,SAAS,GAAG;AAE1C,eAAO;AAAA,MACT,WAAW,UAAU,WAAW,GAAG,GAAG;AAEpC,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,MAAM,oBAAoB,UAAU;AAClC,UAAI;AACF,YAAI,CAACD,qBAAAA,YAAY,cAAc;AAC7B,gBAAM,IAAI,MAAM,MAAM;AAAA,QACxB;AAEA,cAAM,QAAQA,iCAAY;AAE1B,eAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,gBAAM,YAAY,GAAGM,aAAY,YAAA;AAEjCL,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,YACL;AAAA,YACA,MAAM;AAAA,YACN,QAAQ;AAAA,cACN,iBAAiB,UAAU,KAAK;AAAA,YACjC;AAAA,YACD,SAAS,CAAC,QAAQ;AAChB,kBAAI;AACFA,8BAAY,MAAA,MAAA,OAAA,+BAAA,WAAW,GAAG;AAE1B,oBAAI,IAAI,eAAe,KAAK;AAC1B,yBAAO,IAAI,MAAM,YAAY,IAAI,UAAU,EAAE,CAAC;AAC9C;AAAA,gBACF;AAEA,oBAAI,CAAC,IAAI,MAAM;AACb,yBAAO,IAAI,MAAM,UAAU,CAAC;AAC5B;AAAA,gBACF;AAEA,sBAAM,OAAO,KAAK,MAAM,IAAI,IAAI;AAChCA,8BAAA,MAAA,MAAA,OAAA,+BAAY,WAAW,IAAI;AAE3B,oBAAI,KAAK,SAAS,KAAK;AACrB,0BAAQ;AAAA,oBACN,SAAS;AAAA,oBACT,MAAM,KAAK;AAAA,oBACX,KAAK,KAAK,KAAK;AAAA,kBACjB,CAAC;AAAA,uBACI;AACL,yBAAO,IAAI,MAAM,KAAK,WAAW,MAAM,CAAC;AAAA,gBAC1C;AAAA,cACA,SAAO,OAAO;AACdA,8BAAc,MAAA,MAAA,SAAA,+BAAA,aAAa,KAAK;AAChC,uBAAO,IAAI,MAAM,aAAa,MAAM,OAAO,EAAE,CAAC;AAAA,cAChD;AAAA,YACD;AAAA,YACD,MAAM,CAAC,UAAU;AACfA,4BAAc,MAAA,MAAA,SAAA,+BAAA,WAAW,KAAK;AAC9B,qBAAO,IAAI,MAAM,QAAQ,CAAC;AAAA,YAC5B;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACD,SAAO,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,+BAAc,WAAW,KAAK;AAC9B,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS,MAAM,WAAW;AAAA;MAE9B;AAAA,IACD;AAAA,IAED,gBAAgB,KAAK;AACnB,WAAK,KAAK,YAAY,oBAAoB;AAC1C,WAAK,KAAK,YAAY,eAAe,KAAK,gBAAgB,GAAG;AAAA,IAC9D;AAAA,IACD,eAAe,KAAK;AAClB,WAAK,KAAK,YAAY,mBAAmB;AACzC,WAAK,KAAK,YAAY,cAAc,KAAK,eAAe,GAAG;AAAA,IAC5D;AAAA,IACD,WAAW,OAAO;AAChB,WAAK,KAAK,YAAY,iBAAiB;AACvC,WAAK,KAAK,YAAY,kBAAkB;AAExC,WAAK,KAAK,YAAY,kBAAkB;AAExC,UAAI,UAAU,aAAa,UAAU,aAAa,UAAU,aACxD,UAAU,aAAa,UAAU,aAAa,UAAU,WAAW;AACrE,aAAK,KAAK,YAAY,YAAY;AAAA,aAC7B;AACL,aAAK,KAAK,YAAY,YAAY;AAAA,MACpC;AAAA,IACD;AAAA,IACD,aAAa,OAAO;AAClB,WAAK,KAAK,YAAY,YAAY;AAAA,IACnC;AAAA,IACD,aAAa,KAAK;AAChB,WAAK,KAAK,YAAY,iBAAiB;AAGvC,cAAO,KAAG;AAAA,QACR,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,eAAe;AAAA,YACf,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,eAAe;AAAA,YACf,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,eAAe;AAAA,YACf,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,eAAe;AAAA,YACf,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,eAAe;AAAA,YACf,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,eAAe;AAAA,YACf,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,eAAe;AAAA,YACf,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,QACF,KAAK;AACH,eAAK,KAAK,cAAc;AAAA,YACtB,GAAG,KAAK,KAAK;AAAA,YACb,gBAAgB;AAAA,YAChB,eAAe;AAAA;AAAA,YACf,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,gBAAgB;AAAA;AAElB;AAAA,MACJ;AAAA,IACD;AAAA,IACD,YAAY,KAAK;AACf,WAAK,KAAK,YAAY,iBAAiB;AACvC,WAAK,KAAK,YAAY,gBAAgB;AAEtC,WAAK,KAAK,YAAY,kBAAkB;AACxC,WAAK,KAAK,YAAY,kBAAkB;AACxC,WAAK,KAAK,YAAY,YAAY;AAGlC,WAAK,aAAY;AAAA,IAClB;AAAA,IACD,UAAU,UAAU;AAClB,WAAK,KAAK,YAAY,SAAS;AAAA,IAChC;AAAA,IACD,YAAY,KAAK;AACf,WAAK,KAAK,YAAY,gBAAgB;AAAA,IACvC;AAAA,IACD,gBAAgB,WAAW;AACzB,WAAK,KAAK,YAAY,eAAe;AAAA,IACtC;AAAA,IACD,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,eAAe;AAAA,IAYd;AAAA,IACD,gBAAgB;AACd,WAAK,KAAK,YAAY,iBAAiB;AAAA,IACxC;AAAA,IACD,WAAW,KAAK;AACd,WAAK,KAAK,YAAY,iBAAiB;AACvC,WAAK,KAAK,YAAY,kBAAkB;AAExC,WAAK,KAAK,YAAY,kBAAkB;AACxC,WAAK,KAAK,YAAY,gBAAgB;AACtC,WAAK,KAAK,YAAY,YAAY;AAGlC,WAAK,aAAY;AAGjB,iBAAW,MAAM;AACf,aAAK,aAAY;AAAA,MAClB,GAAE,GAAG;AAAA,IACP;AAAA,IACD,eAAe,GAAG;AAChB,WAAK,cAAc,EAAE,OAAO;AAC5B,WAAK,aAAa,KAAK,YAAY,KAAK,GAAG;AAAA,IAC5C;AAAA,IACD,cAAc;AACZA,oBAAAA,MAAI,YAAY;AAAA,QACd,MAAM;AAAA,QACN,SAAS,CAAC,QAAQ;AAChB,gBAAM,WAAW,IAAI;AACrB,gBAAM,YAAY,IAAI;AAGtBA,wBAAAA,MAAI,QAAQ;AAAA,YACV,KAAK,oDAAoD,QAAQ,IAAI,SAAS;AAAA,YAC9E,SAAS,CAACM,SAAQ;AAChB,kBAAIA,KAAI,QAAQA,KAAI,KAAK,QAAQ;AAC/B,sBAAM,UAAUA,KAAI,KAAK,OAAO;AAChC,sBAAM,WAAWA,KAAI,KAAK,OAAO,kBAAkB;AACnD,sBAAM,OAAOA,KAAI,KAAK,OAAO,kBAAkB;AAC/C,sBAAM,WAAWA,KAAI,KAAK,OAAO,kBAAkB;AAEnD,qBAAK,cAAc,CAAC,UAAU,MAAM,QAAQ;AAC5C,qBAAK,aAAa,KAAK,YAAY,KAAK,GAAG;AAC3C,qBAAK,gBAAgB,QAAQ,QAAQ,KAAK,YAAY,EAAE;AAExDN,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACR,CAAC;AAAA,cACH;AAAA,YACD;AAAA,YACD,MAAM,MAAM;AACVA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACF;AAAA,QACD,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,iBAAiB;AACf,YAAM,cAAc,KAAK,aAAa,MAAM,KAAK;AACjD,WAAK,KAAK,UAAU,YAAY,KAAI;AACpC,WAAK,mBAAmB;AAGxBA,oBAAG,MAAC,eAAe,eAAe;AAAA,QAChC,aAAa,KAAK;AAAA,QAClB,YAAY,KAAK;AAAA,QACjB,eAAe,KAAK;AAAA,MACtB,CAAC;AAAA,IACF;AAAA,IACD,uBAAuB;AAErB,UAAI,KAAK,mBAAmB,GAAG;AAC7B,cAAM,aAAa,KAAK,kBAAkB,KAAK,KAAK;AACpD,cAAM,iBAAiB,KAAK,eAAe,MAAM,OAAO;AACxD,cAAM,mBAAmB,KAAK,mBAAmB;AACjD,cAAM,eAAe,aAAa;AAWlC,cAAM,QAAQA,oBAAI;AAClB,cAAM,OAAO,eAAe,EAAE,mBAAmB,UAAQ;AACvD,cAAI,MAAM;AAER,iBAAK,oBAAoB;AAAA,UAC3B;AAAA,QACF,CAAC,EAAE,KAAI;AAAA,MAET;AAAA,IACD;AAAA;AAAA,IAED,sBAAsB;AACpB,WAAK,mBAAmB;AAAA,IACzB;AAAA;AAAA,IAGD,MAAM,qBAAqB;AAEzB,YAAM,cAAc;AAAA,QAClB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,UAAU;AAAA,QACV,SAAS;AAAA,QACT,OAAO;AAAA,QACP,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aAAa;AAAA,UACX,gBAAgB;AAAA,UAChB,eAAe;AAAA,UACf,iBAAiB;AAAA,UACjB,iBAAiB;AAAA,UACjB,WAAW;AAAA,UACX,UAAU;AAAA,UACV,aAAa;AAAA,UACb,cAAc;AAAA,UACd,mBAAmB;AAAA,UACnB,gBAAgB;AAAA,QAClB;AAAA;AAIF,WAAK,OAAO,EAAE,GAAG;AAGjB,UAAID,qBAAAA,YAAY,cAAc;AAC5B,YAAI;AAEFC,wBAAAA,MAAI,YAAY;AAAA,YACd,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAED,gBAAM,SAAS,MAAMC,yCAAgB;AAErC,cAAI,OAAO,WAAW,OAAO,MAAM;AACjC,kBAAM,aAAa,OAAO;AAG1B,iBAAK,OAAO;AAAA,cACV,GAAG;AAAA,cACH,IAAI,WAAW;AAAA,cACf,MAAM,WAAW,QAAQ,YAAY;AAAA,cACrC,OAAO,WAAW,SAAS,YAAY;AAAA,cACvC,OAAO,WAAW,SAAS,YAAY;AAAA,cACvC,QAAQ,WAAW,UAAU,YAAY;AAAA,cACzC,UAAU,WAAW,YAAY,YAAY;AAAA,cAC7C,SAAS,WAAW,WAAW,YAAY;AAAA,cAC3C,SAAS,WAAW,WAAW,YAAY;AAAA,cAC3C,aAAa,WAAW,eAAe,YAAY;AAAA,cACnD,QAAQ,WAAW,UAAU,YAAY;AAAA;AAAA,cAEzC,aAAa;AAAA,gBACX,GAAG,YAAY;AAAA,gBACf,GAAI,WAAW,eAAe,CAAE;AAAA,cAClC;AAAA;AAIFD,0BAAAA,MAAI,eAAe,YAAY,KAAK,IAAI;AAAA,iBAEnC;AACL,iBAAK,qBAAqB,WAAW;AAAA,UACvC;AAAA,QAEA,SAAO,OAAO;AACdA,wBAAA,MAAA,MAAA,SAAA,+BAAc,eAAe,KAAK;AAClC,eAAK,qBAAqB,WAAW;AAAA,QACvC,UAAU;AACRA,wBAAG,MAAC,YAAW;AAAA,QACjB;AAAA,aAEK;AACL,aAAK,qBAAqB,WAAW;AAAA,MACvC;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB,aAAa;AAChC,UAAI;AAEF,cAAM,QAAQA,cAAAA,MAAI,eAAe,UAAU;AAG3C,cAAM,cAAc,SAAS,OAAO,UAAU,YAC3B,OAAO,KAAK,KAAK,EAAE,SAAS;AAE/C,YAAI,aAAa;AAEf,eAAK,OAAO;AAAA,YACV,GAAG;AAAA,YACH,GAAG;AAAA;AAIL,cAAI,MAAM,aAAa;AACrB,iBAAK,KAAK,cAAc;AAAA,cACtB,GAAG,YAAY;AAAA,cACf,GAAG,MAAM;AAAA;iBAEN;AACL,iBAAK,KAAK,cAAc,EAAE,GAAG,YAAY,YAAU;AAAA,UACrD;AAAA,eACK;AAEL,eAAK,OAAO,EAAE,GAAG;AAGjBA,wBAAAA,MAAI,eAAe,YAAY,KAAK,IAAI;AAAA,QAC1C;AAAA,MACA,SAAO,OAAO;AAEd,aAAK,OAAO,EAAE,GAAG;AAEjB,YAAI;AACFA,wBAAAA,MAAI,eAAe,YAAY,KAAK,IAAI;AAAA,QAC1C,SAAS,GAAG;AAAA,QAEZ;AAAA,MACF;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB;AAChB,UAAI;AACF,cAAM,eAAeA,cAAAA,MAAI,eAAe,aAAa;AACrD,YAAI,cAAc;AAChB,eAAK,cAAc,aAAa,eAAe,CAAC,IAAI,IAAI,EAAE;AAC1D,eAAK,aAAa,aAAa,cAAc;AAC7C,eAAK,gBAAgB,aAAa,iBAAiB;AAAA,QACrD;AAAA,MACA,SAAO,OAAO;AAAA,MAEhB;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB;AACf,UAAI;AACF,cAAM,aAAaA,oBAAI;AACvB,cAAM,kBAAkB,WAAW,mBAAmB;AACtD,aAAK,kBAAkB;AACvB,aAAK,cAAc,WAAW,eAAe;AAC7C,aAAK,eAAe,WAAW,gBAAgB;AAC/C,aAAK,cAAc,WAAW,cAAc;AAAA,MAC5C,SAAO,OAAO;AACd,aAAK,kBAAkB;AAAA,MACzB;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB;AAChB,WAAK,UAAU,MAAM;AACnB,aAAK,aAAY;AAGjB,mBAAW,MAAM;AACf,eAAK,aAAY;AAAA,QAClB,GAAE,GAAG;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACD;AAAA,EACD,UAAU;AAMR,SAAK,kBAAiB;AAAA,EACvB;AAAA,EACD,gBAAgB;AAAA,EAIf;AAAA,EACD,UAAU;AAER,eAAW,MAAM;AACf,YAAM,QAAQA,oBAAI;AAClB,YAAM,OAAO,cAAc,EAAE,mBAAmB,UAAQ;AACtD,YAAI,MAAM;AACR,eAAK,mBAAmB,KAAK;AAE7B,eAAK,UAAU,MAAM;AACnB,iBAAK,qBAAoB;AAAA,UAC3B,CAAC;AAAA,QACH;AAAA,MACF,CAAC,EAAE,KAAI;AAAA,IACR,GAAE,EAAE;AAAA,EACN;AAAA,EACD,MAAM,SAAS;AAEb,SAAK,kBAAiB;AAGtB,SAAK,eAAc;AAGnB,SAAK,gBAAe;AAGpB,UAAM,KAAK;AAGX,SAAK,gBAAe;AAAA,EACrB;AAAA,EAED,SAAS;AAEP,SAAK,iBAAgB;AAAA,EACvB;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7xEA,GAAG,WAAW,eAAe;"}