<template>
	<view class="content">
		<image class="logo" src="/static/logo.png"></image>
		<view class="text-area">
			<text class="title">{{title}}</text>
		</view>
	</view>
</template>

<script>
	import userService from '@/services/userService.js';

	export default {
		data() {
			return {
				title: 'Hello'
			}
		},
		onLoad() {
			// 同步会员状态
			this.syncMemberStatus();
		},

		onShow() {
			// 页面显示时同步会员状态
			this.syncMemberStatus();
		},
		methods: {
			// 同步会员状态
			async syncMemberStatus() {
				try {
					// 静默同步会员状态
					await userService.syncMemberStatus();
				} catch (error) {
					console.error('会员状态同步失败:', error);
				}
			}
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.logo {
		height: 200rpx;
		width: 200rpx;
		margin-top: 200rpx;
		margin-left: auto;
		margin-right: auto;
		margin-bottom: 50rpx;
	}

	.text-area {
		display: flex;
		justify-content: center;
	}

	.title {
		font-size: 36rpx;
		color: #8f8f94;
	}
</style>
