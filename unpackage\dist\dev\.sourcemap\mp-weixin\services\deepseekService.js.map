{"version": 3, "file": "deepseekService.js", "sources": ["services/deepseekService.js"], "sourcesContent": ["/**\n * DeepSeek API 服务\n * 用于调用DeepSeek AI接口生成企业介绍内容\n */\n\nclass DeepSeekService {\n\tconstructor() {\n\t\t// DeepSeek API配置\n\t\tthis.baseURL = 'https://api.deepseek.com/v1';\n\t\tthis.apiKey = ''; // 需要配置你的API Key\n\t\tthis.model = 'deepseek-chat';\n\t}\n\n\t/**\n\t * 设置API Key\n\t * @param {string} apiKey - DeepSeek API Key\n\t */\n\tsetApiKey(apiKey) {\n\t\tthis.apiKey = apiKey;\n\t}\n\n\t/**\n\t * 调用DeepSeek API\n\t * @param {string} prompt - 提示词\n\t * @param {Object} options - 可选参数\n\t * @returns {Promise<string>} - 生成的内容\n\t */\n\tasync chat(prompt, options = {}) {\n\t\tif (!this.apiKey) {\n\t\t\tthrow new Error('请先设置DeepSeek API Key');\n\t\t}\n\n\t\tconst requestData = {\n\t\t\tmodel: this.model,\n\t\t\tmessages: [\n\t\t\t\t{\n\t\t\t\t\trole: 'user',\n\t\t\t\t\tcontent: prompt\n\t\t\t\t}\n\t\t\t],\n\t\t\tmax_tokens: options.maxTokens || 1000,\n\t\t\ttemperature: options.temperature || 0.7,\n\t\t\tstream: false\n\t\t};\n\n\t\ttry {\n\t\t\tconst response = await uni.request({\n\t\t\t\turl: `${this.baseURL}/chat/completions`,\n\t\t\t\tmethod: 'POST',\n\t\t\t\theader: {\n\t\t\t\t\t'Content-Type': 'application/json',\n\t\t\t\t\t'Authorization': `Bearer ${this.apiKey}`\n\t\t\t\t},\n\t\t\t\tdata: requestData,\n\t\t\t\ttimeout: 30000\n\t\t\t});\n\n\t\t\tif (response.statusCode === 200 && response.data.choices && response.data.choices.length > 0) {\n\t\t\t\treturn response.data.choices[0].message.content.trim();\n\t\t\t} else {\n\t\t\t\tthrow new Error(response.data.error?.message || '调用DeepSeek API失败');\n\t\t\t}\n\t\t} catch (error) {\n\t\t\tconsole.error('DeepSeek API调用错误:', error);\n\t\t\tthrow new Error('网络错误或API调用失败，请检查网络连接和API Key');\n\t\t}\n\t}\n\n\t/**\n\t * 生成企业介绍\n\t * @param {string} companyName - 企业名称\n\t * @param {string} industry - 行业类型（可选）\n\t * @param {string} keywords - 关键词（可选）\n\t * @returns {Promise<string>} - 生成的企业介绍\n\t */\n\tasync generateCompanyDescription(companyName, industry = '', keywords = '') {\n\t\tlet prompt = `请为企业\"${companyName}\"生成一段专业的企业介绍，要求：\n1. 内容简洁明了，突出企业特色\n2. 字数控制在100-200字之间\n3. 语言正式、专业\n4. 突出企业的核心优势和价值`;\n\n\t\tif (industry) {\n\t\t\tprompt += `\\n5. 企业所属行业：${industry}`;\n\t\t}\n\n\t\tif (keywords) {\n\t\t\tprompt += `\\n6. 需要体现的关键词：${keywords}`;\n\t\t}\n\n\t\tprompt += '\\n\\n请直接返回企业介绍内容，不需要其他说明文字。';\n\n\t\treturn await this.chat(prompt, { maxTokens: 500, temperature: 0.8 });\n\t}\n\n\t/**\n\t * 优化企业介绍\n\t * @param {string} currentDescription - 当前的企业介绍\n\t * @param {string} requirements - 优化要求\n\t * @returns {Promise<string>} - 优化后的企业介绍\n\t */\n\tasync optimizeCompanyDescription(currentDescription, requirements = '') {\n\t\tlet prompt = `请优化以下企业介绍，使其更加专业和吸引人：\n\n当前介绍：\n${currentDescription}\n\n优化要求：\n1. 保持核心信息不变\n2. 语言更加精炼和专业\n3. 突出企业优势和特色\n4. 字数控制在100-200字之间`;\n\n\t\tif (requirements) {\n\t\t\tprompt += `\\n5. 特殊要求：${requirements}`;\n\t\t}\n\n\t\tprompt += '\\n\\n请直接返回优化后的企业介绍，不需要其他说明文字。';\n\n\t\treturn await this.chat(prompt, { maxTokens: 500, temperature: 0.7 });\n\t}\n\n\t/**\n\t * 生成企业名称建议\n\t * @param {string} industry - 行业类型\n\t * @param {string} keywords - 关键词\n\t * @param {number} count - 生成数量\n\t * @returns {Promise<Array<string>>} - 企业名称建议列表\n\t */\n\tasync generateCompanyNames(industry, keywords, count = 5) {\n\t\tconst prompt = `请为${industry}行业的企业生成${count}个专业的企业名称建议，要求：\n1. 名称简洁易记\n2. 体现行业特色\n3. 寓意积极向上\n4. 适合商业使用\n5. 关键词：${keywords}\n\n请以JSON数组格式返回，例如：[\"名称1\", \"名称2\", \"名称3\"]`;\n\n\t\tconst response = await this.chat(prompt, { maxTokens: 300, temperature: 0.9 });\n\t\t\n\t\ttry {\n\t\t\t// 尝试解析JSON\n\t\t\tconst names = JSON.parse(response);\n\t\t\treturn Array.isArray(names) ? names : [response];\n\t\t} catch (error) {\n\t\t\t// 如果不是JSON格式，尝试按行分割\n\t\t\treturn response.split('\\n').filter(line => line.trim()).slice(0, count);\n\t\t}\n\t}\n\n\t/**\n\t * 检查API Key是否有效\n\t * @returns {Promise<boolean>} - 是否有效\n\t */\n\tasync validateApiKey() {\n\t\ttry {\n\t\t\tawait this.chat('测试', { maxTokens: 10 });\n\t\t\treturn true;\n\t\t} catch (error) {\n\t\t\treturn false;\n\t\t}\n\t}\n}\n\n// 创建单例实例\nconst deepseekService = new DeepSeekService();\n\nexport default deepseekService;\n"], "names": ["uni"], "mappings": ";;AAKA,MAAM,gBAAgB;AAAA,EACrB,cAAc;AAEb,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,QAAQ;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,UAAU,QAAQ;AACjB,SAAK,SAAS;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,MAAM,KAAK,QAAQ,UAAU,IAAI;;AAChC,QAAI,CAAC,KAAK,QAAQ;AACjB,YAAM,IAAI,MAAM,sBAAsB;AAAA,IACtC;AAED,UAAM,cAAc;AAAA,MACnB,OAAO,KAAK;AAAA,MACZ,UAAU;AAAA,QACT;AAAA,UACC,MAAM;AAAA,UACN,SAAS;AAAA,QACT;AAAA,MACD;AAAA,MACD,YAAY,QAAQ,aAAa;AAAA,MACjC,aAAa,QAAQ,eAAe;AAAA,MACpC,QAAQ;AAAA,IACX;AAEE,QAAI;AACH,YAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,QAClC,KAAK,GAAG,KAAK,OAAO;AAAA,QACpB,QAAQ;AAAA,QACR,QAAQ;AAAA,UACP,gBAAgB;AAAA,UAChB,iBAAiB,UAAU,KAAK,MAAM;AAAA,QACtC;AAAA,QACD,MAAM;AAAA,QACN,SAAS;AAAA,MACb,CAAI;AAED,UAAI,SAAS,eAAe,OAAO,SAAS,KAAK,WAAW,SAAS,KAAK,QAAQ,SAAS,GAAG;AAC7F,eAAO,SAAS,KAAK,QAAQ,CAAC,EAAE,QAAQ,QAAQ;MACpD,OAAU;AACN,cAAM,IAAI,QAAM,cAAS,KAAK,UAAd,mBAAqB,YAAW,kBAAkB;AAAA,MAClE;AAAA,IACD,SAAQ,OAAO;AACfA,oBAAc,MAAA,MAAA,SAAA,qCAAA,qBAAqB,KAAK;AACxC,YAAM,IAAI,MAAM,8BAA8B;AAAA,IAC9C;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,MAAM,2BAA2B,aAAa,WAAW,IAAI,WAAW,IAAI;AAC3E,QAAI,SAAS,QAAQ,WAAW;AAAA;AAAA;AAAA;AAAA;AAMhC,QAAI,UAAU;AACb,gBAAU;AAAA,YAAe,QAAQ;AAAA,IACjC;AAED,QAAI,UAAU;AACb,gBAAU;AAAA,cAAiB,QAAQ;AAAA,IACnC;AAED,cAAU;AAEV,WAAO,MAAM,KAAK,KAAK,QAAQ,EAAE,WAAW,KAAK,aAAa,IAAG,CAAE;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,MAAM,2BAA2B,oBAAoB,eAAe,IAAI;AACvE,QAAI,SAAS;AAAA;AAAA;AAAA,EAGb,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQlB,QAAI,cAAc;AACjB,gBAAU;AAAA,UAAa,YAAY;AAAA,IACnC;AAED,cAAU;AAEV,WAAO,MAAM,KAAK,KAAK,QAAQ,EAAE,WAAW,KAAK,aAAa,IAAG,CAAE;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,MAAM,qBAAqB,UAAU,UAAU,QAAQ,GAAG;AACzD,UAAM,SAAS,KAAK,QAAQ,UAAU,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,SAKpC,QAAQ;AAAA;AAAA;AAIf,UAAM,WAAW,MAAM,KAAK,KAAK,QAAQ,EAAE,WAAW,KAAK,aAAa,IAAG,CAAE;AAE7E,QAAI;AAEH,YAAM,QAAQ,KAAK,MAAM,QAAQ;AACjC,aAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,QAAQ;AAAA,IAC/C,SAAQ,OAAO;AAEf,aAAO,SAAS,MAAM,IAAI,EAAE,OAAO,UAAQ,KAAK,KAAM,CAAA,EAAE,MAAM,GAAG,KAAK;AAAA,IACtE;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,iBAAiB;AACtB,QAAI;AACH,YAAM,KAAK,KAAK,MAAM,EAAE,WAAW,GAAE,CAAE;AACvC,aAAO;AAAA,IACP,SAAQ,OAAO;AACf,aAAO;AAAA,IACP;AAAA,EACD;AACF;AAGK,MAAC,kBAAkB,IAAI,gBAAe;;"}