"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      // 状态栏高度
      statusBarHeight: 0,
      // 企业版提示
      showPremiumNotice: false,
      // 展开状态
      expandStatus: {
        info: true,
        images: false,
        honors: false
      },
      // 企业数据
      companyData: {
        showSection: true,
        // 基本信息
        name: "",
        showName: true,
        desc: "",
        showDesc: true,
        slogan: "",
        showSlogan: true,
        address: "",
        showAddress: true,
        // 图片
        images: [],
        // 荣誉
        honors: []
      },
      // 荣誉弹窗
      showHonorModal: false,
      currentHonor: {
        title: "",
        image: ""
      }
    };
  },
  computed: {
    navbarStyle() {
      return {
        height: `${this.statusBarHeight + 45}px`
      };
    },
    mainStyle() {
      return {
        marginTop: `${this.statusBarHeight + 55}px`
      };
    }
  },
  onLoad() {
    this.getSystemInfo();
    this.loadData();
  },
  methods: {
    // 获取系统信息
    getSystemInfo() {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      this.statusBarHeight = systemInfo.statusBarHeight || 0;
    },
    // 返回
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 加载数据
    loadData() {
      common_vendor.index.__f__("log", "at pages/card/company.vue:352", "加载企业数据");
    },
    // 保存数据
    saveData() {
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success"
        });
      }, 1e3);
    },
    // 主开关变化
    onSwitchChange(e) {
      this.companyData.showSection = e.detail.value;
    },
    // 切换展开状态
    toggleExpand(section) {
      this.expandStatus[section] = !this.expandStatus[section];
    },
    // 上传图片
    uploadImage() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          this.companyData.images.push(tempFilePath);
        }
      });
    },
    // 删除图片
    deleteImage(index) {
      common_vendor.index.showModal({
        title: "确认删除",
        content: "确定要删除这张图片吗？",
        success: (res) => {
          if (res.confirm) {
            this.companyData.images.splice(index, 1);
          }
        }
      });
    },
    // 添加荣誉
    addHonor() {
      this.currentHonor = {
        title: "",
        image: ""
      };
      this.showHonorModal = true;
    },
    // 选择荣誉图片
    selectHonorImage() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          this.currentHonor.image = res.tempFilePaths[0];
        }
      });
    },
    // 确认添加荣誉
    confirmHonor() {
      if (!this.currentHonor.title.trim()) {
        common_vendor.index.showToast({
          title: "请输入荣誉标题",
          icon: "none"
        });
        return;
      }
      if (!this.currentHonor.image) {
        common_vendor.index.showToast({
          title: "请选择荣誉图片",
          icon: "none"
        });
        return;
      }
      this.companyData.honors.push({
        title: this.currentHonor.title,
        image: this.currentHonor.image
      });
      this.closeHonorModal();
    },
    // 关闭荣誉弹窗
    closeHonorModal() {
      this.showHonorModal = false;
    },
    // 删除荣誉
    deleteHonor(index) {
      common_vendor.index.showModal({
        title: "确认删除",
        content: "确定要删除这个荣誉吗？",
        success: (res) => {
          if (res.confirm) {
            this.companyData.honors.splice(index, 1);
          }
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.s($options.navbarStyle),
    c: $data.showPremiumNotice
  }, $data.showPremiumNotice ? {} : {}, {
    d: $data.companyData.showSection,
    e: common_vendor.o((...args) => $options.onSwitchChange && $options.onSwitchChange(...args)),
    f: $data.expandStatus.info ? 1 : "",
    g: common_vendor.o(($event) => $options.toggleExpand("info")),
    h: $data.expandStatus.info
  }, $data.expandStatus.info ? {
    i: $data.companyData.showName,
    j: common_vendor.o((e) => $data.companyData.showName = e.detail.value),
    k: $data.companyData.name,
    l: common_vendor.o(($event) => $data.companyData.name = $event.detail.value),
    m: $data.companyData.showDesc,
    n: common_vendor.o((e) => $data.companyData.showDesc = e.detail.value),
    o: $data.companyData.desc,
    p: common_vendor.o(($event) => $data.companyData.desc = $event.detail.value),
    q: $data.companyData.showSlogan,
    r: common_vendor.o((e) => $data.companyData.showSlogan = e.detail.value),
    s: $data.companyData.slogan,
    t: common_vendor.o(($event) => $data.companyData.slogan = $event.detail.value),
    v: $data.companyData.showAddress,
    w: common_vendor.o((e) => $data.companyData.showAddress = e.detail.value),
    x: $data.companyData.address,
    y: common_vendor.o(($event) => $data.companyData.address = $event.detail.value)
  } : {}, {
    z: !$data.companyData.showSection ? 1 : "",
    A: $data.expandStatus.images ? 1 : "",
    B: common_vendor.o(($event) => $options.toggleExpand("images")),
    C: $data.expandStatus.images
  }, $data.expandStatus.images ? common_vendor.e({
    D: common_vendor.f($data.companyData.images, (image, index, i0) => {
      return {
        a: image,
        b: common_vendor.o(($event) => $options.deleteImage(index), index),
        c: index
      };
    }),
    E: $data.companyData.images.length < 9
  }, $data.companyData.images.length < 9 ? {
    F: common_vendor.o((...args) => $options.uploadImage && $options.uploadImage(...args))
  } : {}) : {}, {
    G: !$data.companyData.showSection ? 1 : "",
    H: $data.expandStatus.honors ? 1 : "",
    I: common_vendor.o(($event) => $options.toggleExpand("honors")),
    J: $data.expandStatus.honors
  }, $data.expandStatus.honors ? common_vendor.e({
    K: common_vendor.f($data.companyData.honors, (honor, index, i0) => {
      return {
        a: honor.image,
        b: common_vendor.t(honor.title),
        c: common_vendor.o(($event) => $options.deleteHonor(index), index),
        d: index
      };
    }),
    L: $data.companyData.honors.length < 6
  }, $data.companyData.honors.length < 6 ? {
    M: common_vendor.o((...args) => $options.addHonor && $options.addHonor(...args))
  } : {}) : {}, {
    N: !$data.companyData.showSection ? 1 : "",
    O: common_vendor.s($options.mainStyle),
    P: common_vendor.o((...args) => $options.saveData && $options.saveData(...args)),
    Q: $data.showHonorModal
  }, $data.showHonorModal ? common_vendor.e({
    R: common_vendor.o((...args) => $options.closeHonorModal && $options.closeHonorModal(...args)),
    S: $data.currentHonor.title,
    T: common_vendor.o(($event) => $data.currentHonor.title = $event.detail.value),
    U: $data.currentHonor.image
  }, $data.currentHonor.image ? {
    V: $data.currentHonor.image
  } : {}, {
    W: common_vendor.o((...args) => $options.selectHonorImage && $options.selectHonorImage(...args)),
    X: common_vendor.o((...args) => $options.closeHonorModal && $options.closeHonorModal(...args)),
    Y: common_vendor.o((...args) => $options.confirmHonor && $options.confirmHonor(...args)),
    Z: common_vendor.o(() => {
    }),
    aa: common_vendor.o((...args) => $options.closeHonorModal && $options.closeHonorModal(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-becff3b5"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/card/company.js.map
