"use strict";
const common_vendor = require("../../common/vendor.js");
const services_userService = require("../../services/userService.js");
const services_userInfoService = require("../../services/userInfoService.js");
const utils_storage = require("../../utils/storage.js");
const utils_config = require("../../utils/config.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      statusBarHeight: 44,
      activeTab: 0,
      showSaveBtn: true,
      lastScrollTop: 0,
      showAddressModal: false,
      addressTab: 0,
      regionValue: ["", "", ""],
      regionText: "",
      addressDetail: "",
      topSectionHeight: 0,
      // 将默认值设为0，避免初始大间距
      contentPaddingTop: 0,
      // 添加新属性用于设置内容区域padding-top
      screenWidth: 375,
      screenHeight: 667,
      deviceRatio: 1,
      isPremiumMember: false,
      // 添加会员状态标志
      phoneError: "",
      // 电话号码错误提示
      emailError: "",
      // 邮箱错误提示
      form: {
        avatar: "/static/default-avatar.png",
        name: "徐敏",
        position: "销售专员",
        company: "德萨大数据股份有限公司",
        phone: "18596320120",
        email: "www.1099458780.com",
        wechat: "xumin_wx",
        address: "山东省庄市滕州市北辛西路2000号",
        customStyle: {
          backgroundType: "color",
          gradientIndex: 0,
          backgroundColor: "#fff",
          backgroundImage: "",
          textColor: "#222",
          borderRadius: "small",
          borderRadiusIndex: 1,
          cardStyleIndex: 0
        }
      },
      cardStyles: [
        {
          name: "经典商务",
          previewStyle: "background: #ffffff; border: 2px solid #4a6ef2; box-shadow: 0 4px 10px rgba(0,0,0,0.15);"
        },
        {
          name: "科技蓝",
          previewStyle: "background: linear-gradient(90deg, #4a6ef2 0%, #6fc3ff 100%);"
        },
        {
          name: "简约灰",
          previewStyle: "background: #f5f7fa; border: 1px solid #eee;"
        },
        {
          name: "暗夜黑",
          previewStyle: "background: #333333; color: #ffffff;"
        },
        {
          name: "活力橙",
          previewStyle: "background: linear-gradient(90deg, #f2a54a 0%, #ffd36f 100%);"
        },
        {
          name: "森林绿",
          previewStyle: "background: linear-gradient(90deg, #2c9d6d 0%, #6fd39f 100%);"
        },
        {
          name: "浪漫粉",
          previewStyle: "background: linear-gradient(90deg, #f25a8e 0%, #ff9cc0 100%);"
        },
        {
          name: "高端黑金",
          previewStyle: "background: linear-gradient(90deg, #333333 0%, #666666 100%); border: 1px solid #d4af37;"
        },
        {
          name: "紫色梦幻",
          previewStyle: "background: linear-gradient(90deg, #9d2c8d 0%, #d36fc6 100%);"
        },
        {
          name: "复古棕",
          previewStyle: "background: #8b5a2b; color: #f5deb3;"
        },
        {
          name: "海洋蓝",
          previewStyle: "background: linear-gradient(135deg, #1a2980 0%, #26d0ce 100%);"
        },
        {
          name: "珊瑚红",
          previewStyle: "background: #FF6F61; color: #ffffff;"
        },
        {
          name: "薄荷绿",
          previewStyle: "background: #98ff98; color: #006400; border: 1px solid #7ccc7c;"
        },
        {
          name: "太空灰",
          previewStyle: "background: linear-gradient(135deg, #232526 0%, #414345 100%); color: #ffffff;"
        },
        {
          name: "日落橙",
          previewStyle: "background: linear-gradient(135deg, #ff512f 0%, #f09819 100%); color: #ffffff;"
        },
        {
          name: "翡翠绿",
          previewStyle: "background: linear-gradient(135deg, #43c6ac 0%, #191654 100%); color: #ffffff;"
        },
        {
          name: "商务蓝",
          previewStyle: "background: #1e3c72; color: #ffffff; border: 2px solid #2a5298;"
        },
        {
          name: "玫瑰金",
          previewStyle: "background: linear-gradient(135deg, #e6b980 0%, #eacda3 100%); color: #8b4513;"
        },
        {
          name: "星空紫",
          previewStyle: "background: linear-gradient(135deg, #5f2c82 0%, #49a09d 100%); color: #ffffff;"
        }
      ],
      bgColors: [
        "#ffffff",
        // 纯白
        "#f5f7fa",
        // 浅灰
        "#e6f0ff",
        // 浅蓝
        "#f0f9eb",
        // 浅绿
        "#fef0f0",
        // 浅红
        "#fdf6ec",
        // 浅黄
        "#f0f2f5",
        // 银灰
        "#2c5aa0",
        // 深蓝
        "#333333",
        // 深灰
        "#67c23a",
        // 绿色
        "#1e3c72",
        // 商务蓝
        "#8b5a2b",
        // 复古棕
        "#FF6F61",
        // 珊瑚红
        "#98ff98",
        // 薄荷绿
        "#5f2c82",
        // 深紫
        "#b8860b",
        // 暗金色
        "#20b2aa",
        // 浅绿松石
        "#4b0082",
        // 靛青
        "#800000",
        // 栗色
        "#008080"
        // 蓝绿色
      ],
      gradients: [
        "linear-gradient(90deg, #4a6ef2 0%, #6fc3ff 100%)",
        // 蓝色渐变
        "linear-gradient(90deg, #f2a54a 0%, #ffd36f 100%)",
        // 橙色渐变
        "linear-gradient(90deg, #2c9d6d 0%, #6fd39f 100%)",
        // 绿色渐变
        "linear-gradient(90deg, #f25a8e 0%, #ff9cc0 100%)",
        // 粉色渐变
        "linear-gradient(90deg, #333333 0%, #666666 100%)",
        // 灰色渐变
        "linear-gradient(90deg, #9d2c8d 0%, #d36fc6 100%)",
        // 紫色渐变
        "linear-gradient(135deg, #1a2980 0%, #26d0ce 100%)",
        // 海洋蓝渐变
        "linear-gradient(135deg, #ff512f 0%, #f09819 100%)",
        // 日落橙渐变
        "linear-gradient(135deg, #43c6ac 0%, #191654 100%)",
        // 翡翠绿渐变
        "linear-gradient(135deg, #e6b980 0%, #eacda3 100%)",
        // 玫瑰金渐变
        "linear-gradient(135deg, #5f2c82 0%, #49a09d 100%)",
        // 星空紫渐变
        "linear-gradient(135deg, #232526 0%, #414345 100%)",
        // 太空灰渐变
        "linear-gradient(135deg, #2980b9 0%, #6dd5fa 100%)",
        // 清新天蓝
        "linear-gradient(135deg, #8e2de2 0%, #4a00e0 100%)",
        // 皇家紫
        "linear-gradient(135deg, #f953c6 0%, #b91d73 100%)",
        // 热情玫红
        "linear-gradient(135deg, #00b09b 0%, #96c93d 100%)",
        // 春日绿
        "linear-gradient(135deg, #c31432 0%, #240b36 100%)",
        // 深红暗紫
        "linear-gradient(135deg, #3a1c71 0%, #d76d77 50%, #ffaf7b 100%)",
        // 三色渐变
        "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
        // 电光蓝
        "linear-gradient(135deg, #7F7FD5 0%, #86A8E7 50%, #91EAE4 100%)"
        // 薰衣草蓝
      ],
      bgImages: ["/static/template/1.jpg", "/static/template/2.png"],
      textColors: [
        "#222222",
        // 黑色
        "#ffffff",
        // 白色
        "#4a6ef2",
        // 蓝色
        "#f2a54a",
        // 橙色
        "#2c9d6d",
        // 绿色
        "#f25a8e",
        // 粉色
        "#9d2c8d",
        // 紫色
        "#d4af37",
        // 金色
        "#1e3c72",
        // 商务蓝
        "#8b5a2b",
        // 复古棕
        "#FF6F61",
        // 珊瑚红
        "#006400",
        // 深绿色
        "#800000",
        // 栗色
        "#4b0082",
        // 靛青
        "#f5deb3",
        // 小麦色
        "#00b09b",
        // 薄荷绿
        "#c31432",
        // 深红色
        "#3a1c71",
        // 深紫色
        "#4facfe",
        // 电光蓝
        "#7F7FD5",
        // 薰衣草蓝
        "#b91d73",
        // 玫红
        "#96c93d",
        // 嫩绿
        "#240b36",
        // 暗紫
        "#ffaf7b",
        // 杏橙
        "#00f2fe",
        // 亮青
        "#2E8B57",
        // 海绿色
        "#CD5C5C",
        // 印第安红
        "#191970",
        // 午夜蓝
        "#DAA520",
        // 金菊色
        "#20B2AA"
        // 浅海绿
      ]
    };
  },
  computed: {
    navbarStyle() {
      return {
        height: `${this.statusBarHeight + 45}px`,
        paddingTop: `${this.statusBarHeight}px`,
        zIndex: 1001
        // 确保导航栏在最上层
      };
    },
    // 计算实际背景样式
    computedBackgroundStyle() {
      const style = {};
      if (this.form.customStyle.backgroundType === "solid" || this.form.customStyle.backgroundType === "color") {
        style.background = this.form.customStyle.backgroundColor || "#ffffff";
      } else if (this.form.customStyle.backgroundType === "gradient") {
        try {
          const gradientIndex = parseInt(this.form.customStyle.gradientIndex || 0);
          if (gradientIndex >= 0 && gradientIndex < this.gradients.length) {
            const gradient = this.gradients[gradientIndex];
            if (gradient) {
              style.background = gradient;
            } else {
              style.background = "#ffffff";
            }
          } else {
            style.background = "#ffffff";
          }
        } catch (error) {
          style.background = "#ffffff";
        }
      } else if (this.form.customStyle.backgroundType === "image" && this.form.customStyle.backgroundImage) {
        style.background = "transparent";
      } else {
        style.background = "#ffffff";
      }
      return style;
    },
    iconStyle() {
      if (this.form.customStyle.textColor === "#ffffff") {
        return {
          filter: "brightness(0) invert(1)",
          opacity: 0.9
        };
      } else {
        return {
          opacity: 0.8
        };
      }
    },
    cardPreviewStyle() {
      const style = { ...this.computedBackgroundStyle };
      style.borderRadius = this.form.customStyle.borderRadiusIndex === void 0 ? "12rpx" : ["12rpx", "24rpx", "0"][this.form.customStyle.borderRadiusIndex];
      if (this.form.customStyle.shadow) {
        style.boxShadow = "0 4rpx 16rpx rgba(0,0,0,0.1)";
      }
      return style;
    },
    cardTextStyle() {
      return {
        color: this.form.customStyle.textColor || "#222222",
        fontSize: this.fontSizes[this.form.customStyle.fontSizeIndex || 1].previewSize
      };
    },
    avatarPreviewStyle() {
      const style = {
        borderRadius: this.avatarStyles[this.form.customStyle.avatarStyleIndex || 0]
      };
      if (this.form.customStyle.avatarBorder) {
        style.border = "2rpx solid #ffffff";
      }
      return style;
    },
    adaptiveSpacing() {
      const baseSpacing = this.screenWidth / 30;
      return {
        xs: baseSpacing * 0.4,
        // 超小间距（减小系数）
        sm: baseSpacing * 0.7,
        // 小间距（减小系数）
        md: baseSpacing * 0.9,
        // 中等间距（减小系数）
        lg: baseSpacing * 1.2,
        // 大间距（减小系数）
        xl: baseSpacing * 1.5
        // 超大间距（减小系数）
      };
    },
    cardPreviewMargin() {
      const spacing = this.adaptiveSpacing;
      return {
        margin: `${spacing.md}px ${spacing.sm}px ${spacing.md}px ${spacing.sm}px`
      };
    },
    contentStyle() {
      if (this.contentPaddingTop > 0) {
        return {
          paddingTop: `${this.contentPaddingTop}px`
        };
      }
      const sectionHeight = this.topSectionHeight || 400;
      const reductionRatio = this.screenHeight < 700 ? 0.25 : 0.2;
      const paddingReduction = sectionHeight * reductionRatio;
      return {
        paddingTop: `${this.statusBarHeight + 45 + sectionHeight - paddingReduction}px`
      };
    },
    // 计算职位样式
    positionStyle() {
      const textColor = this.form.customStyle.textColor;
      if (textColor === "#ffffff") {
        return {
          color: "#ffffff",
          background: "rgba(255, 255, 255, 0.2)"
        };
      } else if (this.form.customStyle.backgroundType === "gradient" || this.form.customStyle.backgroundColor === "#333333" || this.form.customStyle.backgroundColor === "#2c5aa0") {
        return {
          color: "#ffffff",
          background: "rgba(255, 255, 255, 0.2)"
        };
      } else {
        return {
          color: textColor,
          background: "rgba(0, 0, 0, 0.05)"
        };
      }
    }
  },
  methods: {
    // 同步会员状态
    async syncMemberStatus() {
      try {
        await services_userService.userService.syncMemberStatus();
        this.checkMemberStatus();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/card/edit.vue:742", "会员状态同步失败:", error);
      }
    },
    // 电话号码输入验证
    onPhoneInput(e) {
      let value = e.detail.value;
      value = value.replace(/[^\d]/g, "");
      if (value.length > 11) {
        value = value.slice(0, 11);
      }
      this.form.phone = value;
      this.validatePhone(value);
    },
    // 验证手机号
    validatePhone(phone) {
      if (!phone) {
        this.phoneError = "";
        return;
      }
      if (phone.length < 11) {
        this.phoneError = "手机号必须为11位";
      } else if (!/^1[3-9]\d{9}$/.test(phone)) {
        this.phoneError = "请输入正确的手机号格式";
      } else {
        this.phoneError = "";
      }
    },
    // 验证邮箱格式
    validateEmail() {
      if (!this.form.email) {
        this.emailError = "";
        return;
      }
      if (!services_userInfoService.userInfoService.validateEmail(this.form.email)) {
        this.emailError = "请输入正确的邮箱格式";
      } else {
        this.emailError = "";
      }
    },
    // 清除输入框内容的方法
    clearPhone() {
      this.form.phone = "";
      this.phoneError = "";
    },
    clearWechat() {
      this.form.wechat = "";
    },
    clearEmail() {
      this.form.email = "";
      this.emailError = "";
    },
    // 从服务器加载用户信息
    async loadUserInfoFromServer() {
      var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n;
      try {
        const isLoggedIn = services_userService.userService.isLoggedIn();
        const token = utils_storage.getData(utils_config.TOKEN_KEY);
        const currentUser = utils_storage.getData(utils_config.USER_INFO_KEY);
        if (!isLoggedIn) {
          return;
        }
        common_vendor.index.showLoading({
          title: "加载中...",
          mask: true
        });
        const result = await services_userInfoService.userInfoService.getCurrentUserInfo();
        if (result.success && result.data) {
          const serverData = result.data;
          const updatedForm = {
            ...this.form,
            id: serverData.id,
            name: serverData.name || "",
            phone: serverData.phone || "",
            email: serverData.email || "",
            wechat: serverData.wechat || "",
            position: serverData.position || "",
            company: serverData.company || "",
            address: serverData.address || "",
            description: serverData.description || "",
            avatar: serverData.avatar || "/static/default-avatar.png",
            // 重要：同步自定义样式，包括背景图片
            customStyle: {
              ...this.form.customStyle,
              ...serverData.customStyle || {},
              // 确保关键字段被正确同步
              backgroundType: ((_a = serverData.customStyle) == null ? void 0 : _a.backgroundType) || ((_b = this.form.customStyle) == null ? void 0 : _b.backgroundType) || "color",
              backgroundImage: ((_c = serverData.customStyle) == null ? void 0 : _c.backgroundImage) || ((_d = this.form.customStyle) == null ? void 0 : _d.backgroundImage) || "",
              backgroundColor: ((_e = serverData.customStyle) == null ? void 0 : _e.backgroundColor) || ((_f = this.form.customStyle) == null ? void 0 : _f.backgroundColor) || "#ffffff",
              gradientIndex: ((_g = serverData.customStyle) == null ? void 0 : _g.gradientIndex) !== void 0 ? serverData.customStyle.gradientIndex : ((_h = this.form.customStyle) == null ? void 0 : _h.gradientIndex) || 0,
              textColor: ((_i = serverData.customStyle) == null ? void 0 : _i.textColor) || ((_j = this.form.customStyle) == null ? void 0 : _j.textColor) || "#333333",
              borderRadiusIndex: ((_k = serverData.customStyle) == null ? void 0 : _k.borderRadiusIndex) !== void 0 ? serverData.customStyle.borderRadiusIndex : ((_l = this.form.customStyle) == null ? void 0 : _l.borderRadiusIndex) || 1,
              cardStyleIndex: ((_m = serverData.customStyle) == null ? void 0 : _m.cardStyleIndex) !== void 0 ? serverData.customStyle.cardStyleIndex : ((_n = this.form.customStyle) == null ? void 0 : _n.cardStyleIndex) || 0
            }
          };
          this.form = updatedForm;
          common_vendor.index.setStorageSync("userInfo", this.form);
        } else {
          common_vendor.index.showToast({
            title: "网络异常，请检查网络连接",
            icon: "none",
            duration: 2e3
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/card/edit.vue:881", "加载服务器用户信息失败:", error);
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 静默上传头像（相册和拍照使用，不显示loading避免重复）
    async uploadAvatarSilently(filePath) {
      try {
        if (services_userService.userService.isLoggedIn()) {
          const result = await services_userInfoService.userInfoService.uploadAvatar(filePath);
          if (result.success && result.url) {
            this.form.avatar = result.url;
            this.$forceUpdate();
          } else {
            this.form.avatar = "/static/default-avatar.png";
            common_vendor.index.__f__("error", "at pages/card/edit.vue:905", "头像上传失败:", result.message);
            common_vendor.index.showToast({
              title: "头像更新失败，请重试",
              icon: "none"
            });
          }
        } else {
          this.form.avatar = "/static/default-avatar.png";
          common_vendor.index.showToast({
            title: "请先登录后更换头像",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/card/edit.vue:920", "头像上传失败:", error);
        this.form.avatar = "/static/default-avatar.png";
        let errorMessage = "头像上传失败";
        if (error.message.includes("404")) {
          errorMessage = "上传接口不存在，请联系管理员";
        } else if (error.message.includes("directory")) {
          errorMessage = "服务器存储配置问题，请联系管理员";
        } else if (error.message.includes("权限")) {
          errorMessage = "服务器权限问题，请联系管理员";
        }
        common_vendor.index.showToast({
          title: errorMessage,
          icon: "none",
          duration: 3e3
        });
      }
    },
    // 显示头像选择ActionSheet（仅非微信环境使用）
    showAvatarActionSheet() {
      common_vendor.index.showActionSheet({
        itemList: ["从相册选择", "拍照"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.chooseAvatarFromAlbum();
              break;
            case 1:
              this.chooseAvatarFromCamera();
              break;
          }
        }
      });
    },
    // 从相册选择头像
    chooseAvatarFromAlbum() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album"],
        success: (res) => {
          this.uploadAvatarSilently(res.tempFilePaths[0]);
        }
      });
    },
    // 拍照获取头像
    chooseAvatarFromCamera() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["camera"],
        success: (res) => {
          this.uploadAvatarSilently(res.tempFilePaths[0]);
        }
      });
    },
    // 上传头像到服务器（微信头像选择使用，会显示loading）
    async uploadAvatarToServer(filePath) {
      try {
        common_vendor.index.showLoading({
          title: "上传头像中...",
          mask: true
        });
        if (services_userService.userService.isLoggedIn()) {
          const result = await services_userInfoService.userInfoService.uploadAvatar(filePath);
          if (result.success && result.url) {
            this.form.avatar = result.url;
            this.$forceUpdate();
          } else {
            this.form.avatar = "/static/default-avatar.png";
            common_vendor.index.__f__("error", "at pages/card/edit.vue:1016", "头像上传失败:", result.message);
            common_vendor.index.showToast({
              title: "头像更新失败，请重试",
              icon: "none"
            });
          }
        } else {
          this.form.avatar = "/static/default-avatar.png";
          common_vendor.index.showToast({
            title: "请先登录后更换头像",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/card/edit.vue:1031", "头像上传失败:", error);
        this.form.avatar = "/static/default-avatar.png";
        let errorMessage = "头像上传失败";
        if (error.message.includes("404")) {
          errorMessage = "上传接口不存在，请联系管理员";
        } else if (error.message.includes("directory")) {
          errorMessage = "服务器存储配置问题，请联系管理员";
        } else if (error.message.includes("权限")) {
          errorMessage = "服务器权限问题，请联系管理员";
        }
        common_vendor.index.showToast({
          title: errorMessage,
          icon: "none",
          duration: 3e3
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 微信头像选择回调（新版API）
    onChooseAvatar(e) {
      if (e.detail && e.detail.avatarUrl) {
        const avatarUrl = e.detail.avatarUrl;
        common_vendor.index.__f__("log", "at pages/card/edit.vue:1060", "微信头像URL:", avatarUrl);
        this.uploadAvatarToServer(avatarUrl);
      } else {
        common_vendor.index.showToast({
          title: "头像获取失败，请重试",
          icon: "none"
        });
      }
    },
    // 添加会员状态检查方法
    checkMemberStatus() {
      try {
        const isMember = common_vendor.index.getStorageSync("isMember");
        const isPro = common_vendor.index.getStorageSync("isPro");
        const memberExpireDate = common_vendor.index.getStorageSync("memberExpireDate");
        if (isMember || isPro) {
          this.isPremiumMember = true;
          return true;
        }
        return false;
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/card/edit.vue:1095", "加载会员状态失败", e);
        return false;
      }
    },
    // 跳转到会员开通页面
    goPremium() {
      common_vendor.index.navigateTo({ url: "/pages/company/premium" });
    },
    // 修改保存方法，根据选项卡实现分级权限检查
    saveCard() {
      var _a, _b, _c, _d, _e, _f, _g;
      if (!services_userService.userService.isLoggedIn()) {
        common_vendor.index.showModal({
          title: "需要登录",
          content: "保存功能需要登录后才能使用，是否前往登录？",
          confirmText: "去登录",
          cancelText: "取消",
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.navigateTo({
                url: "/pages/auth/auth"
              });
            }
          }
        });
        return;
      }
      const isMember = common_vendor.index.getStorageSync("isMember");
      const memberLevel = common_vendor.index.getStorageSync("memberLevel") || 0;
      const memberExpireDate = common_vendor.index.getStorageSync("memberExpireDate");
      let isExpired = false;
      if (memberExpireDate) {
        const now = /* @__PURE__ */ new Date();
        const formattedDate = memberExpireDate.replace(/\s/g, "T");
        const expireDate = new Date(formattedDate);
        isExpired = now > expireDate;
      }
      if (this.activeTab === 0) {
        const hasPermission = isMember && memberLevel >= 1 && !isExpired;
        if (!hasPermission) {
          common_vendor.index.showModal({
            title: "专业版功能",
            content: "联系信息保存功能需要专业版或企业版权限",
            confirmText: "立即升级",
            cancelText: "取消",
            success: (res) => {
              if (res.confirm) {
                this.goPremium();
              }
            }
          });
          return;
        }
      } else if (this.activeTab === 1) {
        const isEnterprise = isMember && memberLevel >= 2 && !isExpired;
        if (!isEnterprise) {
          common_vendor.index.showModal({
            title: "企业版功能",
            content: "个性定制保存功能仅限企业版用户使用，专业版用户需要升级到企业版",
            confirmText: "立即升级",
            cancelText: "取消",
            success: (res) => {
              if (res.confirm) {
                this.goPremium();
              }
            }
          });
          return;
        }
      }
      const currentUserInfo = common_vendor.index.getStorageSync("userInfo") || {};
      let userInfoToSave;
      let successMessage;
      if (this.activeTab === 0) {
        userInfoToSave = {
          ...currentUserInfo,
          avatar: this.form.avatar || "/static/default-avatar.png",
          name: this.form.name || "徐敏",
          position: this.form.position || "销售专员",
          company: this.form.company || "德萨大数据股份有限公司",
          phone: this.form.phone || "18596320120",
          email: this.form.email || "www.1099458780.com",
          wechat: this.form.wechat || "xumin_wx",
          address: this.form.address || "山东省庄市滕州市北辛西路2000号",
          description: this.form.description || "拥有5年销售经验，专注于客户关系维护和业务拓展。热爱学习新技术，善于沟通协调，致力于为客户提供最优质的服务体验。"
        };
        successMessage = "联系信息保存成功";
      } else {
        userInfoToSave = {
          ...currentUserInfo,
          customStyle: {
            ...currentUserInfo.customStyle || {},
            backgroundType: ((_a = this.form.customStyle) == null ? void 0 : _a.backgroundType) || "color",
            gradientIndex: ((_b = this.form.customStyle) == null ? void 0 : _b.gradientIndex) || 0,
            backgroundColor: ((_c = this.form.customStyle) == null ? void 0 : _c.backgroundColor) || "#ffffff",
            backgroundImage: ((_d = this.form.customStyle) == null ? void 0 : _d.backgroundImage) || "",
            textColor: ((_e = this.form.customStyle) == null ? void 0 : _e.textColor) || "#333333",
            borderRadiusIndex: ((_f = this.form.customStyle) == null ? void 0 : _f.borderRadiusIndex) || 1,
            cardStyleIndex: ((_g = this.form.customStyle) == null ? void 0 : _g.cardStyleIndex) || 0
          }
        };
        successMessage = "个性定制保存成功";
      }
      this.saveToServerAndLocal(userInfoToSave, successMessage);
    },
    // 保存到服务器和本地存储
    async saveToServerAndLocal(userInfoToSave, successMessage) {
      try {
        common_vendor.index.showLoading({
          title: "保存中...",
          mask: true
        });
        common_vendor.index.setStorageSync("userInfo", userInfoToSave);
        const isLoggedIn = await services_userService.userService.isLoggedIn();
        if (isLoggedIn) {
          const result = await services_userInfoService.userInfoService.updateUserInfo(userInfoToSave);
          if (result.success) {
            common_vendor.index.showToast({
              title: successMessage,
              icon: "success",
              duration: 1500
            });
            setTimeout(() => {
              this.loadUserInfoFromServer();
            }, 500);
          } else {
            common_vendor.index.showToast({
              title: successMessage + "（网络异常，已保存到本地）",
              icon: "none",
              duration: 2500
            });
            common_vendor.index.__f__("error", "at pages/card/edit.vue:1257", "服务器保存失败:", result.message);
          }
        } else {
          common_vendor.index.showToast({
            title: successMessage,
            icon: "success",
            duration: 1500
          });
        }
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1e3);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/card/edit.vue:1274", "保存失败:", error);
        common_vendor.index.showToast({
          title: "保存失败，请检查网络后重试",
          icon: "none",
          duration: 2e3
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 修改应用样式方法，增加会员检查
    applyCardStyle(idx) {
      this.form.customStyle.cardStyleIndex = idx;
      switch (idx) {
        case 0:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "solid",
            backgroundColor: "#ffffff",
            backgroundImage: "",
            gradientIndex: -1,
            textColor: "#2c5aa0",
            cardStyleIndex: idx
          };
          break;
        case 1:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 0,
            // 蓝色渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 2:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "solid",
            backgroundColor: "#f5f7fa",
            backgroundImage: "",
            gradientIndex: -1,
            textColor: "#222222",
            cardStyleIndex: idx
          };
          break;
        case 3:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "solid",
            backgroundColor: "#333333",
            backgroundImage: "",
            gradientIndex: -1,
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 4:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 1,
            // 橙色渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 5:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 2,
            // 绿色渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 6:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 3,
            // 粉色渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 7:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 4,
            // 灰色渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#d4af37",
            // 金色文字
            cardStyleIndex: idx
          };
          break;
        case 8:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 5,
            // 紫色渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 9:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "solid",
            backgroundColor: "#8b5a2b",
            backgroundImage: "",
            gradientIndex: -1,
            textColor: "#f5deb3",
            cardStyleIndex: idx
          };
          break;
        case 10:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 6,
            // 海洋蓝渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 11:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "solid",
            backgroundColor: "#FF6F61",
            backgroundImage: "",
            gradientIndex: -1,
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 12:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "solid",
            backgroundColor: "#98ff98",
            backgroundImage: "",
            gradientIndex: -1,
            textColor: "#006400",
            cardStyleIndex: idx
          };
          break;
        case 13:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 11,
            // 太空灰渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 14:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 7,
            // 日落橙渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 15:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 8,
            // 翡翠绿渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 16:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "solid",
            backgroundColor: "#1e3c72",
            backgroundImage: "",
            gradientIndex: -1,
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 17:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 9,
            // 玫瑰金渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#8b4513",
            cardStyleIndex: idx
          };
          break;
        case 18:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 10,
            // 星空紫渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
      }
      this.$forceUpdate();
    },
    // 修改 applyBgColor 方法
    applyBgColor(color) {
      this.form.customStyle.backgroundType = "color";
      this.form.customStyle.backgroundColor = color;
      this.form.customStyle.backgroundImage = "";
      this.form.customStyle.gradientIndex = -1;
      if (color === "#ffffff" || color === "#f5f7fa" || color === "#e6f0ff" || color === "#f0f9eb" || color === "#fef0f0" || color === "#fdf6ec") {
        this.form.customStyle.textColor = "#222222";
      } else {
        this.form.customStyle.textColor = "#ffffff";
      }
      this.$forceUpdate();
    },
    // 修改 applyGradient 方法
    applyGradient(idx) {
      if (idx >= 0 && idx < this.gradients.length) {
        this.form.customStyle.backgroundType = "gradient";
        this.form.customStyle.gradientIndex = idx;
        this.form.customStyle.backgroundImage = "";
        this.form.customStyle.backgroundColor = "";
        this.form.customStyle.textColor = "#ffffff";
        this.$forceUpdate();
        setTimeout(() => {
          this.$forceUpdate();
        }, 100);
      } else {
        common_vendor.index.__f__("error", "at pages/card/edit.vue:1547", "无效的渐变索引:", idx);
      }
    },
    // 修改 applyBgImage 方法
    applyBgImage(img) {
      this.form.customStyle.backgroundType = "image";
      this.form.customStyle.backgroundImage = img;
      this.form.customStyle.backgroundColor = "";
      this.form.customStyle.gradientIndex = -1;
      this.form.customStyle.textColor = "#ffffff";
      this.$forceUpdate();
      setTimeout(() => {
        this.$forceUpdate();
      }, 100);
    },
    // 修改 applyTextColor 方法
    applyTextColor(color) {
      this.form.customStyle.textColor = color;
      this.$forceUpdate();
    },
    // 上传背景图片
    async uploadBgImage() {
      common_vendor.index.chooseImage({
        count: 1,
        success: async (res) => {
          const tempFilePath = res.tempFilePaths[0];
          try {
            common_vendor.index.showLoading({
              title: "上传中...",
              mask: true
            });
            this.form.customStyle.backgroundType = "image";
            this.form.customStyle.backgroundImage = tempFilePath;
            this.form.customStyle.backgroundColor = "";
            this.form.customStyle.gradientIndex = -1;
            this.form.customStyle.textColor = "#ffffff";
            this.$forceUpdate();
            if (services_userService.userService.isLoggedIn()) {
              const result = await this.uploadImageToServer(tempFilePath);
              if (result.success && result.url) {
                this.form.customStyle.backgroundImage = result.url;
                this.$forceUpdate();
                common_vendor.index.showToast({
                  title: "背景图片设置成功",
                  icon: "success"
                });
              } else {
                common_vendor.index.showToast({
                  title: "背景图片设置成功",
                  icon: "success"
                });
              }
            } else {
              common_vendor.index.showToast({
                title: "背景图片设置成功",
                icon: "success"
              });
            }
          } catch (error) {
            common_vendor.index.__f__("error", "at pages/card/edit.vue:1623", "背景图片上传失败:", error);
            common_vendor.index.showToast({
              title: "背景图片设置失败",
              icon: "none"
            });
          } finally {
            common_vendor.index.hideLoading();
          }
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/card/edit.vue:1633", "选择图片失败:", err);
          common_vendor.index.showToast({ title: "图片选择失败，请重试", icon: "none" });
        }
      });
    },
    // 获取可显示的头像URL
    getDisplayAvatar(avatarUrl) {
      if (!avatarUrl) {
        return "/static/default-avatar.png";
      }
      if (avatarUrl.startsWith("https://")) {
        return avatarUrl;
      } else if (avatarUrl.startsWith("http://tmp/")) {
        return avatarUrl;
      } else if (avatarUrl.startsWith("http://")) {
        return "/static/default-avatar.png";
      } else if (avatarUrl.startsWith("/")) {
        return avatarUrl;
      }
      return avatarUrl;
    },
    // 上传图片到服务器
    async uploadImageToServer(filePath) {
      try {
        if (!services_userService.userService.isLoggedIn()) {
          throw new Error("请先登录");
        }
        const token = services_userService.userService.getToken();
        return new Promise((resolve, reject) => {
          const uploadUrl = `${utils_config.API_BASE_URL}/upload/image`;
          common_vendor.index.uploadFile({
            url: uploadUrl,
            filePath,
            name: "image",
            header: {
              "Authorization": `Bearer ${token}`
            },
            success: (res) => {
              try {
                common_vendor.index.__f__("log", "at pages/card/edit.vue:1688", "图片上传响应:", res);
                if (res.statusCode !== 200) {
                  reject(new Error(`服务器响应错误: ${res.statusCode}`));
                  return;
                }
                if (!res.data) {
                  reject(new Error("服务器返回空数据"));
                  return;
                }
                const data = JSON.parse(res.data);
                common_vendor.index.__f__("log", "at pages/card/edit.vue:1701", "解析后的数据:", data);
                if (data.code === 200) {
                  resolve({
                    success: true,
                    data: data.data,
                    url: data.data.url
                  });
                } else {
                  reject(new Error(data.message || "上传失败"));
                }
              } catch (error) {
                common_vendor.index.__f__("error", "at pages/card/edit.vue:1713", "解析响应数据失败:", error);
                reject(new Error(`响应数据解析失败: ${error.message}`));
              }
            },
            fail: (error) => {
              common_vendor.index.__f__("error", "at pages/card/edit.vue:1718", "上传请求失败:", error);
              reject(new Error("上传请求失败"));
            }
          });
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/card/edit.vue:1724", "上传图片失败:", error);
        return {
          success: false,
          message: error.message || "上传失败"
        };
      }
    },
    setBorderRadius(idx) {
      this.form.customStyle.borderRadiusIndex = idx;
      this.form.customStyle.borderRadius = this.borderRadiusMap[idx];
    },
    setAvatarStyle(idx) {
      this.form.customStyle.avatarStyleIndex = idx;
      this.form.customStyle.avatarStyle = this.avatarStyleMap[idx];
    },
    setBgColor(color) {
      this.form.customStyle.backgroundType = "color";
      this.form.customStyle.backgroundColor = color;
      this.form.customStyle.backgroundImage = "";
      if (color === "#ffffff" || color === "#f5f7fa" || color === "#e6f0ff" || color === "#f0f9eb" || color === "#fef0f0" || color === "#fdf6ec") {
        this.form.customStyle.textColor = "#222222";
      } else {
        this.form.customStyle.textColor = "#ffffff";
      }
    },
    setTextColor(color) {
      this.form.customStyle.textColor = color;
    },
    setCardStyle(idx) {
      this.form.customStyle.cardStyleIndex = idx;
      switch (idx) {
        case 0:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "color",
            backgroundColor: "#ffffff",
            backgroundImage: "",
            gradientIndex: -1,
            textColor: "#2c5aa0",
            cardStyleIndex: idx
          };
          break;
        case 1:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 0,
            // 蓝色渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 2:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "color",
            backgroundColor: "#f5f7fa",
            backgroundImage: "",
            gradientIndex: -1,
            textColor: "#222222",
            cardStyleIndex: idx
          };
          break;
        case 3:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "color",
            backgroundColor: "#333333",
            backgroundImage: "",
            gradientIndex: -1,
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 4:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 1,
            // 橙色渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 5:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 2,
            // 绿色渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 6:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 3,
            // 粉色渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 7:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 4,
            // 灰色渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#d4af37",
            // 金色文字
            cardStyleIndex: idx
          };
          break;
        case 8:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 5,
            // 紫色渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 9:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "color",
            backgroundColor: "#8b5a2b",
            backgroundImage: "",
            gradientIndex: -1,
            textColor: "#f5deb3",
            cardStyleIndex: idx
          };
          break;
        case 10:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 6,
            // 海洋蓝渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 11:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "solid",
            backgroundColor: "#FF6F61",
            backgroundImage: "",
            gradientIndex: -1,
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 12:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "solid",
            backgroundColor: "#98ff98",
            backgroundImage: "",
            gradientIndex: -1,
            textColor: "#006400",
            cardStyleIndex: idx
          };
          break;
        case 13:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 11,
            // 太空灰渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 14:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 7,
            // 日落橙渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 15:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 8,
            // 翡翠绿渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 16:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "color",
            backgroundColor: "#1e3c72",
            backgroundImage: "",
            gradientIndex: -1,
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
        case 17:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 9,
            // 玫瑰金渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#8b4513",
            cardStyleIndex: idx
          };
          break;
        case 18:
          this.form.customStyle = {
            ...this.form.customStyle,
            backgroundType: "gradient",
            gradientIndex: 10,
            // 星空紫渐变
            backgroundColor: "",
            backgroundImage: "",
            textColor: "#ffffff",
            cardStyleIndex: idx
          };
          break;
      }
    },
    setGradient(idx) {
      this.form.customStyle.backgroundType = "gradient";
      this.form.customStyle.gradientIndex = idx;
      this.form.customStyle.backgroundImage = "";
      this.form.customStyle.backgroundColor = "";
      this.form.customStyle.textColor = "#ffffff";
      this.$forceUpdate();
    },
    setShadow(isShadow) {
      this.form.customStyle.shadow = isShadow;
    },
    setFontSize(idx) {
      this.form.customStyle.fontSizeIndex = idx;
    },
    setAvatarBorder(hasBorder) {
      this.form.customStyle.avatarBorder = hasBorder;
    },
    goBack() {
      common_vendor.index.navigateBack();
    },
    handleScroll() {
    },
    chooseBgImage() {
      this.form.customStyle.backgroundType = "image";
    },
    setBgImage(img) {
      this.form.customStyle.backgroundType = "image";
      this.form.customStyle.backgroundImage = img;
      this.form.customStyle.backgroundColor = "";
      this.form.customStyle.gradientIndex = -1;
      this.form.customStyle.textColor = "#ffffff";
      this.$forceUpdate();
      setTimeout(() => {
        this.$forceUpdate();
      }, 100);
    },
    onRegionChange(e) {
      this.regionValue = e.detail.value;
      this.regionText = this.regionValue.join(" ");
    },
    getLocation() {
      common_vendor.index.getLocation({
        type: "gcj02",
        success: (res) => {
          const latitude = res.latitude;
          const longitude = res.longitude;
          common_vendor.index.request({
            url: `https://apis.map.qq.com/ws/geocoder/v1/?location=${latitude},${longitude}&key=您的腾讯地图KEY`,
            success: (res2) => {
              if (res2.data && res2.data.result) {
                const address = res2.data.result.address;
                const province = res2.data.result.address_component.province;
                const city = res2.data.result.address_component.city;
                const district = res2.data.result.address_component.district;
                this.regionValue = [province, city, district];
                this.regionText = this.regionValue.join(" ");
                this.addressDetail = address.replace(this.regionText, "");
                common_vendor.index.showToast({
                  title: "位置信息已更新",
                  icon: "success"
                });
              }
            },
            fail: () => {
              common_vendor.index.showToast({
                title: "地址解析失败，请重试",
                icon: "none"
              });
            }
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "位置获取失败，请检查定位权限",
            icon: "none"
          });
        }
      });
    },
    confirmAddress() {
      const fullAddress = this.regionText + " " + this.addressDetail;
      this.form.address = fullAddress.trim();
      this.showAddressModal = false;
      common_vendor.index.setStorageSync("userAddress", {
        regionValue: this.regionValue,
        regionText: this.regionText,
        addressDetail: this.addressDetail
      });
    },
    updateContentPadding() {
      if (this.topSectionHeight > 0) {
        const paddingTop = this.statusBarHeight + 45 + this.topSectionHeight;
        const reductionRatio = this.screenHeight < 700 ? 0.25 : 0.2;
        const paddingReduction = this.topSectionHeight * reductionRatio;
        const finalPadding = paddingTop - paddingReduction;
        const query = common_vendor.index.createSelectorQuery();
        query.select(".content-area").boundingClientRect((data) => {
          if (data) {
            this.contentPaddingTop = finalPadding;
          }
        }).exec();
      }
    },
    // 添加地址选择器显示方法
    showAddressSelector() {
      this.showAddressModal = true;
    },
    // 初始化用户数据（优先从服务器加载）
    async initializeUserData() {
      const defaultForm = {
        avatar: "/static/default-avatar.png",
        name: "徐敏",
        position: "销售专员",
        company: "德萨大数据股份有限公司",
        phone: "18596320120",
        email: "www.1099458780.com",
        wechat: "xumin_wx",
        address: "山东省庄市滕州市北辛西路2000号",
        description: "拥有5年销售经验，专注于客户关系维护和业务拓展。热爱学习新技术，善于沟通协调，致力于为客户提供最优质的服务体验。",
        customStyle: {
          backgroundType: "solid",
          gradientIndex: 0,
          backgroundColor: "#ffffff",
          backgroundImage: "",
          textColor: "#333333",
          fontSize: "medium",
          avatarStyle: "rounded",
          borderRadius: "small",
          borderRadiusIndex: 1,
          cardStyleIndex: 0
        }
      };
      this.form = { ...defaultForm };
      if (services_userService.userService.isLoggedIn()) {
        try {
          common_vendor.index.showLoading({
            title: "加载数据中...",
            mask: true
          });
          const result = await services_userInfoService.userInfoService.getCurrentUserInfo();
          if (result.success && result.data) {
            const serverData = result.data;
            this.form = {
              ...defaultForm,
              id: serverData.id,
              name: serverData.name || defaultForm.name,
              phone: serverData.phone || defaultForm.phone,
              email: serverData.email || defaultForm.email,
              wechat: serverData.wechat || defaultForm.wechat,
              position: serverData.position || defaultForm.position,
              company: serverData.company || defaultForm.company,
              address: serverData.address || defaultForm.address,
              description: serverData.description || defaultForm.description,
              avatar: serverData.avatar || defaultForm.avatar,
              // 重要：使用服务器的自定义样式数据
              customStyle: {
                ...defaultForm.customStyle,
                ...serverData.customStyle || {}
              }
            };
            common_vendor.index.setStorageSync("userInfo", this.form);
          } else {
            this.loadFromLocalStorage(defaultForm);
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/card/edit.vue:2190", "从服务器加载数据失败:", error);
          this.loadFromLocalStorage(defaultForm);
        } finally {
          common_vendor.index.hideLoading();
        }
      } else {
        this.loadFromLocalStorage(defaultForm);
      }
    },
    // 从本地存储加载数据
    loadFromLocalStorage(defaultForm) {
      try {
        const saved = common_vendor.index.getStorageSync("userInfo");
        const isValidData = saved && typeof saved === "object" && Object.keys(saved).length > 0;
        if (isValidData) {
          this.form = {
            ...defaultForm,
            ...saved
          };
          if (saved.customStyle) {
            this.form.customStyle = {
              ...defaultForm.customStyle,
              ...saved.customStyle
            };
          } else {
            this.form.customStyle = { ...defaultForm.customStyle };
          }
        } else {
          this.form = { ...defaultForm };
          common_vendor.index.setStorageSync("userInfo", this.form);
        }
      } catch (error) {
        this.form = { ...defaultForm };
        try {
          common_vendor.index.setStorageSync("userInfo", this.form);
        } catch (e) {
        }
      }
    },
    // 读取地址信息
    loadAddressInfo() {
      try {
        const savedAddress = common_vendor.index.getStorageSync("userAddress");
        if (savedAddress) {
          this.regionValue = savedAddress.regionValue || ["", "", ""];
          this.regionText = savedAddress.regionText || "";
          this.addressDetail = savedAddress.addressDetail || "";
        }
      } catch (error) {
      }
    },
    // 获取系统信息
    loadSystemInfo() {
      try {
        const windowInfo = common_vendor.index.getWindowInfo();
        const statusBarHeight = windowInfo.statusBarHeight || 0;
        this.statusBarHeight = statusBarHeight;
        this.screenWidth = windowInfo.windowWidth || 375;
        this.screenHeight = windowInfo.windowHeight || 667;
        this.deviceRatio = windowInfo.pixelRatio || 1;
      } catch (error) {
        this.statusBarHeight = 44;
      }
    },
    // 强制更新视图
    forceUpdateView() {
      this.$nextTick(() => {
        this.$forceUpdate();
        setTimeout(() => {
          this.$forceUpdate();
        }, 300);
      });
    }
  },
  mounted() {
    this.checkMemberStatus();
  },
  beforeDestroy() {
  },
  onReady() {
    setTimeout(() => {
      const query = common_vendor.index.createSelectorQuery();
      query.select(".top-section").boundingClientRect((data) => {
        if (data) {
          this.topSectionHeight = data.height;
          this.$nextTick(() => {
            this.updateContentPadding();
          });
        }
      }).exec();
    }, 50);
  },
  async onLoad() {
    this.checkMemberStatus();
    this.loadSystemInfo();
    this.loadAddressInfo();
    await this.initializeUserData();
    this.forceUpdateView();
  },
  onShow() {
    this.syncMemberStatus();
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.s($options.navbarStyle),
    c: $data.form.customStyle.backgroundType === "image" && $data.form.customStyle.backgroundImage
  }, $data.form.customStyle.backgroundType === "image" && $data.form.customStyle.backgroundImage ? {} : {}, {
    d: $data.form.customStyle.backgroundType === "image" && $data.form.customStyle.backgroundImage
  }, $data.form.customStyle.backgroundType === "image" && $data.form.customStyle.backgroundImage ? {
    e: $data.form.customStyle.backgroundImage
  } : {}, {
    f: common_vendor.t($data.form.name),
    g: $data.form.customStyle.textColor,
    h: common_vendor.t($data.form.position),
    i: common_vendor.s($options.positionStyle),
    j: common_vendor.t($data.form.company),
    k: $data.form.customStyle.textColor,
    l: $options.getDisplayAvatar($data.form.avatar),
    m: common_assets._imports_0$1,
    n: $data.form.customStyle.textColor === "#ffffff" ? "rgba(255,255,255,0.2)" : "#f0f0f0",
    o: common_assets._imports_1,
    p: common_vendor.s($options.iconStyle),
    q: $data.form.customStyle.textColor === "#ffffff" ? "rgba(255,255,255,0.7)" : "#888",
    r: common_vendor.t($data.form.phone),
    s: $data.form.customStyle.textColor,
    t: common_assets._imports_2,
    v: common_vendor.s($options.iconStyle),
    w: $data.form.customStyle.textColor === "#ffffff" ? "rgba(255,255,255,0.7)" : "#888",
    x: common_vendor.t($data.form.wechat),
    y: $data.form.customStyle.textColor,
    z: common_assets._imports_3,
    A: common_vendor.s($options.iconStyle),
    B: $data.form.customStyle.textColor === "#ffffff" ? "rgba(255,255,255,0.7)" : "#888",
    C: common_vendor.t($data.form.email),
    D: $data.form.customStyle.textColor,
    E: common_assets._imports_4,
    F: common_vendor.s($options.iconStyle),
    G: $data.form.customStyle.textColor === "#ffffff" ? "rgba(255,255,255,0.7)" : "#888",
    H: common_vendor.t($data.form.address),
    I: $data.form.customStyle.textColor,
    J: $data.form.customStyle.backgroundType === "image" && $data.form.customStyle.backgroundImage ? 1 : "",
    K: common_vendor.s(Object.assign($options.cardPreviewMargin, $options.cardPreviewStyle)),
    L: common_vendor.n({
      active: $data.activeTab === 0
    }),
    M: common_vendor.o(($event) => $data.activeTab = 0),
    N: common_vendor.n({
      active: $data.activeTab === 1
    }),
    O: common_vendor.o(($event) => $data.activeTab = 1),
    P: `0 ${$options.adaptiveSpacing.sm}px`,
    Q: $data.statusBarHeight + 45 + "px",
    R: $options.getDisplayAvatar($data.form.avatar),
    S: common_vendor.o((...args) => $options.onChooseAvatar && $options.onChooseAvatar(...args)),
    T: $data.form.name,
    U: common_vendor.o(($event) => $data.form.name = $event.detail.value),
    V: $data.form.position,
    W: common_vendor.o(($event) => $data.form.position = $event.detail.value),
    X: $data.form.company,
    Y: common_vendor.o(($event) => $data.form.company = $event.detail.value),
    Z: common_assets._imports_1,
    aa: common_vendor.o([($event) => $data.form.phone = $event.detail.value, (...args) => $options.onPhoneInput && $options.onPhoneInput(...args)]),
    ab: $data.form.phone,
    ac: $data.form.phone
  }, $data.form.phone ? {
    ad: common_vendor.o((...args) => $options.clearPhone && $options.clearPhone(...args))
  } : {}, {
    ae: $data.phoneError
  }, $data.phoneError ? {
    af: common_vendor.t($data.phoneError)
  } : {}, {
    ag: common_assets._imports_2,
    ah: $data.form.wechat,
    ai: common_vendor.o(($event) => $data.form.wechat = $event.detail.value),
    aj: $data.form.wechat
  }, $data.form.wechat ? {
    ak: common_vendor.o((...args) => $options.clearWechat && $options.clearWechat(...args))
  } : {}, {
    al: common_assets._imports_3,
    am: common_vendor.o((...args) => $options.validateEmail && $options.validateEmail(...args)),
    an: $data.form.email,
    ao: common_vendor.o(($event) => $data.form.email = $event.detail.value),
    ap: $data.form.email
  }, $data.form.email ? {
    aq: common_vendor.o((...args) => $options.clearEmail && $options.clearEmail(...args))
  } : {}, {
    ar: $data.emailError
  }, $data.emailError ? {
    as: common_vendor.t($data.emailError)
  } : {}, {
    at: common_assets._imports_4,
    av: common_vendor.t($data.form.address || "请选择地址"),
    aw: common_vendor.o((...args) => $options.showAddressSelector && $options.showAddressSelector(...args)),
    ax: $data.activeTab === 0,
    ay: `${$options.adaptiveSpacing.xs}px ${$options.adaptiveSpacing.sm}px ${$options.adaptiveSpacing.md}px`,
    az: common_vendor.f($data.cardStyles, (item, idx, i0) => {
      return {
        a: common_vendor.s(item.previewStyle),
        b: common_vendor.t(item.name),
        c: item.name,
        d: common_vendor.n({
          active: $data.form.customStyle.cardStyleIndex === idx
        }),
        e: common_vendor.o(($event) => $options.applyCardStyle(idx), item.name)
      };
    }),
    aA: common_vendor.f($data.bgColors, (color, idx, i0) => {
      return {
        a: color,
        b: color,
        c: common_vendor.n({
          active: $data.form.customStyle.backgroundType === "color" && $data.form.customStyle.backgroundColor === color
        }),
        d: common_vendor.o(($event) => $options.applyBgColor(color), color)
      };
    }),
    aB: common_vendor.f($data.gradients, (gradient, idx, i0) => {
      return {
        a: idx,
        b: gradient,
        c: common_vendor.n({
          active: $data.form.customStyle.backgroundType === "gradient" && $data.form.customStyle.gradientIndex === idx
        }),
        d: common_vendor.o(($event) => $options.applyGradient(idx), idx)
      };
    }),
    aC: common_vendor.o((...args) => $options.uploadBgImage && $options.uploadBgImage(...args)),
    aD: common_vendor.f($data.bgImages, (img, idx, i0) => {
      return {
        a: img,
        b: img,
        c: common_vendor.n({
          active: $data.form.customStyle.backgroundType === "image" && $data.form.customStyle.backgroundImage === img
        }),
        d: common_vendor.o(($event) => $options.applyBgImage(img), img)
      };
    }),
    aE: common_vendor.f($data.textColors, (color, idx, i0) => {
      return common_vendor.e({
        a: color === "#ffffff"
      }, color === "#ffffff" ? {} : {}, {
        b: color,
        c: color,
        d: common_vendor.n({
          active: $data.form.customStyle.textColor === color
        }),
        e: common_vendor.o(($event) => $options.applyTextColor(color), color)
      });
    }),
    aF: $data.activeTab === 1,
    aG: `${$options.adaptiveSpacing.xs}px ${$options.adaptiveSpacing.sm}px`,
    aH: common_vendor.s($options.contentStyle),
    aI: common_vendor.t($data.activeTab === 0 ? "保存联系信息" : "保存个性定制"),
    aJ: $data.showSaveBtn ? 1 : "",
    aK: common_vendor.o((...args) => $options.saveCard && $options.saveCard(...args)),
    aL: $data.showAddressModal
  }, $data.showAddressModal ? common_vendor.e({
    aM: common_vendor.o(($event) => $data.showAddressModal = false),
    aN: common_vendor.o(($event) => $data.showAddressModal = false),
    aO: common_vendor.n({
      active: $data.addressTab === 0
    }),
    aP: common_vendor.o(($event) => $data.addressTab = 0),
    aQ: common_vendor.n({
      active: $data.addressTab === 1
    }),
    aR: common_vendor.o(($event) => $data.addressTab = 1),
    aS: $data.addressTab === 0
  }, $data.addressTab === 0 ? {
    aT: common_vendor.t($data.regionText || "选择省/市/区"),
    aU: common_vendor.o((...args) => $options.onRegionChange && $options.onRegionChange(...args)),
    aV: $data.regionValue,
    aW: $data.addressDetail,
    aX: common_vendor.o(($event) => $data.addressDetail = $event.detail.value)
  } : {}, {
    aY: $data.addressTab === 1
  }, $data.addressTab === 1 ? {
    aZ: common_assets._imports_4,
    ba: common_vendor.o((...args) => $options.getLocation && $options.getLocation(...args))
  } : {}, {
    bb: common_vendor.o(($event) => $data.showAddressModal = false),
    bc: common_vendor.o((...args) => $options.confirmAddress && $options.confirmAddress(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-a9a206dd"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/card/edit.js.map
